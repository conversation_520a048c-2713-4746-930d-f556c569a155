{"ast": null, "code": "import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInWeek } from \"./constants.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\n\n/**\n * The {@link differenceInCalendarISOWeeks} function options.\n */\n\n/**\n * @name differenceInCalendarISOWeeks\n * @category ISO Week Helpers\n * @summary Get the number of calendar ISO weeks between the given dates.\n *\n * @description\n * Get the number of calendar ISO weeks between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar ISO weeks\n *\n * @example\n * // How many calendar ISO weeks are between 6 July 2014 and 21 July 2014?\n * const result = differenceInCalendarISOWeeks(\n *   new Date(2014, 6, 21),\n *   new Date(2014, 6, 6),\n * );\n * //=> 3\n */\nexport function differenceInCalendarISOWeeks(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  const startOfISOWeekLeft = startOfISOWeek(laterDate_);\n  const startOfISOWeekRight = startOfISOWeek(earlierDate_);\n  const timestampLeft = +startOfISOWeekLeft - getTimezoneOffsetInMilliseconds(startOfISOWeekLeft);\n  const timestampRight = +startOfISOWeekRight - getTimezoneOffsetInMilliseconds(startOfISOWeekRight);\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round((timestampLeft - timestampRight) / millisecondsInWeek);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarISOWeeks;", "map": {"version": 3, "names": ["getTimezoneOffsetInMilliseconds", "normalizeDates", "millisecondsInWeek", "startOfISOWeek", "differenceInCalendarISOWeeks", "laterDate", "earlierDate", "options", "laterDate_", "earlierDate_", "in", "startOfISOWeekLeft", "startOfISOWeekRight", "timestampLeft", "timestampRight", "Math", "round"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/differenceInCalendarISOWeeks.js"], "sourcesContent": ["import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInWeek } from \"./constants.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\n\n/**\n * The {@link differenceInCalendarISOWeeks} function options.\n */\n\n/**\n * @name differenceInCalendarISOWeeks\n * @category ISO Week Helpers\n * @summary Get the number of calendar ISO weeks between the given dates.\n *\n * @description\n * Get the number of calendar ISO weeks between the given dates.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar ISO weeks\n *\n * @example\n * // How many calendar ISO weeks are between 6 July 2014 and 21 July 2014?\n * const result = differenceInCalendarISOWeeks(\n *   new Date(2014, 6, 21),\n *   new Date(2014, 6, 6),\n * );\n * //=> 3\n */\nexport function differenceInCalendarISOWeeks(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const startOfISOWeekLeft = startOfISOWeek(laterDate_);\n  const startOfISOWeekRight = startOfISOWeek(earlierDate_);\n\n  const timestampLeft =\n    +startOfISOWeekLeft - getTimezoneOffsetInMilliseconds(startOfISOWeekLeft);\n  const timestampRight =\n    +startOfISOWeekRight - getTimezoneOffsetInMilliseconds(startOfISOWeekRight);\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round((timestampLeft - timestampRight) / millisecondsInWeek);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarISOWeeks;\n"], "mappings": "AAAA,SAASA,+BAA+B,QAAQ,2CAA2C;AAC3F,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAASC,cAAc,QAAQ,qBAAqB;;AAEpD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,4BAA4BA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAC5E,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGR,cAAc,CAC/CM,OAAO,EAAEG,EAAE,EACXL,SAAS,EACTC,WACF,CAAC;EAED,MAAMK,kBAAkB,GAAGR,cAAc,CAACK,UAAU,CAAC;EACrD,MAAMI,mBAAmB,GAAGT,cAAc,CAACM,YAAY,CAAC;EAExD,MAAMI,aAAa,GACjB,CAACF,kBAAkB,GAAGX,+BAA+B,CAACW,kBAAkB,CAAC;EAC3E,MAAMG,cAAc,GAClB,CAACF,mBAAmB,GAAGZ,+BAA+B,CAACY,mBAAmB,CAAC;;EAE7E;EACA;EACA;EACA,OAAOG,IAAI,CAACC,KAAK,CAAC,CAACH,aAAa,GAAGC,cAAc,IAAIZ,kBAAkB,CAAC;AAC1E;;AAEA;AACA,eAAeE,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}