{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfSecond} function options.\n */\n\n/**\n * @name startOfSecond\n * @category Second Helpers\n * @summary Return the start of a second for the given date.\n *\n * @description\n * Return the start of a second for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a second\n *\n * @example\n * // The start of a second for 1 December 2014 22:15:45.400:\n * const result = startOfSecond(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:45.000\n */\nexport function startOfSecond(date, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setMilliseconds(0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default startOfSecond;", "map": {"version": 3, "names": ["toDate", "startOfSecond", "date", "options", "date_", "in", "setMilliseconds"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/startOfSecond.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfSecond} function options.\n */\n\n/**\n * @name startOfSecond\n * @category Second Helpers\n * @summary Return the start of a second for the given date.\n *\n * @description\n * Return the start of a second for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a second\n *\n * @example\n * // The start of a second for 1 December 2014 22:15:45.400:\n * const result = startOfSecond(new Date(2014, 11, 1, 22, 15, 45, 400))\n * //=> Mon Dec 01 2014 22:15:45.000\n */\nexport function startOfSecond(date, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setMilliseconds(0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default startOfSecond;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC3C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvCD,KAAK,CAACE,eAAe,CAAC,CAAC,CAAC;EACxB,OAAOF,KAAK;AACd;;AAEA;AACA,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}