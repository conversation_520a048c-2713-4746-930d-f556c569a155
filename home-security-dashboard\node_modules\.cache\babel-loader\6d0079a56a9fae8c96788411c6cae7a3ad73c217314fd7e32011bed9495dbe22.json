{"ast": null, "code": "import { constructNow } from \"./constructNow.js\";\nimport { isSameHour } from \"./isSameHour.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isThisHour} function options.\n */\n\n/**\n * @name isThisHour\n * @category Hour Helpers\n * @summary Is the given date in the same hour as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same hour as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this hour\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:00:00 in this hour?\n * const result = isThisHour(new Date(2014, 8, 25, 18))\n * //=> true\n */\nexport function isThisHour(date, options) {\n  return isSameHour(toDate(date, options?.in), constructNow(options?.in || date));\n}\n\n// Fallback for modularized imports:\nexport default isThisHour;", "map": {"version": 3, "names": ["constructNow", "isSameHour", "toDate", "isThisHour", "date", "options", "in"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/isThisHour.js"], "sourcesContent": ["import { constructNow } from \"./constructNow.js\";\nimport { isSameHour } from \"./isSameHour.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isThisHour} function options.\n */\n\n/**\n * @name isThisHour\n * @category Hour Helpers\n * @summary Is the given date in the same hour as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same hour as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this hour\n *\n * @example\n * // If now is 25 September 2014 18:30:15.500,\n * // is 25 September 2014 18:00:00 in this hour?\n * const result = isThisHour(new Date(2014, 8, 25, 18))\n * //=> true\n */\nexport function isThisHour(date, options) {\n  return isSameHour(\n    toDate(date, options?.in),\n    constructNow(options?.in || date),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isThisHour;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;AAChD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,OAAOJ,UAAU,CACfC,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC,EACzBN,YAAY,CAACK,OAAO,EAAEC,EAAE,IAAIF,IAAI,CAClC,CAAC;AACH;;AAEA;AACA,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}