{"ast": null, "code": "import d, { createContext as c, useContext as m } from \"react\";\nimport { useEvent as p } from '../hooks/use-event.js';\nimport { useIsoMorphicEffect as f } from '../hooks/use-iso-morphic-effect.js';\nlet a = c(() => {});\na.displayName = \"StackContext\";\nvar s = (e => (e[e.Add = 0] = \"Add\", e[e.Remove = 1] = \"Remove\", e))(s || {});\nfunction x() {\n  return m(a);\n}\nfunction b({\n  children: i,\n  onUpdate: r,\n  type: e,\n  element: n,\n  enabled: u\n}) {\n  let l = x(),\n    o = p((...t) => {\n      r == null || r(...t), l(...t);\n    });\n  return f(() => {\n    let t = u === void 0 || u === !0;\n    return t && o(0, e, n), () => {\n      t && o(1, e, n);\n    };\n  }, [o, e, n, u]), d.createElement(a.Provider, {\n    value: o\n  }, i);\n}\nexport { s as StackMessage, b as StackProvider, x as useStackContext };", "map": {"version": 3, "names": ["d", "createContext", "c", "useContext", "m", "useEvent", "p", "useIsoMorphicEffect", "f", "a", "displayName", "s", "e", "Add", "Remove", "x", "b", "children", "i", "onUpdate", "r", "type", "element", "n", "enabled", "u", "l", "o", "t", "createElement", "Provider", "value", "StackMessage", "Stack<PERSON><PERSON><PERSON>", "useStackContext"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/@headlessui/react/dist/internal/stack-context.js"], "sourcesContent": ["import d,{createContext as c,useContext as m}from\"react\";import{useEvent as p}from'../hooks/use-event.js';import{useIsoMorphicEffect as f}from'../hooks/use-iso-morphic-effect.js';let a=c(()=>{});a.displayName=\"StackContext\";var s=(e=>(e[e.Add=0]=\"Add\",e[e.Remove=1]=\"Remove\",e))(s||{});function x(){return m(a)}function b({children:i,onUpdate:r,type:e,element:n,enabled:u}){let l=x(),o=p((...t)=>{r==null||r(...t),l(...t)});return f(()=>{let t=u===void 0||u===!0;return t&&o(0,e,n),()=>{t&&o(1,e,n)}},[o,e,n,u]),d.createElement(a.Provider,{value:o},i)}export{s as StackMessage,b as StackProvider,x as useStackContext};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,oCAAoC;AAAC,IAAIC,CAAC,GAACP,CAAC,CAAC,MAAI,CAAC,CAAC,CAAC;AAACO,CAAC,CAACC,WAAW,GAAC,cAAc;AAAC,IAAIC,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,GAAG,GAAC,CAAC,CAAC,GAAC,KAAK,EAACD,CAAC,CAACA,CAAC,CAACE,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACF,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,OAAOX,CAAC,CAACK,CAAC,CAAC;AAAA;AAAC,SAASO,CAACA,CAAC;EAACC,QAAQ,EAACC,CAAC;EAACC,QAAQ,EAACC,CAAC;EAACC,IAAI,EAACT,CAAC;EAACU,OAAO,EAACC,CAAC;EAACC,OAAO,EAACC;AAAC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACX,CAAC,CAAC,CAAC;IAACY,CAAC,GAACrB,CAAC,CAAC,CAAC,GAAGsB,CAAC,KAAG;MAACR,CAAC,IAAE,IAAI,IAAEA,CAAC,CAAC,GAAGQ,CAAC,CAAC,EAACF,CAAC,CAAC,GAAGE,CAAC,CAAC;IAAA,CAAC,CAAC;EAAC,OAAOpB,CAAC,CAAC,MAAI;IAAC,IAAIoB,CAAC,GAACH,CAAC,KAAG,KAAK,CAAC,IAAEA,CAAC,KAAG,CAAC,CAAC;IAAC,OAAOG,CAAC,IAAED,CAAC,CAAC,CAAC,EAACf,CAAC,EAACW,CAAC,CAAC,EAAC,MAAI;MAACK,CAAC,IAAED,CAAC,CAAC,CAAC,EAACf,CAAC,EAACW,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC,EAAC,CAACI,CAAC,EAACf,CAAC,EAACW,CAAC,EAACE,CAAC,CAAC,CAAC,EAACzB,CAAC,CAAC6B,aAAa,CAACpB,CAAC,CAACqB,QAAQ,EAAC;IAACC,KAAK,EAACJ;EAAC,CAAC,EAACT,CAAC,CAAC;AAAA;AAAC,SAAOP,CAAC,IAAIqB,YAAY,EAAChB,CAAC,IAAIiB,aAAa,EAAClB,CAAC,IAAImB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}