{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\Resonance-KLE\\\\home-security-dashboard\\\\src\\\\components\\\\shared\\\\Card.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Card = ({\n  title,\n  icon,\n  actionButton,\n  children,\n  className = ''\n}) => {\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    className: `bg-gray-900 rounded-xl p-6 shadow-lg ${className}`,\n    children: [(title || icon || actionButton) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [icon && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-400\",\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 34\n        }, this), title && /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-white\",\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 35\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 21\n      }, this), actionButton && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: actionButton\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 38\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 17\n    }, this), children]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 9\n  }, this);\n};\n_c = Card;\nvar _c;\n$RefreshReg$(_c, \"Card\");", "map": {"version": 3, "names": ["React", "motion", "jsxDEV", "_jsxDEV", "Card", "title", "icon", "actionButton", "children", "className", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/src/components/shared/Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n    title?: string;\n    icon?: React.ReactNode;\n    actionButton?: React.ReactNode;\n    children?: React.ReactNode;\n    className?: string;\n}\n\nexport const Card: React.FC<CardProps> = ({\n    title,\n    icon,\n    actionButton,\n    children,\n    className = ''\n}) => {\n    return (\n        <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            className={`bg-gray-900 rounded-xl p-6 shadow-lg ${className}`}\n        >\n            {(title || icon || actionButton) && (\n                <div className=\"flex items-center justify-between mb-6\">\n                    <div className=\"flex items-center space-x-3\">\n                        {icon && <span className=\"text-gray-400\">{icon}</span>}\n                        {title && <h2 className=\"text-xl font-semibold text-white\">{title}</h2>}\n                    </div>\n                    {actionButton && <div>{actionButton}</div>}\n                </div>\n            )}\n            {children}\n        </motion.div>\n    );\n};"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUvC,OAAO,MAAMC,IAAyB,GAAGA,CAAC;EACtCC,KAAK;EACLC,IAAI;EACJC,YAAY;EACZC,QAAQ;EACRC,SAAS,GAAG;AAChB,CAAC,KAAK;EACF,oBACIN,OAAA,CAACF,MAAM,CAACS,GAAG;IACPC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAE;IAC/BC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BJ,SAAS,EAAE,wCAAwCA,SAAS,EAAG;IAAAD,QAAA,GAE9D,CAACH,KAAK,IAAIC,IAAI,IAAIC,YAAY,kBAC3BJ,OAAA;MAAKM,SAAS,EAAC,wCAAwC;MAAAD,QAAA,gBACnDL,OAAA;QAAKM,SAAS,EAAC,6BAA6B;QAAAD,QAAA,GACvCF,IAAI,iBAAIH,OAAA;UAAMM,SAAS,EAAC,eAAe;UAAAD,QAAA,EAAEF;QAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACrDb,KAAK,iBAAIF,OAAA;UAAIM,SAAS,EAAC,kCAAkC;UAAAD,QAAA,EAAEH;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,EACLX,YAAY,iBAAIJ,OAAA;QAAAK,QAAA,EAAMD;MAAY;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CACR,EACAV,QAAQ;EAAA;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAErB,CAAC;AAACC,EAAA,GAzBWf,IAAyB;AAAA,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}