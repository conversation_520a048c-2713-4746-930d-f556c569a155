{"ast": null, "code": "import * as React from \"react\";\nimport { flushSync } from \"react-dom\";\nimport { Virtualizer, elementScroll, observeElementOffset, observeElementRect, windowScroll, observeWindowOffset, observeWindowRect } from \"@tanstack/virtual-core\";\nexport * from \"@tanstack/virtual-core\";\nconst useIsomorphicLayoutEffect = typeof document !== \"undefined\" ? React.useLayoutEffect : React.useEffect;\nfunction useVirtualizerBase(options) {\n  const rerender = React.useReducer(() => ({}), {})[1];\n  const resolvedOptions = {\n    ...options,\n    onChange: (instance2, sync) => {\n      var _a;\n      if (sync) {\n        flushSync(rerender);\n      } else {\n        rerender();\n      }\n      (_a = options.onChange) == null ? void 0 : _a.call(options, instance2, sync);\n    }\n  };\n  const [instance] = React.useState(() => new Virtualizer(resolvedOptions));\n  instance.setOptions(resolvedOptions);\n  useIsomorphicLayoutEffect(() => {\n    return instance._didMount();\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    return instance._willUpdate();\n  });\n  return instance;\n}\nfunction useVirtualizer(options) {\n  return useVirtualizerBase({\n    observeElementRect,\n    observeElementOffset,\n    scrollToFn: elementScroll,\n    ...options\n  });\n}\nfunction useWindowVirtualizer(options) {\n  return useVirtualizerBase({\n    getScrollElement: () => typeof document !== \"undefined\" ? window : null,\n    observeElementRect: observeWindowRect,\n    observeElementOffset: observeWindowOffset,\n    scrollToFn: windowScroll,\n    initialOffset: () => typeof document !== \"undefined\" ? window.scrollY : 0,\n    ...options\n  });\n}\nexport { useVirtualizer, useWindowVirtualizer };", "map": {"version": 3, "names": ["useIsomorphicLayoutEffect", "document", "React", "useLayoutEffect", "useEffect", "useVirtualizerBase", "options", "rerender", "useReducer", "resolvedOptions", "onChange", "instance2", "sync", "flushSync", "_a", "call", "instance", "useState", "Virtualizer", "setOptions", "_didMount", "_willUpdate", "useVirtualizer", "observeElementRect", "observeElementOffset", "scrollToFn", "elementScroll", "useWindowVirtualizer", "getScrollElement", "window", "observeWindowRect", "observeWindowOffset", "windowScroll", "initialOffset", "scrollY"], "sources": ["E:\\code\\Resonance-KLE\\home-security-dashboard\\node_modules\\@tanstack\\react-virtual\\src\\index.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { flushSync } from 'react-dom'\nimport {\n  Virtualizer,\n  elementScroll,\n  observeElementOffset,\n  observeElementRect,\n  observeWindowOffset,\n  observeWindowRect,\n  windowScroll,\n} from '@tanstack/virtual-core'\nimport type { PartialKeys, VirtualizerOptions } from '@tanstack/virtual-core'\n\nexport * from '@tanstack/virtual-core'\n\nconst useIsomorphicLayoutEffect =\n  typeof document !== 'undefined' ? React.useLayoutEffect : React.useEffect\n\nfunction useVirtualizerBase<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n>(\n  options: VirtualizerOptions<TScrollElement, TItemElement>,\n): Virtualizer<TScrollElement, TItemElement> {\n  const rerender = React.useReducer(() => ({}), {})[1]\n\n  const resolvedOptions: VirtualizerOptions<TScrollElement, TItemElement> = {\n    ...options,\n    onChange: (instance, sync) => {\n      if (sync) {\n        flushSync(rerender)\n      } else {\n        rerender()\n      }\n      options.onChange?.(instance, sync)\n    },\n  }\n\n  const [instance] = React.useState(\n    () => new Virtualizer<TScrollElement, TItemElement>(resolvedOptions),\n  )\n\n  instance.setOptions(resolvedOptions)\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._didMount()\n  }, [])\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._willUpdate()\n  })\n\n  return instance\n}\n\nexport function useVirtualizer<\n  TScrollElement extends Element,\n  TItemElement extends Element,\n>(\n  options: PartialKeys<\n    VirtualizerOptions<TScrollElement, TItemElement>,\n    'observeElementRect' | 'observeElementOffset' | 'scrollToFn'\n  >,\n): Virtualizer<TScrollElement, TItemElement> {\n  return useVirtualizerBase<TScrollElement, TItemElement>({\n    observeElementRect: observeElementRect,\n    observeElementOffset: observeElementOffset,\n    scrollToFn: elementScroll,\n    ...options,\n  })\n}\n\nexport function useWindowVirtualizer<TItemElement extends Element>(\n  options: PartialKeys<\n    VirtualizerOptions<Window, TItemElement>,\n    | 'getScrollElement'\n    | 'observeElementRect'\n    | 'observeElementOffset'\n    | 'scrollToFn'\n  >,\n): Virtualizer<Window, TItemElement> {\n  return useVirtualizerBase<Window, TItemElement>({\n    getScrollElement: () => (typeof document !== 'undefined' ? window : null),\n    observeElementRect: observeWindowRect,\n    observeElementOffset: observeWindowOffset,\n    scrollToFn: windowScroll,\n    initialOffset: () => (typeof document !== 'undefined' ? window.scrollY : 0),\n    ...options,\n  })\n}\n"], "mappings": ";;;;AAeA,MAAMA,yBAAA,GACJ,OAAOC,QAAA,KAAa,cAAcC,KAAA,CAAMC,eAAA,GAAkBD,KAAA,CAAME,SAAA;AAElE,SAASC,mBAIPC,OAAA,EAC2C;EACrC,MAAAC,QAAA,GAAWL,KAAA,CAAMM,UAAA,CAAW,OAAO,KAAK,EAAE,EAAE,CAAC;EAEnD,MAAMC,eAAA,GAAoE;IACxE,GAAGH,OAAA;IACHI,QAAA,EAAUA,CAACC,SAAA,EAAUC,IAAA,KAAS;;MAC5B,IAAIA,IAAA,EAAM;QACRC,SAAA,CAAUN,QAAQ;MAAA,OACb;QACIA,QAAA;MAAA;MAEH,CAAAO,EAAA,GAAAR,OAAA,CAAAI,QAAA,qBAAAI,EAAA,CAAAC,IAAA,CAAAT,OAAA,EAAWK,SAAA,EAAUC,IAAA;IAAI;EAErC;EAEM,OAACI,QAAQ,IAAId,KAAA,CAAMe,QAAA,CACvB,MAAM,IAAIC,WAAA,CAA0CT,eAAe,CACrE;EAEAO,QAAA,CAASG,UAAA,CAAWV,eAAe;EAEnCT,yBAAA,CAA0B,MAAM;IAC9B,OAAOgB,QAAA,CAASI,SAAA,CAAU;EAC5B,GAAG,EAAE;EAELpB,yBAAA,CAA0B,MAAM;IAC9B,OAAOgB,QAAA,CAASK,WAAA,CAAY;EAAA,CAC7B;EAEM,OAAAL,QAAA;AACT;AAEO,SAASM,eAIdhB,OAAA,EAI2C;EAC3C,OAAOD,kBAAA,CAAiD;IACtDkB,kBAAA;IACAC,oBAAA;IACAC,UAAA,EAAYC,aAAA;IACZ,GAAGpB;EAAA,CACJ;AACH;AAEO,SAASqB,qBACdrB,OAAA,EAOmC;EACnC,OAAOD,kBAAA,CAAyC;IAC9CuB,gBAAA,EAAkBA,CAAA,KAAO,OAAO3B,QAAA,KAAa,cAAc4B,MAAA,GAAS;IACpEN,kBAAA,EAAoBO,iBAAA;IACpBN,oBAAA,EAAsBO,mBAAA;IACtBN,UAAA,EAAYO,YAAA;IACZC,aAAA,EAAeA,CAAA,KAAO,OAAOhC,QAAA,KAAa,cAAc4B,MAAA,CAAOK,OAAA,GAAU;IACzE,GAAG5B;EAAA,CACJ;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}