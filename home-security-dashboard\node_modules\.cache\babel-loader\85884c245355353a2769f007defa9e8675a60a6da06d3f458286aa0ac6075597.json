{"ast": null, "code": "const namedEdges = {\n  start: 0,\n  center: 0.5,\n  end: 1\n};\nfunction resolveEdge(edge, length, inset = 0) {\n  let delta = 0;\n  /**\n   * If we have this edge defined as a preset, replace the definition\n   * with the numerical value.\n   */\n  if (namedEdges[edge] !== undefined) {\n    edge = namedEdges[edge];\n  }\n  /**\n   * Handle unit values\n   */\n  if (typeof edge === \"string\") {\n    const asNumber = parseFloat(edge);\n    if (edge.endsWith(\"px\")) {\n      delta = asNumber;\n    } else if (edge.endsWith(\"%\")) {\n      edge = asNumber / 100;\n    } else if (edge.endsWith(\"vw\")) {\n      delta = asNumber / 100 * document.documentElement.clientWidth;\n    } else if (edge.endsWith(\"vh\")) {\n      delta = asNumber / 100 * document.documentElement.clientHeight;\n    } else {\n      edge = asNumber;\n    }\n  }\n  /**\n   * If the edge is defined as a number, handle as a progress value.\n   */\n  if (typeof edge === \"number\") {\n    delta = length * edge;\n  }\n  return inset + delta;\n}\nexport { namedEdges, resolveEdge };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "start", "center", "end", "resolveEdge", "edge", "length", "inset", "delta", "undefined", "asNumber", "parseFloat", "endsWith", "document", "documentElement", "clientWidth", "clientHeight"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/edge.mjs"], "sourcesContent": ["const namedEdges = {\n    start: 0,\n    center: 0.5,\n    end: 1,\n};\nfunction resolveEdge(edge, length, inset = 0) {\n    let delta = 0;\n    /**\n     * If we have this edge defined as a preset, replace the definition\n     * with the numerical value.\n     */\n    if (namedEdges[edge] !== undefined) {\n        edge = namedEdges[edge];\n    }\n    /**\n     * Handle unit values\n     */\n    if (typeof edge === \"string\") {\n        const asNumber = parseFloat(edge);\n        if (edge.endsWith(\"px\")) {\n            delta = asNumber;\n        }\n        else if (edge.endsWith(\"%\")) {\n            edge = asNumber / 100;\n        }\n        else if (edge.endsWith(\"vw\")) {\n            delta = (asNumber / 100) * document.documentElement.clientWidth;\n        }\n        else if (edge.endsWith(\"vh\")) {\n            delta = (asNumber / 100) * document.documentElement.clientHeight;\n        }\n        else {\n            edge = asNumber;\n        }\n    }\n    /**\n     * If the edge is defined as a number, handle as a progress value.\n     */\n    if (typeof edge === \"number\") {\n        delta = length * edge;\n    }\n    return inset + delta;\n}\n\nexport { namedEdges, resolveEdge };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG;EACfC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,GAAG;EACXC,GAAG,EAAE;AACT,CAAC;AACD,SAASC,WAAWA,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,GAAG,CAAC,EAAE;EAC1C,IAAIC,KAAK,GAAG,CAAC;EACb;AACJ;AACA;AACA;EACI,IAAIR,UAAU,CAACK,IAAI,CAAC,KAAKI,SAAS,EAAE;IAChCJ,IAAI,GAAGL,UAAU,CAACK,IAAI,CAAC;EAC3B;EACA;AACJ;AACA;EACI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,MAAMK,QAAQ,GAAGC,UAAU,CAACN,IAAI,CAAC;IACjC,IAAIA,IAAI,CAACO,QAAQ,CAAC,IAAI,CAAC,EAAE;MACrBJ,KAAK,GAAGE,QAAQ;IACpB,CAAC,MACI,IAAIL,IAAI,CAACO,QAAQ,CAAC,GAAG,CAAC,EAAE;MACzBP,IAAI,GAAGK,QAAQ,GAAG,GAAG;IACzB,CAAC,MACI,IAAIL,IAAI,CAACO,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC1BJ,KAAK,GAAIE,QAAQ,GAAG,GAAG,GAAIG,QAAQ,CAACC,eAAe,CAACC,WAAW;IACnE,CAAC,MACI,IAAIV,IAAI,CAACO,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC1BJ,KAAK,GAAIE,QAAQ,GAAG,GAAG,GAAIG,QAAQ,CAACC,eAAe,CAACE,YAAY;IACpE,CAAC,MACI;MACDX,IAAI,GAAGK,QAAQ;IACnB;EACJ;EACA;AACJ;AACA;EACI,IAAI,OAAOL,IAAI,KAAK,QAAQ,EAAE;IAC1BG,KAAK,GAAGF,MAAM,GAAGD,IAAI;EACzB;EACA,OAAOE,KAAK,GAAGC,KAAK;AACxB;AAEA,SAASR,UAAU,EAAEI,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}