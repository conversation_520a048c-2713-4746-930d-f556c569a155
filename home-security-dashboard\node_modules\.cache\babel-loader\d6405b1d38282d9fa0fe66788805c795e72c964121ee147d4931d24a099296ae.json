{"ast": null, "code": "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\nexport class SecondParser extends Parser {\n  priority = 50;\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"s\":\n        return parseNumericPattern(numericPatterns.second, dateString);\n      case \"so\":\n        return match.ordinalNumber(dateString, {\n          unit: \"second\"\n        });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n  validate(_date, value) {\n    return value >= 0 && value <= 59;\n  }\n  set(date, _flags, value) {\n    date.setSeconds(value, 0);\n    return date;\n  }\n  incompatibleTokens = [\"t\", \"T\"];\n}", "map": {"version": 3, "names": ["numericPatterns", "<PERSON><PERSON><PERSON>", "parseNDigits", "parseNumericPattern", "Second<PERSON><PERSON><PERSON>", "priority", "parse", "dateString", "token", "match", "second", "ordinalNumber", "unit", "length", "validate", "_date", "value", "set", "date", "_flags", "setSeconds", "incompatibleTokens"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/parse/_lib/parsers/SecondParser.js"], "sourcesContent": ["import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class SecondParser extends Parser {\n  priority = 50;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"s\":\n        return parseNumericPattern(numericPatterns.second, dateString);\n      case \"so\":\n        return match.ordinalNumber(dateString, { unit: \"second\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 59;\n  }\n\n  set(date, _flags, value) {\n    date.setSeconds(value, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"t\", \"T\"];\n}\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,iBAAiB;AACjD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,aAAa;AAE/D,OAAO,MAAMC,YAAY,SAASH,MAAM,CAAC;EACvCI,QAAQ,GAAG,EAAE;EAEbC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,QAAQD,KAAK;MACX,KAAK,GAAG;QACN,OAAOL,mBAAmB,CAACH,eAAe,CAACU,MAAM,EAAEH,UAAU,CAAC;MAChE,KAAK,IAAI;QACP,OAAOE,KAAK,CAACE,aAAa,CAACJ,UAAU,EAAE;UAAEK,IAAI,EAAE;QAAS,CAAC,CAAC;MAC5D;QACE,OAAOV,YAAY,CAACM,KAAK,CAACK,MAAM,EAAEN,UAAU,CAAC;IACjD;EACF;EAEAO,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,EAAE;EAClC;EAEAC,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEH,KAAK,EAAE;IACvBE,IAAI,CAACE,UAAU,CAACJ,KAAK,EAAE,CAAC,CAAC;IACzB,OAAOE,IAAI;EACb;EAEAG,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}