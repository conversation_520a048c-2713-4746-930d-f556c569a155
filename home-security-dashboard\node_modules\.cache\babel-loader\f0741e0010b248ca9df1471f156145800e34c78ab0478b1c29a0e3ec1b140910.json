{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameMonth } from \"./isSameMonth.js\";\n\n/**\n * The {@link isThisMonth} function options.\n */\n\n/**\n * @name isThisMonth\n * @category Month Helpers\n * @summary Is the given date in the same month as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same month as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this month\n *\n * @example\n * // If today is 25 September 2014, is 15 September 2014 in this month?\n * const result = isThisMonth(new Date(2014, 8, 15))\n * //=> true\n */\nexport function isThisMonth(date, options) {\n  return isSameMonth(constructFrom(options?.in || date, date), constructNow(options?.in || date));\n}\n\n// Fallback for modularized imports:\nexport default isThisMonth;", "map": {"version": 3, "names": ["constructFrom", "constructNow", "isSameMonth", "isThis<PERSON><PERSON><PERSON>", "date", "options", "in"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/isThisMonth.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { constructNow } from \"./constructNow.js\";\nimport { isSameMonth } from \"./isSameMonth.js\";\n\n/**\n * The {@link isThisMonth} function options.\n */\n\n/**\n * @name isThisMonth\n * @category Month Helpers\n * @summary Is the given date in the same month as the current date?\n * @pure false\n *\n * @description\n * Is the given date in the same month as the current date?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is in this month\n *\n * @example\n * // If today is 25 September 2014, is 15 September 2014 in this month?\n * const result = isThisMonth(new Date(2014, 8, 15))\n * //=> true\n */\nexport function isThisMonth(date, options) {\n  return isSameMonth(\n    constructFrom(options?.in || date, date),\n    constructNow(options?.in || date),\n  );\n}\n\n// Fallback for modularized imports:\nexport default isThisMonth;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;;AAE9C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,OAAOH,WAAW,CAChBF,aAAa,CAACK,OAAO,EAAEC,EAAE,IAAIF,IAAI,EAAEA,IAAI,CAAC,EACxCH,YAAY,CAACI,OAAO,EAAEC,EAAE,IAAIF,IAAI,CAClC,CAAC;AACH;;AAEA;AACA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}