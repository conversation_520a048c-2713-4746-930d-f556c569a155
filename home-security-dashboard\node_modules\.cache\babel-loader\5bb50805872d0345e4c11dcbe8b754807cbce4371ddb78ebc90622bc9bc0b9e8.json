{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\Resonance-KLE\\\\home-security-dashboard\\\\src\\\\components\\\\layout\\\\Sidebar.tsx\";\nimport React from 'react';\nimport { NavLink } from 'react-router-dom';\nimport { Monitor, Camera, RotateCcw, FileText, Settings, Phone, ChevronLeft, ChevronRight } from 'lucide-react';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst navItems = [{\n  path: '/',\n  icon: Monitor,\n  label: 'Monitoring'\n}, {\n  path: '/camera',\n  icon: Camera,\n  label: 'Security Camera'\n}, {\n  path: '/updates',\n  icon: RotateCcw,\n  label: 'Updates'\n}, {\n  path: '/license',\n  icon: FileText,\n  label: 'License'\n}];\nexport const Sidebar = ({\n  isOpen = true,\n  onClose\n}) => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 lg:hidden z-20\",\n      onClick: onClose\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(motion.aside, {\n      initial: {\n        x: -300\n      },\n      animate: {\n        x: isOpen ? 0 : -300\n      },\n      transition: {\n        type: 'spring',\n        damping: 20\n      },\n      className: `fixed left-0 top-0 bottom-0 w-64 bg-black border-r border-gray-600 p-6 lg:static z-30\n          ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        className: \"lg:hidden absolute right-4 top-4 p-2 hover:bg-gray-800 rounded-lg transition-colors\",\n        children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n          className: \"w-5 h-5 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-12 mt-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 rounded bg-green-400 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-gray-900 rounded-sm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl font-bold text-white\",\n            children: \"Oniex\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-400 ml-9\",\n          children: \"Endpoint Security\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"space-y-2\",\n        children: navItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n          to: item.path,\n          className: ({\n            isActive\n          }) => `\n                flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors group\n                ${isActive ? 'bg-gray-600 text-white' : 'text-gray-400 hover:bg-gray-600 hover:text-white'}\n              `,\n          children: [/*#__PURE__*/_jsxDEV(item.icon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ChevronRight, {\n            className: \"w-4 h-4 ml-auto opacity-0 group-hover:opacity-100 transition-opacity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-6 left-6 right-6 space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center space-x-3 px-4 py-3 text-gray-400 hover:bg-gray-800 hover:text-white rounded-lg transition-colors w-full\",\n          children: [/*#__PURE__*/_jsxDEV(Settings, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"flex items-center space-x-3 px-4 py-3 text-gray-400 hover:bg-gray-800 hover:text-white rounded-lg transition-colors w-full\",\n          children: [/*#__PURE__*/_jsxDEV(Phone, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: \"Support\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "Monitor", "Camera", "RotateCcw", "FileText", "Settings", "Phone", "ChevronLeft", "ChevronRight", "motion", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "navItems", "path", "icon", "label", "Sidebar", "isOpen", "onClose", "children", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "aside", "initial", "x", "animate", "transition", "type", "damping", "map", "item", "to", "isActive", "_c", "$RefreshReg$"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/src/components/layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink } from 'react-router-dom';\nimport {\n  Monitor,\n  Camera,\n  RotateCcw,\n  FileText,\n  Settings,\n  Phone,\n  ChevronLeft,\n  ChevronRight\n} from 'lucide-react';\nimport { motion } from 'framer-motion';\n\ninterface SidebarProps {\n  isOpen?: boolean;\n  onClose?: () => void;\n}\n\nconst navItems = [\n  { path: '/', icon: Monitor, label: 'Monitoring' },\n  { path: '/camera', icon: Camera, label: 'Security Camera' },\n  { path: '/updates', icon: RotateCcw, label: 'Updates' },\n  { path: '/license', icon: FileText, label: 'License' },\n];\n\nexport const Sidebar: React.FC<SidebarProps> = ({ isOpen = true, onClose }) => {\n  return (\n    <>\n      {/* Mobile Overlay */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 lg:hidden z-20\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Sidebar */}\n      <motion.aside\n        initial={{ x: -300 }}\n        animate={{ x: isOpen ? 0 : -300 }}\n        transition={{ type: 'spring', damping: 20 }}\n        className={`fixed left-0 top-0 bottom-0 w-64 bg-black border-r border-gray-600 p-6 lg:static z-30\n          ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}`}\n      >\n        {/* Close button - mobile only */}\n        <button\n          onClick={onClose}\n          className=\"lg:hidden absolute right-4 top-4 p-2 hover:bg-gray-800 rounded-lg transition-colors\"\n        >\n          <ChevronLeft className=\"w-5 h-5 text-gray-400\" />\n        </button>\n\n        {/* Logo */}\n        <div className=\"mb-12 mt-4\">\n          <div className=\"flex items-center space-x-3 mb-2\">\n            <div className=\"w-6 h-6 rounded bg-green-400 flex items-center justify-center\">\n              <div className=\"w-3 h-3 bg-gray-900 rounded-sm\"></div>\n            </div>\n            <span className=\"text-xl font-bold text-white\">Oniex</span>\n          </div>\n          <div className=\"text-sm text-gray-400 ml-9\">Endpoint Security</div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"space-y-2\">\n          {navItems.map((item) => (\n            <NavLink\n              key={item.path}\n              to={item.path}\n              className={({ isActive }) => `\n                flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors group\n                ${isActive\n                  ? 'bg-gray-600 text-white'\n                  : 'text-gray-400 hover:bg-gray-600 hover:text-white'}\n              `}\n            >\n              <item.icon className=\"w-5 h-5\" />\n              <span className=\"text-sm\">{item.label}</span>\n              <ChevronRight className=\"w-4 h-4 ml-auto opacity-0 group-hover:opacity-100 transition-opacity\" />\n            </NavLink>\n          ))}\n        </nav>\n\n        {/* Bottom Actions */}\n        <div className=\"absolute bottom-6 left-6 right-6 space-y-4\">\n          <button className=\"flex items-center space-x-3 px-4 py-3 text-gray-400 hover:bg-gray-800 hover:text-white rounded-lg transition-colors w-full\">\n            <Settings className=\"w-5 h-5\" />\n            <span className=\"text-sm\">Settings</span>\n          </button>\n          <button className=\"flex items-center space-x-3 px-4 py-3 text-gray-400 hover:bg-gray-800 hover:text-white rounded-lg transition-colors w-full\">\n            <Phone className=\"w-5 h-5\" />\n            <span className=\"text-sm\">Support</span>\n          </button>\n        </div>\n      </motion.aside>\n    </>\n  );\n};"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,SACEC,OAAO,EACPC,MAAM,EACNC,SAAS,EACTC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,YAAY,QACP,cAAc;AACrB,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOvC,MAAMC,QAAQ,GAAG,CACf;EAAEC,IAAI,EAAE,GAAG;EAAEC,IAAI,EAAEf,OAAO;EAAEgB,KAAK,EAAE;AAAa,CAAC,EACjD;EAAEF,IAAI,EAAE,SAAS;EAAEC,IAAI,EAAEd,MAAM;EAAEe,KAAK,EAAE;AAAkB,CAAC,EAC3D;EAAEF,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAEb,SAAS;EAAEc,KAAK,EAAE;AAAU,CAAC,EACvD;EAAEF,IAAI,EAAE,UAAU;EAAEC,IAAI,EAAEZ,QAAQ;EAAEa,KAAK,EAAE;AAAU,CAAC,CACvD;AAED,OAAO,MAAMC,OAA+B,GAAGA,CAAC;EAAEC,MAAM,GAAG,IAAI;EAAEC;AAAQ,CAAC,KAAK;EAC7E,oBACET,OAAA,CAAAE,SAAA;IAAAQ,QAAA,GAEGF,MAAM,iBACLR,OAAA;MACEW,SAAS,EAAC,qDAAqD;MAC/DC,OAAO,EAAEH;IAAQ;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CACF,eAGDhB,OAAA,CAACF,MAAM,CAACmB,KAAK;MACXC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC;MAAI,CAAE;MACrBC,OAAO,EAAE;QAAED,CAAC,EAAEX,MAAM,GAAG,CAAC,GAAG,CAAC;MAAI,CAAE;MAClCa,UAAU,EAAE;QAAEC,IAAI,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAG,CAAE;MAC5CZ,SAAS,EAAE;AACnB,YAAYH,MAAM,GAAG,eAAe,GAAG,oCAAoC,EAAG;MAAAE,QAAA,gBAGtEV,OAAA;QACEY,OAAO,EAAEH,OAAQ;QACjBE,SAAS,EAAC,qFAAqF;QAAAD,QAAA,eAE/FV,OAAA,CAACJ,WAAW;UAACe,SAAS,EAAC;QAAuB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eAGThB,OAAA;QAAKW,SAAS,EAAC,YAAY;QAAAD,QAAA,gBACzBV,OAAA;UAAKW,SAAS,EAAC,kCAAkC;UAAAD,QAAA,gBAC/CV,OAAA;YAAKW,SAAS,EAAC,+DAA+D;YAAAD,QAAA,eAC5EV,OAAA;cAAKW,SAAS,EAAC;YAAgC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNhB,OAAA;YAAMW,SAAS,EAAC,8BAA8B;YAAAD,QAAA,EAAC;UAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACNhB,OAAA;UAAKW,SAAS,EAAC,4BAA4B;UAAAD,QAAA,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eAGNhB,OAAA;QAAKW,SAAS,EAAC,WAAW;QAAAD,QAAA,EACvBP,QAAQ,CAACqB,GAAG,CAAEC,IAAI,iBACjBzB,OAAA,CAACX,OAAO;UAENqC,EAAE,EAAED,IAAI,CAACrB,IAAK;UACdO,SAAS,EAAEA,CAAC;YAAEgB;UAAS,CAAC,KAAK;AAC3C;AACA,kBAAkBA,QAAQ,GACN,wBAAwB,GACxB,kDAAkD;AACtE,eAAgB;UAAAjB,QAAA,gBAEFV,OAAA,CAACyB,IAAI,CAACpB,IAAI;YAACM,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjChB,OAAA;YAAMW,SAAS,EAAC,SAAS;YAAAD,QAAA,EAAEe,IAAI,CAACnB;UAAK;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7ChB,OAAA,CAACH,YAAY;YAACc,SAAS,EAAC;UAAsE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA,GAX5FS,IAAI,CAACrB,IAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYP,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNhB,OAAA;QAAKW,SAAS,EAAC,4CAA4C;QAAAD,QAAA,gBACzDV,OAAA;UAAQW,SAAS,EAAC,4HAA4H;UAAAD,QAAA,gBAC5IV,OAAA,CAACN,QAAQ;YAACiB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChChB,OAAA;YAAMW,SAAS,EAAC,SAAS;YAAAD,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACThB,OAAA;UAAQW,SAAS,EAAC,4HAA4H;UAAAD,QAAA,gBAC5IV,OAAA,CAACL,KAAK;YAACgB,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7BhB,OAAA;YAAMW,SAAS,EAAC,SAAS;YAAAD,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA,eACf,CAAC;AAEP,CAAC;AAACY,EAAA,GAxEWrB,OAA+B;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}