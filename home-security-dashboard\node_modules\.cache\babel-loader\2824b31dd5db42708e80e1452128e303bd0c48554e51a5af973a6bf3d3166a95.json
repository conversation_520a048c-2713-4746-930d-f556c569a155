{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { secondsInDay, secondsInHour, secondsInMinute, secondsInMonth, secondsInQuarter, secondsInWeek, secondsInYear } from \"./constants.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { differenceInCalendarMonths } from \"./differenceInCalendarMonths.js\";\nimport { differenceInCalendarQuarters } from \"./differenceInCalendarQuarters.js\";\nimport { differenceInCalendarWeeks } from \"./differenceInCalendarWeeks.js\";\nimport { differenceInCalendarYears } from \"./differenceInCalendarYears.js\";\nimport { differenceInHours } from \"./differenceInHours.js\";\nimport { differenceInMinutes } from \"./differenceInMinutes.js\";\nimport { differenceInSeconds } from \"./differenceInSeconds.js\";\n\n/**\n * The {@link intlFormatDistance} function options.\n */\n\n/**\n * The unit used to format the distance in {@link intlFormatDistance}.\n */\n\n/**\n * @name intlFormatDistance\n * @category Common Helpers\n * @summary Formats distance between two dates in a human-readable format\n * @description\n * The function calculates the difference between two dates and formats it as a human-readable string.\n *\n * The function will pick the most appropriate unit depending on the distance between dates. For example, if the distance is a few hours, it might return `x hours`. If the distance is a few months, it might return `x months`.\n *\n * You can also specify a unit to force using it regardless of the distance to get a result like `123456 hours`.\n *\n * See the table below for the unit picking logic:\n *\n * | Distance between dates | Result (past)  | Result (future) |\n * | ---------------------- | -------------- | --------------- |\n * | 0 seconds              | now            | now             |\n * | 1-59 seconds           | X seconds ago  | in X seconds    |\n * | 1-59 minutes           | X minutes ago  | in X minutes    |\n * | 1-23 hours             | X hours ago    | in X hours      |\n * | 1 day                  | yesterday      | tomorrow        |\n * | 2-6 days               | X days ago     | in X days       |\n * | 7 days                 | last week      | next week       |\n * | 8 days-1 month         | X weeks ago    | in X weeks      |\n * | 1 month                | last month     | next month      |\n * | 2-3 months             | X months ago   | in X months     |\n * | 1 quarter              | last quarter   | next quarter    |\n * | 2-3 quarters           | X quarters ago | in X quarters   |\n * | 1 year                 | last year      | next year       |\n * | 2+ years               | X years ago    | in X years      |\n *\n * @param laterDate - The date\n * @param earlierDate - The date to compare with.\n * @param options - An object with options.\n * See MDN for details [Locale identification and negotiation](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locale_identification_and_negotiation)\n * The narrow one could be similar to the short one for some locales.\n *\n * @returns The distance in words according to language-sensitive relative time formatting.\n *\n * @throws `date` must not be Invalid Date\n * @throws `baseDate` must not be Invalid Date\n * @throws `options.unit` must not be invalid Unit\n * @throws `options.locale` must not be invalid locale\n * @throws `options.localeMatcher` must not be invalid localeMatcher\n * @throws `options.numeric` must not be invalid numeric\n * @throws `options.style` must not be invalid style\n *\n * @example\n * // What is the distance between the dates when the fist date is after the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0)\n * )\n * //=> 'in 1 hour'\n *\n * // What is the distance between the dates when the fist date is before the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0)\n * )\n * //=> '1 hour ago'\n *\n * @example\n * // Use the unit option to force the function to output the result in quarters. Without setting it, the example would return \"next year\"\n * intlFormatDistance(\n *   new Date(1987, 6, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { unit: 'quarter' }\n * )\n * //=> 'in 5 quarters'\n *\n * @example\n * // Use the locale option to get the result in Spanish. Without setting it, the example would return \"in 1 hour\".\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { locale: 'es' }\n * )\n * //=> 'dentro de 1 hora'\n *\n * @example\n * // Use the numeric option to force the function to use numeric values. Without setting it, the example would return \"tomorrow\".\n * intlFormatDistance(\n *   new Date(1986, 3, 5, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { numeric: 'always' }\n * )\n * //=> 'in 1 day'\n *\n * @example\n * // Use the style option to force the function to use short values. Without setting it, the example would return \"in 2 years\".\n * intlFormatDistance(\n *   new Date(1988, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { style: 'short' }\n * )\n * //=> 'in 2 yr'\n */\nexport function intlFormatDistance(laterDate, earlierDate, options) {\n  let value = 0;\n  let unit;\n  const [laterDate_, earlierDate_] = normalizeDates(options?.in, laterDate, earlierDate);\n  if (!options?.unit) {\n    // Get the unit based on diffInSeconds calculations if no unit is specified\n    const diffInSeconds = differenceInSeconds(laterDate_, earlierDate_); // The smallest unit\n\n    if (Math.abs(diffInSeconds) < secondsInMinute) {\n      value = differenceInSeconds(laterDate_, earlierDate_);\n      unit = \"second\";\n    } else if (Math.abs(diffInSeconds) < secondsInHour) {\n      value = differenceInMinutes(laterDate_, earlierDate_);\n      unit = \"minute\";\n    } else if (Math.abs(diffInSeconds) < secondsInDay && Math.abs(differenceInCalendarDays(laterDate_, earlierDate_)) < 1) {\n      value = differenceInHours(laterDate_, earlierDate_);\n      unit = \"hour\";\n    } else if (Math.abs(diffInSeconds) < secondsInWeek && (value = differenceInCalendarDays(laterDate_, earlierDate_)) && Math.abs(value) < 7) {\n      unit = \"day\";\n    } else if (Math.abs(diffInSeconds) < secondsInMonth) {\n      value = differenceInCalendarWeeks(laterDate_, earlierDate_);\n      unit = \"week\";\n    } else if (Math.abs(diffInSeconds) < secondsInQuarter) {\n      value = differenceInCalendarMonths(laterDate_, earlierDate_);\n      unit = \"month\";\n    } else if (Math.abs(diffInSeconds) < secondsInYear) {\n      if (differenceInCalendarQuarters(laterDate_, earlierDate_) < 4) {\n        // To filter out cases that are less than a year but match 4 quarters\n        value = differenceInCalendarQuarters(laterDate_, earlierDate_);\n        unit = \"quarter\";\n      } else {\n        value = differenceInCalendarYears(laterDate_, earlierDate_);\n        unit = \"year\";\n      }\n    } else {\n      value = differenceInCalendarYears(laterDate_, earlierDate_);\n      unit = \"year\";\n    }\n  } else {\n    // Get the value if unit is specified\n    unit = options?.unit;\n    if (unit === \"second\") {\n      value = differenceInSeconds(laterDate_, earlierDate_);\n    } else if (unit === \"minute\") {\n      value = differenceInMinutes(laterDate_, earlierDate_);\n    } else if (unit === \"hour\") {\n      value = differenceInHours(laterDate_, earlierDate_);\n    } else if (unit === \"day\") {\n      value = differenceInCalendarDays(laterDate_, earlierDate_);\n    } else if (unit === \"week\") {\n      value = differenceInCalendarWeeks(laterDate_, earlierDate_);\n    } else if (unit === \"month\") {\n      value = differenceInCalendarMonths(laterDate_, earlierDate_);\n    } else if (unit === \"quarter\") {\n      value = differenceInCalendarQuarters(laterDate_, earlierDate_);\n    } else if (unit === \"year\") {\n      value = differenceInCalendarYears(laterDate_, earlierDate_);\n    }\n  }\n  const rtf = new Intl.RelativeTimeFormat(options?.locale, {\n    numeric: \"auto\",\n    ...options\n  });\n  return rtf.format(value, unit);\n}\n\n// Fallback for modularized imports:\nexport default intlFormatDistance;", "map": {"version": 3, "names": ["normalizeDates", "secondsInDay", "secondsInHour", "secondsInMinute", "secondsIn<PERSON><PERSON><PERSON>", "secondsInQuarter", "secondsInWeek", "secondsInYear", "differenceInCalendarDays", "differenceInCalendarMonths", "differenceInCalendarQuarters", "differenceInCalendarWeeks", "differenceInCalendarYears", "differenceInHours", "differenceInMinutes", "differenceInSeconds", "intlFormatDistance", "laterDate", "earlierDate", "options", "value", "unit", "laterDate_", "earlierDate_", "in", "diffInSeconds", "Math", "abs", "rtf", "Intl", "RelativeTimeFormat", "locale", "numeric", "format"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/intlFormatDistance.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport {\n  secondsInDay,\n  secondsInHour,\n  secondsInMinute,\n  secondsInMonth,\n  secondsInQuarter,\n  secondsInWeek,\n  secondsInYear,\n} from \"./constants.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { differenceInCalendarMonths } from \"./differenceInCalendarMonths.js\";\nimport { differenceInCalendarQuarters } from \"./differenceInCalendarQuarters.js\";\nimport { differenceInCalendarWeeks } from \"./differenceInCalendarWeeks.js\";\nimport { differenceInCalendarYears } from \"./differenceInCalendarYears.js\";\nimport { differenceInHours } from \"./differenceInHours.js\";\nimport { differenceInMinutes } from \"./differenceInMinutes.js\";\nimport { differenceInSeconds } from \"./differenceInSeconds.js\";\n\n/**\n * The {@link intlFormatDistance} function options.\n */\n\n/**\n * The unit used to format the distance in {@link intlFormatDistance}.\n */\n\n/**\n * @name intlFormatDistance\n * @category Common Helpers\n * @summary Formats distance between two dates in a human-readable format\n * @description\n * The function calculates the difference between two dates and formats it as a human-readable string.\n *\n * The function will pick the most appropriate unit depending on the distance between dates. For example, if the distance is a few hours, it might return `x hours`. If the distance is a few months, it might return `x months`.\n *\n * You can also specify a unit to force using it regardless of the distance to get a result like `123456 hours`.\n *\n * See the table below for the unit picking logic:\n *\n * | Distance between dates | Result (past)  | Result (future) |\n * | ---------------------- | -------------- | --------------- |\n * | 0 seconds              | now            | now             |\n * | 1-59 seconds           | X seconds ago  | in X seconds    |\n * | 1-59 minutes           | X minutes ago  | in X minutes    |\n * | 1-23 hours             | X hours ago    | in X hours      |\n * | 1 day                  | yesterday      | tomorrow        |\n * | 2-6 days               | X days ago     | in X days       |\n * | 7 days                 | last week      | next week       |\n * | 8 days-1 month         | X weeks ago    | in X weeks      |\n * | 1 month                | last month     | next month      |\n * | 2-3 months             | X months ago   | in X months     |\n * | 1 quarter              | last quarter   | next quarter    |\n * | 2-3 quarters           | X quarters ago | in X quarters   |\n * | 1 year                 | last year      | next year       |\n * | 2+ years               | X years ago    | in X years      |\n *\n * @param laterDate - The date\n * @param earlierDate - The date to compare with.\n * @param options - An object with options.\n * See MDN for details [Locale identification and negotiation](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl#locale_identification_and_negotiation)\n * The narrow one could be similar to the short one for some locales.\n *\n * @returns The distance in words according to language-sensitive relative time formatting.\n *\n * @throws `date` must not be Invalid Date\n * @throws `baseDate` must not be Invalid Date\n * @throws `options.unit` must not be invalid Unit\n * @throws `options.locale` must not be invalid locale\n * @throws `options.localeMatcher` must not be invalid localeMatcher\n * @throws `options.numeric` must not be invalid numeric\n * @throws `options.style` must not be invalid style\n *\n * @example\n * // What is the distance between the dates when the fist date is after the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0)\n * )\n * //=> 'in 1 hour'\n *\n * // What is the distance between the dates when the fist date is before the second?\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0)\n * )\n * //=> '1 hour ago'\n *\n * @example\n * // Use the unit option to force the function to output the result in quarters. Without setting it, the example would return \"next year\"\n * intlFormatDistance(\n *   new Date(1987, 6, 4, 10, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { unit: 'quarter' }\n * )\n * //=> 'in 5 quarters'\n *\n * @example\n * // Use the locale option to get the result in Spanish. Without setting it, the example would return \"in 1 hour\".\n * intlFormatDistance(\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 10, 30, 0),\n *   { locale: 'es' }\n * )\n * //=> 'dentro de 1 hora'\n *\n * @example\n * // Use the numeric option to force the function to use numeric values. Without setting it, the example would return \"tomorrow\".\n * intlFormatDistance(\n *   new Date(1986, 3, 5, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { numeric: 'always' }\n * )\n * //=> 'in 1 day'\n *\n * @example\n * // Use the style option to force the function to use short values. Without setting it, the example would return \"in 2 years\".\n * intlFormatDistance(\n *   new Date(1988, 3, 4, 11, 30, 0),\n *   new Date(1986, 3, 4, 11, 30, 0),\n *   { style: 'short' }\n * )\n * //=> 'in 2 yr'\n */\nexport function intlFormatDistance(laterDate, earlierDate, options) {\n  let value = 0;\n  let unit;\n\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  if (!options?.unit) {\n    // Get the unit based on diffInSeconds calculations if no unit is specified\n    const diffInSeconds = differenceInSeconds(laterDate_, earlierDate_); // The smallest unit\n\n    if (Math.abs(diffInSeconds) < secondsInMinute) {\n      value = differenceInSeconds(laterDate_, earlierDate_);\n      unit = \"second\";\n    } else if (Math.abs(diffInSeconds) < secondsInHour) {\n      value = differenceInMinutes(laterDate_, earlierDate_);\n      unit = \"minute\";\n    } else if (\n      Math.abs(diffInSeconds) < secondsInDay &&\n      Math.abs(differenceInCalendarDays(laterDate_, earlierDate_)) < 1\n    ) {\n      value = differenceInHours(laterDate_, earlierDate_);\n      unit = \"hour\";\n    } else if (\n      Math.abs(diffInSeconds) < secondsInWeek &&\n      (value = differenceInCalendarDays(laterDate_, earlierDate_)) &&\n      Math.abs(value) < 7\n    ) {\n      unit = \"day\";\n    } else if (Math.abs(diffInSeconds) < secondsInMonth) {\n      value = differenceInCalendarWeeks(laterDate_, earlierDate_);\n      unit = \"week\";\n    } else if (Math.abs(diffInSeconds) < secondsInQuarter) {\n      value = differenceInCalendarMonths(laterDate_, earlierDate_);\n      unit = \"month\";\n    } else if (Math.abs(diffInSeconds) < secondsInYear) {\n      if (differenceInCalendarQuarters(laterDate_, earlierDate_) < 4) {\n        // To filter out cases that are less than a year but match 4 quarters\n        value = differenceInCalendarQuarters(laterDate_, earlierDate_);\n        unit = \"quarter\";\n      } else {\n        value = differenceInCalendarYears(laterDate_, earlierDate_);\n        unit = \"year\";\n      }\n    } else {\n      value = differenceInCalendarYears(laterDate_, earlierDate_);\n      unit = \"year\";\n    }\n  } else {\n    // Get the value if unit is specified\n    unit = options?.unit;\n    if (unit === \"second\") {\n      value = differenceInSeconds(laterDate_, earlierDate_);\n    } else if (unit === \"minute\") {\n      value = differenceInMinutes(laterDate_, earlierDate_);\n    } else if (unit === \"hour\") {\n      value = differenceInHours(laterDate_, earlierDate_);\n    } else if (unit === \"day\") {\n      value = differenceInCalendarDays(laterDate_, earlierDate_);\n    } else if (unit === \"week\") {\n      value = differenceInCalendarWeeks(laterDate_, earlierDate_);\n    } else if (unit === \"month\") {\n      value = differenceInCalendarMonths(laterDate_, earlierDate_);\n    } else if (unit === \"quarter\") {\n      value = differenceInCalendarQuarters(laterDate_, earlierDate_);\n    } else if (unit === \"year\") {\n      value = differenceInCalendarYears(laterDate_, earlierDate_);\n    }\n  }\n\n  const rtf = new Intl.RelativeTimeFormat(options?.locale, {\n    numeric: \"auto\",\n    ...options,\n  });\n\n  return rtf.format(value, unit);\n}\n\n// Fallback for modularized imports:\nexport default intlFormatDistance;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SACEC,YAAY,EACZC,aAAa,EACbC,eAAe,EACfC,cAAc,EACdC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,QACR,gBAAgB;AACvB,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,0BAA0B,QAAQ,iCAAiC;AAC5E,SAASC,4BAA4B,QAAQ,mCAAmC;AAChF,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,yBAAyB,QAAQ,gCAAgC;AAC1E,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,mBAAmB,QAAQ,0BAA0B;;AAE9D;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAE;EAClE,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,IAAI;EAER,MAAM,CAACC,UAAU,EAAEC,YAAY,CAAC,GAAGvB,cAAc,CAC/CmB,OAAO,EAAEK,EAAE,EACXP,SAAS,EACTC,WACF,CAAC;EAED,IAAI,CAACC,OAAO,EAAEE,IAAI,EAAE;IAClB;IACA,MAAMI,aAAa,GAAGV,mBAAmB,CAACO,UAAU,EAAEC,YAAY,CAAC,CAAC,CAAC;;IAErE,IAAIG,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGtB,eAAe,EAAE;MAC7CiB,KAAK,GAAGL,mBAAmB,CAACO,UAAU,EAAEC,YAAY,CAAC;MACrDF,IAAI,GAAG,QAAQ;IACjB,CAAC,MAAM,IAAIK,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGvB,aAAa,EAAE;MAClDkB,KAAK,GAAGN,mBAAmB,CAACQ,UAAU,EAAEC,YAAY,CAAC;MACrDF,IAAI,GAAG,QAAQ;IACjB,CAAC,MAAM,IACLK,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGxB,YAAY,IACtCyB,IAAI,CAACC,GAAG,CAACnB,wBAAwB,CAACc,UAAU,EAAEC,YAAY,CAAC,CAAC,GAAG,CAAC,EAChE;MACAH,KAAK,GAAGP,iBAAiB,CAACS,UAAU,EAAEC,YAAY,CAAC;MACnDF,IAAI,GAAG,MAAM;IACf,CAAC,MAAM,IACLK,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGnB,aAAa,KACtCc,KAAK,GAAGZ,wBAAwB,CAACc,UAAU,EAAEC,YAAY,CAAC,CAAC,IAC5DG,IAAI,CAACC,GAAG,CAACP,KAAK,CAAC,GAAG,CAAC,EACnB;MACAC,IAAI,GAAG,KAAK;IACd,CAAC,MAAM,IAAIK,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGrB,cAAc,EAAE;MACnDgB,KAAK,GAAGT,yBAAyB,CAACW,UAAU,EAAEC,YAAY,CAAC;MAC3DF,IAAI,GAAG,MAAM;IACf,CAAC,MAAM,IAAIK,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGpB,gBAAgB,EAAE;MACrDe,KAAK,GAAGX,0BAA0B,CAACa,UAAU,EAAEC,YAAY,CAAC;MAC5DF,IAAI,GAAG,OAAO;IAChB,CAAC,MAAM,IAAIK,IAAI,CAACC,GAAG,CAACF,aAAa,CAAC,GAAGlB,aAAa,EAAE;MAClD,IAAIG,4BAA4B,CAACY,UAAU,EAAEC,YAAY,CAAC,GAAG,CAAC,EAAE;QAC9D;QACAH,KAAK,GAAGV,4BAA4B,CAACY,UAAU,EAAEC,YAAY,CAAC;QAC9DF,IAAI,GAAG,SAAS;MAClB,CAAC,MAAM;QACLD,KAAK,GAAGR,yBAAyB,CAACU,UAAU,EAAEC,YAAY,CAAC;QAC3DF,IAAI,GAAG,MAAM;MACf;IACF,CAAC,MAAM;MACLD,KAAK,GAAGR,yBAAyB,CAACU,UAAU,EAAEC,YAAY,CAAC;MAC3DF,IAAI,GAAG,MAAM;IACf;EACF,CAAC,MAAM;IACL;IACAA,IAAI,GAAGF,OAAO,EAAEE,IAAI;IACpB,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACrBD,KAAK,GAAGL,mBAAmB,CAACO,UAAU,EAAEC,YAAY,CAAC;IACvD,CAAC,MAAM,IAAIF,IAAI,KAAK,QAAQ,EAAE;MAC5BD,KAAK,GAAGN,mBAAmB,CAACQ,UAAU,EAAEC,YAAY,CAAC;IACvD,CAAC,MAAM,IAAIF,IAAI,KAAK,MAAM,EAAE;MAC1BD,KAAK,GAAGP,iBAAiB,CAACS,UAAU,EAAEC,YAAY,CAAC;IACrD,CAAC,MAAM,IAAIF,IAAI,KAAK,KAAK,EAAE;MACzBD,KAAK,GAAGZ,wBAAwB,CAACc,UAAU,EAAEC,YAAY,CAAC;IAC5D,CAAC,MAAM,IAAIF,IAAI,KAAK,MAAM,EAAE;MAC1BD,KAAK,GAAGT,yBAAyB,CAACW,UAAU,EAAEC,YAAY,CAAC;IAC7D,CAAC,MAAM,IAAIF,IAAI,KAAK,OAAO,EAAE;MAC3BD,KAAK,GAAGX,0BAA0B,CAACa,UAAU,EAAEC,YAAY,CAAC;IAC9D,CAAC,MAAM,IAAIF,IAAI,KAAK,SAAS,EAAE;MAC7BD,KAAK,GAAGV,4BAA4B,CAACY,UAAU,EAAEC,YAAY,CAAC;IAChE,CAAC,MAAM,IAAIF,IAAI,KAAK,MAAM,EAAE;MAC1BD,KAAK,GAAGR,yBAAyB,CAACU,UAAU,EAAEC,YAAY,CAAC;IAC7D;EACF;EAEA,MAAMK,GAAG,GAAG,IAAIC,IAAI,CAACC,kBAAkB,CAACX,OAAO,EAAEY,MAAM,EAAE;IACvDC,OAAO,EAAE,MAAM;IACf,GAAGb;EACL,CAAC,CAAC;EAEF,OAAOS,GAAG,CAACK,MAAM,CAACb,KAAK,EAAEC,IAAI,CAAC;AAChC;;AAEA;AACA,eAAeL,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}