{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link interval} function options.\n */\n\n/**\n * The {@link interval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the start argument,\n * then the end interval date. If a context function is passed, it uses the context\n * function return type.\n */\n\n/**\n * @name interval\n * @category Interval Helpers\n * @summary Creates an interval object and validates its values.\n *\n * @description\n * Creates a normalized interval object and validates its values. If the interval is invalid, an exception is thrown.\n *\n * @typeParam StartDate - Start date type.\n * @typeParam EndDate - End date type.\n * @typeParam Options - Options type.\n *\n * @param start - The start of the interval.\n * @param end - The end of the interval.\n * @param options - The options object.\n *\n * @throws `Start date is invalid` when `start` is invalid.\n * @throws `End date is invalid` when `end` is invalid.\n * @throws `End date must be after start date` when end is before `start` and `options.assertPositive` is true.\n *\n * @returns The normalized and validated interval object.\n */\nexport function interval(start, end, options) {\n  const [_start, _end] = normalizeDates(options?.in, start, end);\n  if (isNaN(+_start)) throw new TypeError(\"Start date is invalid\");\n  if (isNaN(+_end)) throw new TypeError(\"End date is invalid\");\n  if (options?.assertPositive && +_start > +_end) throw new TypeError(\"End date must be after start date\");\n  return {\n    start: _start,\n    end: _end\n  };\n}\n\n// Fallback for modularized imports:\nexport default interval;", "map": {"version": 3, "names": ["normalizeDates", "interval", "start", "end", "options", "_start", "_end", "in", "isNaN", "TypeError", "assertPositive"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/interval.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link interval} function options.\n */\n\n/**\n * The {@link interval} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the start argument,\n * then the end interval date. If a context function is passed, it uses the context\n * function return type.\n */\n\n/**\n * @name interval\n * @category Interval Helpers\n * @summary Creates an interval object and validates its values.\n *\n * @description\n * Creates a normalized interval object and validates its values. If the interval is invalid, an exception is thrown.\n *\n * @typeParam StartDate - Start date type.\n * @typeParam EndDate - End date type.\n * @typeParam Options - Options type.\n *\n * @param start - The start of the interval.\n * @param end - The end of the interval.\n * @param options - The options object.\n *\n * @throws `Start date is invalid` when `start` is invalid.\n * @throws `End date is invalid` when `end` is invalid.\n * @throws `End date must be after start date` when end is before `start` and `options.assertPositive` is true.\n *\n * @returns The normalized and validated interval object.\n */\nexport function interval(start, end, options) {\n  const [_start, _end] = normalizeDates(options?.in, start, end);\n\n  if (isNaN(+_start)) throw new TypeError(\"Start date is invalid\");\n  if (isNaN(+_end)) throw new TypeError(\"End date is invalid\");\n\n  if (options?.assertPositive && +_start > +_end)\n    throw new TypeError(\"End date must be after start date\");\n\n  return { start: _start, end: _end };\n}\n\n// Fallback for modularized imports:\nexport default interval;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;;AAEzD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAE;EAC5C,MAAM,CAACC,MAAM,EAAEC,IAAI,CAAC,GAAGN,cAAc,CAACI,OAAO,EAAEG,EAAE,EAAEL,KAAK,EAAEC,GAAG,CAAC;EAE9D,IAAIK,KAAK,CAAC,CAACH,MAAM,CAAC,EAAE,MAAM,IAAII,SAAS,CAAC,uBAAuB,CAAC;EAChE,IAAID,KAAK,CAAC,CAACF,IAAI,CAAC,EAAE,MAAM,IAAIG,SAAS,CAAC,qBAAqB,CAAC;EAE5D,IAAIL,OAAO,EAAEM,cAAc,IAAI,CAACL,MAAM,GAAG,CAACC,IAAI,EAC5C,MAAM,IAAIG,SAAS,CAAC,mCAAmC,CAAC;EAE1D,OAAO;IAAEP,KAAK,EAAEG,MAAM;IAAEF,GAAG,EAAEG;EAAK,CAAC;AACrC;;AAEA;AACA,eAAeL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}