{"ast": null, "code": "import { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link startOfYesterday} function options.\n */\n\n/**\n * @name startOfYesterday\n * @category Day Helpers\n * @summary Return the start of yesterday.\n * @pure false\n *\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param options - An object with options\n *\n * @description\n * Return the start of yesterday.\n *\n * @returns The start of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfYesterday()\n * //=> Sun Oct 5 2014 00:00:00\n */\nexport function startOfYesterday(options) {\n  const now = constructNow(options?.in);\n  const year = now.getFullYear();\n  const month = now.getMonth();\n  const day = now.getDate();\n  const date = constructNow(options?.in);\n  date.setFullYear(year, month, day - 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default startOfYesterday;", "map": {"version": 3, "names": ["constructNow", "startOfYesterday", "options", "now", "in", "year", "getFullYear", "month", "getMonth", "day", "getDate", "date", "setFullYear", "setHours"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/startOfYesterday.js"], "sourcesContent": ["import { constructNow } from \"./constructNow.js\";\n\n/**\n * The {@link startOfYesterday} function options.\n */\n\n/**\n * @name startOfYesterday\n * @category Day Helpers\n * @summary Return the start of yesterday.\n * @pure false\n *\n * @typeParam ContextDate - The `Date` type of the context function.\n *\n * @param options - An object with options\n *\n * @description\n * Return the start of yesterday.\n *\n * @returns The start of yesterday\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfYesterday()\n * //=> Sun Oct 5 2014 00:00:00\n */\nexport function startOfYesterday(options) {\n  const now = constructNow(options?.in);\n  const year = now.getFullYear();\n  const month = now.getMonth();\n  const day = now.getDate();\n\n  const date = constructNow(options?.in);\n  date.setFullYear(year, month, day - 1);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}\n\n// Fallback for modularized imports:\nexport default startOfYesterday;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,mBAAmB;;AAEhD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EACxC,MAAMC,GAAG,GAAGH,YAAY,CAACE,OAAO,EAAEE,EAAE,CAAC;EACrC,MAAMC,IAAI,GAAGF,GAAG,CAACG,WAAW,CAAC,CAAC;EAC9B,MAAMC,KAAK,GAAGJ,GAAG,CAACK,QAAQ,CAAC,CAAC;EAC5B,MAAMC,GAAG,GAAGN,GAAG,CAACO,OAAO,CAAC,CAAC;EAEzB,MAAMC,IAAI,GAAGX,YAAY,CAACE,OAAO,EAAEE,EAAE,CAAC;EACtCO,IAAI,CAACC,WAAW,CAACP,IAAI,EAAEE,KAAK,EAAEE,GAAG,GAAG,CAAC,CAAC;EACtCE,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzB,OAAOF,IAAI;AACb;;AAEA;AACA,eAAeV,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}