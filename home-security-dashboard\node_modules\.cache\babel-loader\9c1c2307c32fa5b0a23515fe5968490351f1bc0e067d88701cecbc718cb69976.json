{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport { useState, useEffect } from 'react';\n// Mock data for demonstration\nconst mockAlerts = [{\n  id: '1',\n  type: 'Intrusion',\n  timestamp: new Date().toISOString(),\n  description: 'Motion detected in living room'\n}, {\n  id: '2',\n  type: 'Animal',\n  timestamp: new Date(Date.now() - 5 * 60000).toISOString(),\n  description: 'Pet movement detected in kitchen'\n}];\nconst mockCameraFeeds = [{\n  id: '1',\n  imageUrl: '/captured_images/img_20250514_185115_951.jpg',\n  timestamp: new Date().toISOString(),\n  aiDescription: 'Living room - no unusual activity detected'\n}, {\n  id: '2',\n  imageUrl: '/captured_images/img_20250514_185135_819.jpg',\n  timestamp: new Date(Date.now() - 2 * 60000).toISOString(),\n  aiDescription: 'Kitchen - pet detected near food bowl'\n}];\nconst mockSystemStatus = {\n  lastImageCapture: new Date(Date.now() - 30000).toISOString(),\n  lastSensorEvent: new Date(Date.now() - 120000).toISOString(),\n  overallStatus: 'Online'\n};\n\n// API functions\nexport const fetchAlerts = async () => {\n  try {\n    // In production, this would be a real API call\n    // const response = await fetch(`${API_BASE_URL}/alerts`);\n    // return await response.json();\n\n    // For demo purposes, return mock data\n    return new Promise(resolve => {\n      setTimeout(() => resolve(mockAlerts), 500);\n    });\n  } catch (error) {\n    console.error('Error fetching alerts:', error);\n    return [];\n  }\n};\nexport const fetchCameraFeeds = async () => {\n  try {\n    // In production, this would be a real API call\n    // const response = await fetch(`${API_BASE_URL}/camera-feeds`);\n    // return await response.json();\n\n    // For demo purposes, return mock data\n    return new Promise(resolve => {\n      setTimeout(() => resolve(mockCameraFeeds), 500);\n    });\n  } catch (error) {\n    console.error('Error fetching camera feeds:', error);\n    return [];\n  }\n};\nexport const fetchSystemStatus = async () => {\n  try {\n    // In production, this would be a real API call\n    // const response = await fetch(`${API_BASE_URL}/system-status`);\n    // return await response.json();\n\n    // For demo purposes, return mock data\n    return new Promise(resolve => {\n      setTimeout(() => resolve(mockSystemStatus), 500);\n    });\n  } catch (error) {\n    console.error('Error fetching system status:', error);\n    return mockSystemStatus;\n  }\n};\n\n// Custom hooks for data fetching\nexport const useAlerts = () => {\n  _s();\n  const [alerts, setAlerts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const loadAlerts = async () => {\n      try {\n        setLoading(true);\n        const data = await fetchAlerts();\n        setAlerts(data);\n      } catch (err) {\n        setError(err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadAlerts();\n  }, []);\n  return {\n    alerts,\n    loading,\n    error\n  };\n};\n_s(useAlerts, \"vxu+AwvM2Pw6sGSp/GKYKQQ5vqA=\");\nexport const useCameraFeeds = () => {\n  _s2();\n  const [feeds, setFeeds] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const loadFeeds = async () => {\n      try {\n        setLoading(true);\n        const data = await fetchCameraFeeds();\n        setFeeds(data);\n      } catch (err) {\n        setError(err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadFeeds();\n  }, []);\n  return {\n    feeds,\n    loading,\n    error\n  };\n};\n_s2(useCameraFeeds, \"0h4HL85MVyNlRG63ZreDY0XlCng=\");\nexport const useSystemStatus = () => {\n  _s3();\n  const [status, setStatus] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const loadStatus = async () => {\n      try {\n        setLoading(true);\n        const data = await fetchSystemStatus();\n        setStatus(data);\n      } catch (err) {\n        setError(err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadStatus();\n  }, []);\n  return {\n    status,\n    loading,\n    error\n  };\n};\n_s3(useSystemStatus, \"8QaqsfG/f5Mmy03sez2J831nd0k=\");", "map": {"version": 3, "names": ["useState", "useEffect", "mock<PERSON>ler<PERSON>", "id", "type", "timestamp", "Date", "toISOString", "description", "now", "mockCameraFeeds", "imageUrl", "aiDescription", "mockSystemStatus", "lastImageCapture", "lastSensorEvent", "overallStatus", "fetch<PERSON><PERSON><PERSON>", "Promise", "resolve", "setTimeout", "error", "console", "fetchCameraFeeds", "fetchSystemStatus", "useAlerts", "_s", "alerts", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "setError", "loadAlerts", "data", "err", "useCameraFeeds", "_s2", "feeds", "setFeeds", "loadFeeds", "useSystemStatus", "_s3", "status", "setStatus", "loadStatus"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/src/utils/api.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { Alert, CameraFeed, SystemStatus } from '../types';\n\n// Mock data for demonstration\nconst mockAlerts: Alert[] = [\n  {\n    id: '1',\n    type: 'Intrusion',\n    timestamp: new Date().toISOString(),\n    description: 'Motion detected in living room'\n  },\n  {\n    id: '2',\n    type: 'Animal',\n    timestamp: new Date(Date.now() - 5 * 60000).toISOString(),\n    description: 'Pet movement detected in kitchen'\n  }\n];\n\nconst mockCameraFeeds: CameraFeed[] = [\n  {\n    id: '1',\n    imageUrl: '/captured_images/img_20250514_185115_951.jpg',\n    timestamp: new Date().toISOString(),\n    aiDescription: 'Living room - no unusual activity detected'\n  },\n  {\n    id: '2',\n    imageUrl: '/captured_images/img_20250514_185135_819.jpg',\n    timestamp: new Date(Date.now() - 2 * 60000).toISOString(),\n    aiDescription: 'Kitchen - pet detected near food bowl'\n  }\n];\n\nconst mockSystemStatus: SystemStatus = {\n  lastImageCapture: new Date(Date.now() - 30000).toISOString(),\n  lastSensorEvent: new Date(Date.now() - 120000).toISOString(),\n  overallStatus: 'Online'\n};\n\n// API functions\nexport const fetchAlerts = async (): Promise<Alert[]> => {\n  try {\n    // In production, this would be a real API call\n    // const response = await fetch(`${API_BASE_URL}/alerts`);\n    // return await response.json();\n\n    // For demo purposes, return mock data\n    return new Promise((resolve) => {\n      setTimeout(() => resolve(mockAlerts), 500);\n    });\n  } catch (error) {\n    console.error('Error fetching alerts:', error);\n    return [];\n  }\n};\n\nexport const fetchCameraFeeds = async (): Promise<CameraFeed[]> => {\n  try {\n    // In production, this would be a real API call\n    // const response = await fetch(`${API_BASE_URL}/camera-feeds`);\n    // return await response.json();\n\n    // For demo purposes, return mock data\n    return new Promise((resolve) => {\n      setTimeout(() => resolve(mockCameraFeeds), 500);\n    });\n  } catch (error) {\n    console.error('Error fetching camera feeds:', error);\n    return [];\n  }\n};\n\nexport const fetchSystemStatus = async (): Promise<SystemStatus> => {\n  try {\n    // In production, this would be a real API call\n    // const response = await fetch(`${API_BASE_URL}/system-status`);\n    // return await response.json();\n\n    // For demo purposes, return mock data\n    return new Promise((resolve) => {\n      setTimeout(() => resolve(mockSystemStatus), 500);\n    });\n  } catch (error) {\n    console.error('Error fetching system status:', error);\n    return mockSystemStatus;\n  }\n};\n\n// Custom hooks for data fetching\nexport const useAlerts = () => {\n  const [alerts, setAlerts] = useState<Alert[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<Error | null>(null);\n\n  useEffect(() => {\n    const loadAlerts = async () => {\n      try {\n        setLoading(true);\n        const data = await fetchAlerts();\n        setAlerts(data);\n      } catch (err) {\n        setError(err as Error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadAlerts();\n  }, []);\n\n  return { alerts, loading, error };\n};\n\nexport const useCameraFeeds = () => {\n  const [feeds, setFeeds] = useState<CameraFeed[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<Error | null>(null);\n\n  useEffect(() => {\n    const loadFeeds = async () => {\n      try {\n        setLoading(true);\n        const data = await fetchCameraFeeds();\n        setFeeds(data);\n      } catch (err) {\n        setError(err as Error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadFeeds();\n  }, []);\n\n  return { feeds, loading, error };\n};\n\nexport const useSystemStatus = () => {\n  const [status, setStatus] = useState<SystemStatus | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<Error | null>(null);\n\n  useEffect(() => {\n    const loadStatus = async () => {\n      try {\n        setLoading(true);\n        const data = await fetchSystemStatus();\n        setStatus(data);\n      } catch (err) {\n        setError(err as Error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadStatus();\n  }, []);\n\n  return { status, loading, error };\n};"], "mappings": ";;;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAG3C;AACA,MAAMC,UAAmB,GAAG,CAC1B;EACEC,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACnCC,WAAW,EAAE;AACf,CAAC,EACD;EACEL,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAACF,WAAW,CAAC,CAAC;EACzDC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAME,eAA6B,GAAG,CACpC;EACEP,EAAE,EAAE,GAAG;EACPQ,QAAQ,EAAE,8CAA8C;EACxDN,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACnCK,aAAa,EAAE;AACjB,CAAC,EACD;EACET,EAAE,EAAE,GAAG;EACPQ,QAAQ,EAAE,8CAA8C;EACxDN,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CAACF,WAAW,CAAC,CAAC;EACzDK,aAAa,EAAE;AACjB,CAAC,CACF;AAED,MAAMC,gBAA8B,GAAG;EACrCC,gBAAgB,EAAE,IAAIR,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAACF,WAAW,CAAC,CAAC;EAC5DQ,eAAe,EAAE,IAAIT,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,CAACF,WAAW,CAAC,CAAC;EAC5DS,aAAa,EAAE;AACjB,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAA8B;EACvD,IAAI;IACF;IACA;IACA;;IAEA;IACA,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAK;MAC9BC,UAAU,CAAC,MAAMD,OAAO,CAACjB,UAAU,CAAC,EAAE,GAAG,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOmB,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAmC;EACjE,IAAI;IACF;IACA;IACA;;IAEA;IACA,OAAO,IAAIL,OAAO,CAAEC,OAAO,IAAK;MAC9BC,UAAU,CAAC,MAAMD,OAAO,CAACT,eAAe,CAAC,EAAE,GAAG,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOW,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAmC;EAClE,IAAI;IACF;IACA;IACA;;IAEA;IACA,OAAO,IAAIN,OAAO,CAAEC,OAAO,IAAK;MAC9BC,UAAU,CAAC,MAAMD,OAAO,CAACN,gBAAgB,CAAC,EAAE,GAAG,CAAC;IAClD,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,OAAOR,gBAAgB;EACzB;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG5B,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEU,QAAQ,CAAC,GAAG/B,QAAQ,CAAe,IAAI,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd,MAAM+B,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACFF,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMG,IAAI,GAAG,MAAMhB,WAAW,CAAC,CAAC;QAChCW,SAAS,CAACK,IAAI,CAAC;MACjB,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZH,QAAQ,CAACG,GAAY,CAAC;MACxB,CAAC,SAAS;QACRJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDE,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IAAEL,MAAM;IAAEE,OAAO;IAAER;EAAM,CAAC;AACnC,CAAC;AAACK,EAAA,CAtBWD,SAAS;AAwBtB,OAAO,MAAMU,cAAc,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAClC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAe,EAAE,CAAC;EACpD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEU,QAAQ,CAAC,GAAG/B,QAAQ,CAAe,IAAI,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd,MAAMsC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFT,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMG,IAAI,GAAG,MAAMV,gBAAgB,CAAC,CAAC;QACrCe,QAAQ,CAACL,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZH,QAAQ,CAACG,GAAY,CAAC;MACxB,CAAC,SAAS;QACRJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDS,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IAAEF,KAAK;IAAER,OAAO;IAAER;EAAM,CAAC;AAClC,CAAC;AAACe,GAAA,CAtBWD,cAAc;AAwB3B,OAAO,MAAMK,eAAe,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACnC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG3C,QAAQ,CAAsB,IAAI,CAAC;EAC/D,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqB,KAAK,EAAEU,QAAQ,CAAC,GAAG/B,QAAQ,CAAe,IAAI,CAAC;EAEtDC,SAAS,CAAC,MAAM;IACd,MAAM2C,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACFd,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMG,IAAI,GAAG,MAAMT,iBAAiB,CAAC,CAAC;QACtCmB,SAAS,CAACV,IAAI,CAAC;MACjB,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZH,QAAQ,CAACG,GAAY,CAAC;MACxB,CAAC,SAAS;QACRJ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDc,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IAAEF,MAAM;IAAEb,OAAO;IAAER;EAAM,CAAC;AACnC,CAAC;AAACoB,GAAA,CAtBWD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}