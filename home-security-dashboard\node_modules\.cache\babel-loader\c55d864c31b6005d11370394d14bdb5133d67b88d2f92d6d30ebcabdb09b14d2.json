{"ast": null, "code": "import { disposables as f } from '../../../utils/disposables.js';\nimport { match as d } from '../../../utils/match.js';\nimport { once as s } from '../../../utils/once.js';\nfunction g(t, ...e) {\n  t && e.length > 0 && t.classList.add(...e);\n}\nfunction v(t, ...e) {\n  t && e.length > 0 && t.classList.remove(...e);\n}\nfunction b(t, e) {\n  let n = f();\n  if (!t) return n.dispose;\n  let {\n      transitionDuration: m,\n      transitionDelay: a\n    } = getComputedStyle(t),\n    [u, p] = [m, a].map(l => {\n      let [r = 0] = l.split(\",\").filter(Boolean).map(i => i.includes(\"ms\") ? parseFloat(i) : parseFloat(i) * 1e3).sort((i, T) => T - i);\n      return r;\n    }),\n    o = u + p;\n  if (o !== 0) {\n    n.group(r => {\n      r.setTimeout(() => {\n        e(), r.dispose();\n      }, o), r.addEventListener(t, \"transitionrun\", i => {\n        i.target === i.currentTarget && r.dispose();\n      });\n    });\n    let l = n.addEventListener(t, \"transitionend\", r => {\n      r.target === r.currentTarget && (e(), l());\n    });\n  } else e();\n  return n.add(() => e()), n.dispose;\n}\nfunction M(t, e, n, m) {\n  let a = n ? \"enter\" : \"leave\",\n    u = f(),\n    p = m !== void 0 ? s(m) : () => {};\n  a === \"enter\" && (t.removeAttribute(\"hidden\"), t.style.display = \"\");\n  let o = d(a, {\n      enter: () => e.enter,\n      leave: () => e.leave\n    }),\n    l = d(a, {\n      enter: () => e.enterTo,\n      leave: () => e.leaveTo\n    }),\n    r = d(a, {\n      enter: () => e.enterFrom,\n      leave: () => e.leaveFrom\n    });\n  return v(t, ...e.base, ...e.enter, ...e.enterTo, ...e.enterFrom, ...e.leave, ...e.leaveFrom, ...e.leaveTo, ...e.entered), g(t, ...e.base, ...o, ...r), u.nextFrame(() => {\n    v(t, ...e.base, ...o, ...r), g(t, ...e.base, ...o, ...l), b(t, () => (v(t, ...e.base, ...o), g(t, ...e.base, ...e.entered), p()));\n  }), u.dispose;\n}\nexport { M as transition };", "map": {"version": 3, "names": ["disposables", "f", "match", "d", "once", "s", "g", "t", "e", "length", "classList", "add", "v", "remove", "b", "n", "dispose", "transitionDuration", "m", "transitionDelay", "a", "getComputedStyle", "u", "p", "map", "l", "r", "split", "filter", "Boolean", "i", "includes", "parseFloat", "sort", "T", "o", "group", "setTimeout", "addEventListener", "target", "currentTarget", "M", "removeAttribute", "style", "display", "enter", "leave", "enterTo", "leaveTo", "enterFrom", "leaveFrom", "base", "entered", "next<PERSON><PERSON><PERSON>", "transition"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/@headlessui/react/dist/components/transitions/utils/transition.js"], "sourcesContent": ["import{disposables as f}from'../../../utils/disposables.js';import{match as d}from'../../../utils/match.js';import{once as s}from'../../../utils/once.js';function g(t,...e){t&&e.length>0&&t.classList.add(...e)}function v(t,...e){t&&e.length>0&&t.classList.remove(...e)}function b(t,e){let n=f();if(!t)return n.dispose;let{transitionDuration:m,transitionDelay:a}=getComputedStyle(t),[u,p]=[m,a].map(l=>{let[r=0]=l.split(\",\").filter(Boolean).map(i=>i.includes(\"ms\")?parseFloat(i):parseFloat(i)*1e3).sort((i,T)=>T-i);return r}),o=u+p;if(o!==0){n.group(r=>{r.setTimeout(()=>{e(),r.dispose()},o),r.addEventListener(t,\"transitionrun\",i=>{i.target===i.currentTarget&&r.dispose()})});let l=n.addEventListener(t,\"transitionend\",r=>{r.target===r.currentTarget&&(e(),l())})}else e();return n.add(()=>e()),n.dispose}function M(t,e,n,m){let a=n?\"enter\":\"leave\",u=f(),p=m!==void 0?s(m):()=>{};a===\"enter\"&&(t.removeAttribute(\"hidden\"),t.style.display=\"\");let o=d(a,{enter:()=>e.enter,leave:()=>e.leave}),l=d(a,{enter:()=>e.enterTo,leave:()=>e.leaveTo}),r=d(a,{enter:()=>e.enterFrom,leave:()=>e.leaveFrom});return v(t,...e.base,...e.enter,...e.enterTo,...e.enterFrom,...e.leave,...e.leaveFrom,...e.leaveTo,...e.entered),g(t,...e.base,...o,...r),u.nextFrame(()=>{v(t,...e.base,...o,...r),g(t,...e.base,...o,...l),b(t,()=>(v(t,...e.base,...o),g(t,...e.base,...e.entered),p()))}),u.dispose}export{M as transition};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,IAAI,IAAIC,CAAC,QAAK,wBAAwB;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC,GAAGC,CAAC,EAAC;EAACD,CAAC,IAAEC,CAAC,CAACC,MAAM,GAAC,CAAC,IAAEF,CAAC,CAACG,SAAS,CAACC,GAAG,CAAC,GAAGH,CAAC,CAAC;AAAA;AAAC,SAASI,CAACA,CAACL,CAAC,EAAC,GAAGC,CAAC,EAAC;EAACD,CAAC,IAAEC,CAAC,CAACC,MAAM,GAAC,CAAC,IAAEF,CAAC,CAACG,SAAS,CAACG,MAAM,CAAC,GAAGL,CAAC,CAAC;AAAA;AAAC,SAASM,CAACA,CAACP,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIO,CAAC,GAACd,CAAC,CAAC,CAAC;EAAC,IAAG,CAACM,CAAC,EAAC,OAAOQ,CAAC,CAACC,OAAO;EAAC,IAAG;MAACC,kBAAkB,EAACC,CAAC;MAACC,eAAe,EAACC;IAAC,CAAC,GAACC,gBAAgB,CAACd,CAAC,CAAC;IAAC,CAACe,CAAC,EAACC,CAAC,CAAC,GAAC,CAACL,CAAC,EAACE,CAAC,CAAC,CAACI,GAAG,CAACC,CAAC,IAAE;MAAC,IAAG,CAACC,CAAC,GAAC,CAAC,CAAC,GAACD,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACL,GAAG,CAACM,CAAC,IAAEA,CAAC,CAACC,QAAQ,CAAC,IAAI,CAAC,GAACC,UAAU,CAACF,CAAC,CAAC,GAACE,UAAU,CAACF,CAAC,CAAC,GAAC,GAAG,CAAC,CAACG,IAAI,CAAC,CAACH,CAAC,EAACI,CAAC,KAAGA,CAAC,GAACJ,CAAC,CAAC;MAAC,OAAOJ,CAAC;IAAA,CAAC,CAAC;IAACS,CAAC,GAACb,CAAC,GAACC,CAAC;EAAC,IAAGY,CAAC,KAAG,CAAC,EAAC;IAACpB,CAAC,CAACqB,KAAK,CAACV,CAAC,IAAE;MAACA,CAAC,CAACW,UAAU,CAAC,MAAI;QAAC7B,CAAC,CAAC,CAAC,EAACkB,CAAC,CAACV,OAAO,CAAC,CAAC;MAAA,CAAC,EAACmB,CAAC,CAAC,EAACT,CAAC,CAACY,gBAAgB,CAAC/B,CAAC,EAAC,eAAe,EAACuB,CAAC,IAAE;QAACA,CAAC,CAACS,MAAM,KAAGT,CAAC,CAACU,aAAa,IAAEd,CAAC,CAACV,OAAO,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA,CAAC,CAAC;IAAC,IAAIS,CAAC,GAACV,CAAC,CAACuB,gBAAgB,CAAC/B,CAAC,EAAC,eAAe,EAACmB,CAAC,IAAE;MAACA,CAAC,CAACa,MAAM,KAAGb,CAAC,CAACc,aAAa,KAAGhC,CAAC,CAAC,CAAC,EAACiB,CAAC,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;EAAA,CAAC,MAAKjB,CAAC,CAAC,CAAC;EAAC,OAAOO,CAAC,CAACJ,GAAG,CAAC,MAAIH,CAAC,CAAC,CAAC,CAAC,EAACO,CAAC,CAACC,OAAO;AAAA;AAAC,SAASyB,CAACA,CAAClC,CAAC,EAACC,CAAC,EAACO,CAAC,EAACG,CAAC,EAAC;EAAC,IAAIE,CAAC,GAACL,CAAC,GAAC,OAAO,GAAC,OAAO;IAACO,CAAC,GAACrB,CAAC,CAAC,CAAC;IAACsB,CAAC,GAACL,CAAC,KAAG,KAAK,CAAC,GAACb,CAAC,CAACa,CAAC,CAAC,GAAC,MAAI,CAAC,CAAC;EAACE,CAAC,KAAG,OAAO,KAAGb,CAAC,CAACmC,eAAe,CAAC,QAAQ,CAAC,EAACnC,CAAC,CAACoC,KAAK,CAACC,OAAO,GAAC,EAAE,CAAC;EAAC,IAAIT,CAAC,GAAChC,CAAC,CAACiB,CAAC,EAAC;MAACyB,KAAK,EAACA,CAAA,KAAIrC,CAAC,CAACqC,KAAK;MAACC,KAAK,EAACA,CAAA,KAAItC,CAAC,CAACsC;IAAK,CAAC,CAAC;IAACrB,CAAC,GAACtB,CAAC,CAACiB,CAAC,EAAC;MAACyB,KAAK,EAACA,CAAA,KAAIrC,CAAC,CAACuC,OAAO;MAACD,KAAK,EAACA,CAAA,KAAItC,CAAC,CAACwC;IAAO,CAAC,CAAC;IAACtB,CAAC,GAACvB,CAAC,CAACiB,CAAC,EAAC;MAACyB,KAAK,EAACA,CAAA,KAAIrC,CAAC,CAACyC,SAAS;MAACH,KAAK,EAACA,CAAA,KAAItC,CAAC,CAAC0C;IAAS,CAAC,CAAC;EAAC,OAAOtC,CAAC,CAACL,CAAC,EAAC,GAAGC,CAAC,CAAC2C,IAAI,EAAC,GAAG3C,CAAC,CAACqC,KAAK,EAAC,GAAGrC,CAAC,CAACuC,OAAO,EAAC,GAAGvC,CAAC,CAACyC,SAAS,EAAC,GAAGzC,CAAC,CAACsC,KAAK,EAAC,GAAGtC,CAAC,CAAC0C,SAAS,EAAC,GAAG1C,CAAC,CAACwC,OAAO,EAAC,GAAGxC,CAAC,CAAC4C,OAAO,CAAC,EAAC9C,CAAC,CAACC,CAAC,EAAC,GAAGC,CAAC,CAAC2C,IAAI,EAAC,GAAGhB,CAAC,EAAC,GAAGT,CAAC,CAAC,EAACJ,CAAC,CAAC+B,SAAS,CAAC,MAAI;IAACzC,CAAC,CAACL,CAAC,EAAC,GAAGC,CAAC,CAAC2C,IAAI,EAAC,GAAGhB,CAAC,EAAC,GAAGT,CAAC,CAAC,EAACpB,CAAC,CAACC,CAAC,EAAC,GAAGC,CAAC,CAAC2C,IAAI,EAAC,GAAGhB,CAAC,EAAC,GAAGV,CAAC,CAAC,EAACX,CAAC,CAACP,CAAC,EAAC,OAAKK,CAAC,CAACL,CAAC,EAAC,GAAGC,CAAC,CAAC2C,IAAI,EAAC,GAAGhB,CAAC,CAAC,EAAC7B,CAAC,CAACC,CAAC,EAAC,GAAGC,CAAC,CAAC2C,IAAI,EAAC,GAAG3C,CAAC,CAAC4C,OAAO,CAAC,EAAC7B,CAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAACD,CAAC,CAACN,OAAO;AAAA;AAAC,SAAOyB,CAAC,IAAIa,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}