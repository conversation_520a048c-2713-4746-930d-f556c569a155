{"ast": null, "code": "function convertOffsetToTimes(offset, duration) {\n  return offset.map(o => o * duration);\n}\nexport { convertOffsetToTimes };", "map": {"version": 3, "names": ["convertOffsetToTimes", "offset", "duration", "map", "o"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/framer-motion/dist/es/utils/offsets/time.mjs"], "sourcesContent": ["function convertOffsetToTimes(offset, duration) {\n    return offset.map((o) => o * duration);\n}\n\nexport { convertOffsetToTimes };\n"], "mappings": "AAAA,SAASA,oBAAoBA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC5C,OAAOD,MAAM,CAACE,GAAG,CAAEC,CAAC,IAAKA,CAAC,GAAGF,QAAQ,CAAC;AAC1C;AAEA,SAASF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}