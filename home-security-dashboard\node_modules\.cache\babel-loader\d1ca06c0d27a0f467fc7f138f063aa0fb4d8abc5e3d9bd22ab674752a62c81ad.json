{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getMinutes} function options.\n */\n\n/**\n * @name getMinutes\n * @category Minute Helpers\n * @summary Get the minutes of the given date.\n *\n * @description\n * Get the minutes of the given date.\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The minutes\n *\n * @example\n * // Get the minutes of 29 February 2012 11:45:05:\n * const result = getMinutes(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 45\n */\nexport function getMinutes(date, options) {\n  return toDate(date, options?.in).getMinutes();\n}\n\n// Fallback for modularized imports:\nexport default getMinutes;", "map": {"version": 3, "names": ["toDate", "getMinutes", "date", "options", "in"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/getMinutes.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getMinutes} function options.\n */\n\n/**\n * @name getMinutes\n * @category Minute Helpers\n * @summary Get the minutes of the given date.\n *\n * @description\n * Get the minutes of the given date.\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The minutes\n *\n * @example\n * // Get the minutes of 29 February 2012 11:45:05:\n * const result = getMinutes(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 45\n */\nexport function getMinutes(date, options) {\n  return toDate(date, options?.in).getMinutes();\n}\n\n// Fallback for modularized imports:\nexport default getMinutes;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACxC,OAAOH,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC,CAACH,UAAU,CAAC,CAAC;AAC/C;;AAEA;AACA,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}