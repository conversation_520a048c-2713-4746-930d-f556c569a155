{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDate} function options.\n */\n\n/**\n * @name getDate\n * @category Day Helpers\n * @summary Get the day of the month of the given date.\n *\n * @description\n * Get the day of the month of the given date.\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The day of month\n *\n * @example\n * // Which day of the month is 29 February 2012?\n * const result = getDate(new Date(2012, 1, 29))\n * //=> 29\n */\nexport function getDate(date, options) {\n  return toDate(date, options?.in).getDate();\n}\n\n// Fallback for modularized imports:\nexport default getDate;", "map": {"version": 3, "names": ["toDate", "getDate", "date", "options", "in"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/getDate.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDate} function options.\n */\n\n/**\n * @name getDate\n * @category Day Helpers\n * @summary Get the day of the month of the given date.\n *\n * @description\n * Get the day of the month of the given date.\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The day of month\n *\n * @example\n * // Which day of the month is 29 February 2012?\n * const result = getDate(new Date(2012, 1, 29))\n * //=> 29\n */\nexport function getDate(date, options) {\n  return toDate(date, options?.in).getDate();\n}\n\n// Fallback for modularized imports:\nexport default getDate;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACrC,OAAOH,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEC,EAAE,CAAC,CAACH,OAAO,CAAC,CAAC;AAC5C;;AAEA;AACA,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}