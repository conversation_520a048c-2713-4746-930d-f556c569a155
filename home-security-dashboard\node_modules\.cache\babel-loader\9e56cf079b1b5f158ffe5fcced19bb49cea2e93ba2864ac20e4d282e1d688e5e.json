{"ast": null, "code": "import { easeInOut } from '../../easing/ease.mjs';\nimport { isEasingArray } from '../../easing/utils/is-easing-array.mjs';\nimport { easingDefinitionToFunction } from '../../easing/utils/map.mjs';\nimport { interpolate } from '../../utils/interpolate.mjs';\nimport { defaultOffset } from '../../utils/offsets/default.mjs';\nimport { convertOffsetToTimes } from '../../utils/offsets/time.mjs';\nfunction defaultEasing(values, easing) {\n  return values.map(() => easing || easeInOut).splice(0, values.length - 1);\n}\nfunction keyframes({\n  duration = 300,\n  keyframes: keyframeValues,\n  times,\n  ease = \"easeInOut\"\n}) {\n  /**\n   * Easing functions can be externally defined as strings. Here we convert them\n   * into actual functions.\n   */\n  const easingFunctions = isEasingArray(ease) ? ease.map(easingDefinitionToFunction) : easingDefinitionToFunction(ease);\n  /**\n   * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n   * to reduce GC during animation.\n   */\n  const state = {\n    done: false,\n    value: keyframeValues[0]\n  };\n  /**\n   * Create a times array based on the provided 0-1 offsets\n   */\n  const absoluteTimes = convertOffsetToTimes(\n  // Only use the provided offsets if they're the correct length\n  // TODO Maybe we should warn here if there's a length mismatch\n  times && times.length === keyframeValues.length ? times : defaultOffset(keyframeValues), duration);\n  const mapTimeToKeyframe = interpolate(absoluteTimes, keyframeValues, {\n    ease: Array.isArray(easingFunctions) ? easingFunctions : defaultEasing(keyframeValues, easingFunctions)\n  });\n  return {\n    calculatedDuration: duration,\n    next: t => {\n      state.value = mapTimeToKeyframe(t);\n      state.done = t >= duration;\n      return state;\n    }\n  };\n}\nexport { defaultEasing, keyframes };", "map": {"version": 3, "names": ["easeInOut", "isEasingArray", "easingDefinitionToFunction", "interpolate", "defaultOffset", "convertOffsetToTimes", "defaultEasing", "values", "easing", "map", "splice", "length", "keyframes", "duration", "keyframeValues", "times", "ease", "easingFunctions", "state", "done", "value", "absoluteTimes", "mapTimeToKeyframe", "Array", "isArray", "calculatedDuration", "next", "t"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/framer-motion/dist/es/animation/generators/keyframes.mjs"], "sourcesContent": ["import { easeInOut } from '../../easing/ease.mjs';\nimport { isEasingArray } from '../../easing/utils/is-easing-array.mjs';\nimport { easingDefinitionToFunction } from '../../easing/utils/map.mjs';\nimport { interpolate } from '../../utils/interpolate.mjs';\nimport { defaultOffset } from '../../utils/offsets/default.mjs';\nimport { convertOffsetToTimes } from '../../utils/offsets/time.mjs';\n\nfunction defaultEasing(values, easing) {\n    return values.map(() => easing || easeInOut).splice(0, values.length - 1);\n}\nfunction keyframes({ duration = 300, keyframes: keyframeValues, times, ease = \"easeInOut\", }) {\n    /**\n     * Easing functions can be externally defined as strings. Here we convert them\n     * into actual functions.\n     */\n    const easingFunctions = isEasingArray(ease)\n        ? ease.map(easingDefinitionToFunction)\n        : easingDefinitionToFunction(ease);\n    /**\n     * This is the Iterator-spec return value. We ensure it's mutable rather than using a generator\n     * to reduce GC during animation.\n     */\n    const state = {\n        done: false,\n        value: keyframeValues[0],\n    };\n    /**\n     * Create a times array based on the provided 0-1 offsets\n     */\n    const absoluteTimes = convertOffsetToTimes(\n    // Only use the provided offsets if they're the correct length\n    // TODO Maybe we should warn here if there's a length mismatch\n    times && times.length === keyframeValues.length\n        ? times\n        : defaultOffset(keyframeValues), duration);\n    const mapTimeToKeyframe = interpolate(absoluteTimes, keyframeValues, {\n        ease: Array.isArray(easingFunctions)\n            ? easingFunctions\n            : defaultEasing(keyframeValues, easingFunctions),\n    });\n    return {\n        calculatedDuration: duration,\n        next: (t) => {\n            state.value = mapTimeToKeyframe(t);\n            state.done = t >= duration;\n            return state;\n        },\n    };\n}\n\nexport { defaultEasing, keyframes };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,uBAAuB;AACjD,SAASC,aAAa,QAAQ,wCAAwC;AACtE,SAASC,0BAA0B,QAAQ,4BAA4B;AACvE,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,oBAAoB,QAAQ,8BAA8B;AAEnE,SAASC,aAAaA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACnC,OAAOD,MAAM,CAACE,GAAG,CAAC,MAAMD,MAAM,IAAIR,SAAS,CAAC,CAACU,MAAM,CAAC,CAAC,EAAEH,MAAM,CAACI,MAAM,GAAG,CAAC,CAAC;AAC7E;AACA,SAASC,SAASA,CAAC;EAAEC,QAAQ,GAAG,GAAG;EAAED,SAAS,EAAEE,cAAc;EAAEC,KAAK;EAAEC,IAAI,GAAG;AAAa,CAAC,EAAE;EAC1F;AACJ;AACA;AACA;EACI,MAAMC,eAAe,GAAGhB,aAAa,CAACe,IAAI,CAAC,GACrCA,IAAI,CAACP,GAAG,CAACP,0BAA0B,CAAC,GACpCA,0BAA0B,CAACc,IAAI,CAAC;EACtC;AACJ;AACA;AACA;EACI,MAAME,KAAK,GAAG;IACVC,IAAI,EAAE,KAAK;IACXC,KAAK,EAAEN,cAAc,CAAC,CAAC;EAC3B,CAAC;EACD;AACJ;AACA;EACI,MAAMO,aAAa,GAAGhB,oBAAoB;EAC1C;EACA;EACAU,KAAK,IAAIA,KAAK,CAACJ,MAAM,KAAKG,cAAc,CAACH,MAAM,GACzCI,KAAK,GACLX,aAAa,CAACU,cAAc,CAAC,EAAED,QAAQ,CAAC;EAC9C,MAAMS,iBAAiB,GAAGnB,WAAW,CAACkB,aAAa,EAAEP,cAAc,EAAE;IACjEE,IAAI,EAAEO,KAAK,CAACC,OAAO,CAACP,eAAe,CAAC,GAC9BA,eAAe,GACfX,aAAa,CAACQ,cAAc,EAAEG,eAAe;EACvD,CAAC,CAAC;EACF,OAAO;IACHQ,kBAAkB,EAAEZ,QAAQ;IAC5Ba,IAAI,EAAGC,CAAC,IAAK;MACTT,KAAK,CAACE,KAAK,GAAGE,iBAAiB,CAACK,CAAC,CAAC;MAClCT,KAAK,CAACC,IAAI,GAAGQ,CAAC,IAAId,QAAQ;MAC1B,OAAOK,KAAK;IAChB;EACJ,CAAC;AACL;AAEA,SAASZ,aAAa,EAAEM,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}