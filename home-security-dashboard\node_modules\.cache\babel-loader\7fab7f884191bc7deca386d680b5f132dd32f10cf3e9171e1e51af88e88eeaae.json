{"ast": null, "code": "import l, { createContext as A, Fragment as H, useContext as F, useEffect as M, useMemo as P, useRef as U, useState as I } from \"react\";\nimport { useControllable as K } from '../../hooks/use-controllable.js';\nimport { useDisposables as B } from '../../hooks/use-disposables.js';\nimport { useEvent as h } from '../../hooks/use-event.js';\nimport { useId as O } from '../../hooks/use-id.js';\nimport { useResolveButtonType as W } from '../../hooks/use-resolve-button-type.js';\nimport { useSyncRefs as N } from '../../hooks/use-sync-refs.js';\nimport { Features as J, Hidden as X } from '../../internal/hidden.js';\nimport { isDisabledReactIssue7711 as j } from '../../utils/bugs.js';\nimport { attemptSubmit as $ } from '../../utils/form.js';\nimport { compact as q, forwardRefWithAs as z, render as g } from '../../utils/render.js';\nimport { Description as Q, useDescriptions as V } from '../description/description.js';\nimport { Keys as D } from '../keyboard.js';\nimport { Label as Y, useLabels as Z } from '../label/label.js';\nlet S = A(null);\nS.displayName = \"GroupContext\";\nlet ee = H;\nfunction te(r) {\n  var u;\n  let [n, p] = I(null),\n    [c, T] = Z(),\n    [o, b] = V(),\n    a = P(() => ({\n      switch: n,\n      setSwitch: p,\n      labelledby: c,\n      describedby: o\n    }), [n, p, c, o]),\n    d = {},\n    y = r;\n  return l.createElement(b, {\n    name: \"Switch.Description\"\n  }, l.createElement(T, {\n    name: \"Switch.Label\",\n    props: {\n      htmlFor: (u = a.switch) == null ? void 0 : u.id,\n      onClick(m) {\n        n && (m.currentTarget.tagName === \"LABEL\" && m.preventDefault(), n.click(), n.focus({\n          preventScroll: !0\n        }));\n      }\n    }\n  }, l.createElement(S.Provider, {\n    value: a\n  }, g({\n    ourProps: d,\n    theirProps: y,\n    defaultTag: ee,\n    name: \"Switch.Group\"\n  }))));\n}\nlet ne = \"button\";\nfunction re(r, n) {\n  var E;\n  let p = O(),\n    {\n      id: c = `headlessui-switch-${p}`,\n      checked: T,\n      defaultChecked: o = !1,\n      onChange: b,\n      disabled: a = !1,\n      name: d,\n      value: y,\n      form: u,\n      ...m\n    } = r,\n    t = F(S),\n    f = U(null),\n    C = N(f, n, t === null ? null : t.setSwitch),\n    [i, s] = K(T, b, o),\n    w = h(() => s == null ? void 0 : s(!i)),\n    L = h(e => {\n      if (j(e.currentTarget)) return e.preventDefault();\n      e.preventDefault(), w();\n    }),\n    x = h(e => {\n      e.key === D.Space ? (e.preventDefault(), w()) : e.key === D.Enter && $(e.currentTarget);\n    }),\n    v = h(e => e.preventDefault()),\n    G = P(() => ({\n      checked: i\n    }), [i]),\n    R = {\n      id: c,\n      ref: C,\n      role: \"switch\",\n      type: W(r, f),\n      tabIndex: r.tabIndex === -1 ? 0 : (E = r.tabIndex) != null ? E : 0,\n      \"aria-checked\": i,\n      \"aria-labelledby\": t == null ? void 0 : t.labelledby,\n      \"aria-describedby\": t == null ? void 0 : t.describedby,\n      disabled: a,\n      onClick: L,\n      onKeyUp: x,\n      onKeyPress: v\n    },\n    k = B();\n  return M(() => {\n    var _;\n    let e = (_ = f.current) == null ? void 0 : _.closest(\"form\");\n    e && o !== void 0 && k.addEventListener(e, \"reset\", () => {\n      s(o);\n    });\n  }, [f, s]), l.createElement(l.Fragment, null, d != null && i && l.createElement(X, {\n    features: J.Hidden,\n    ...q({\n      as: \"input\",\n      type: \"checkbox\",\n      hidden: !0,\n      readOnly: !0,\n      disabled: a,\n      form: u,\n      checked: i,\n      name: d,\n      value: y\n    })\n  }), g({\n    ourProps: R,\n    theirProps: m,\n    slot: G,\n    defaultTag: ne,\n    name: \"Switch\"\n  }));\n}\nlet oe = z(re),\n  ie = te,\n  _e = Object.assign(oe, {\n    Group: ie,\n    Label: Y,\n    Description: Q\n  });\nexport { _e as Switch };", "map": {"version": 3, "names": ["l", "createContext", "A", "Fragment", "H", "useContext", "F", "useEffect", "M", "useMemo", "P", "useRef", "U", "useState", "I", "useControllable", "K", "useDisposables", "B", "useEvent", "h", "useId", "O", "useResolveButtonType", "W", "useSyncRefs", "N", "Features", "J", "Hidden", "X", "isDisabledReactIssue7711", "j", "attemptSubmit", "$", "compact", "q", "forwardRefWithAs", "z", "render", "g", "Description", "Q", "useDescriptions", "V", "Keys", "D", "Label", "Y", "useLabels", "Z", "S", "displayName", "ee", "te", "r", "u", "n", "p", "c", "T", "o", "b", "a", "switch", "setSwitch", "<PERSON>by", "<PERSON><PERSON>", "d", "y", "createElement", "name", "props", "htmlFor", "id", "onClick", "m", "currentTarget", "tagName", "preventDefault", "click", "focus", "preventScroll", "Provider", "value", "ourProps", "theirProps", "defaultTag", "ne", "re", "E", "checked", "defaultChecked", "onChange", "disabled", "form", "t", "f", "C", "i", "s", "w", "L", "e", "x", "key", "Space", "Enter", "v", "G", "R", "ref", "role", "type", "tabIndex", "onKeyUp", "onKeyPress", "k", "_", "current", "closest", "addEventListener", "features", "as", "hidden", "readOnly", "slot", "oe", "ie", "_e", "Object", "assign", "Group", "Switch"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/@headlessui/react/dist/components/switch/switch.js"], "sourcesContent": ["import l,{createContext as A,Fragment as H,useContext as F,useEffect as M,useMemo as P,useRef as U,useState as I}from\"react\";import{useControllable as K}from'../../hooks/use-controllable.js';import{useDisposables as B}from'../../hooks/use-disposables.js';import{useEvent as h}from'../../hooks/use-event.js';import{useId as O}from'../../hooks/use-id.js';import{useResolveButtonType as W}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as N}from'../../hooks/use-sync-refs.js';import{Features as J,Hidden as X}from'../../internal/hidden.js';import{isDisabledReactIssue7711 as j}from'../../utils/bugs.js';import{attemptSubmit as $}from'../../utils/form.js';import{compact as q,forwardRefWithAs as z,render as g}from'../../utils/render.js';import{Description as Q,useDescriptions as V}from'../description/description.js';import{Keys as D}from'../keyboard.js';import{Label as Y,useLabels as Z}from'../label/label.js';let S=A(null);S.displayName=\"GroupContext\";let ee=H;function te(r){var u;let[n,p]=I(null),[c,T]=Z(),[o,b]=V(),a=P(()=>({switch:n,setSwitch:p,labelledby:c,describedby:o}),[n,p,c,o]),d={},y=r;return l.createElement(b,{name:\"Switch.Description\"},l.createElement(T,{name:\"Switch.Label\",props:{htmlFor:(u=a.switch)==null?void 0:u.id,onClick(m){n&&(m.currentTarget.tagName===\"LABEL\"&&m.preventDefault(),n.click(),n.focus({preventScroll:!0}))}}},l.createElement(S.Provider,{value:a},g({ourProps:d,theirProps:y,defaultTag:ee,name:\"Switch.Group\"}))))}let ne=\"button\";function re(r,n){var E;let p=O(),{id:c=`headlessui-switch-${p}`,checked:T,defaultChecked:o=!1,onChange:b,disabled:a=!1,name:d,value:y,form:u,...m}=r,t=F(S),f=U(null),C=N(f,n,t===null?null:t.setSwitch),[i,s]=K(T,b,o),w=h(()=>s==null?void 0:s(!i)),L=h(e=>{if(j(e.currentTarget))return e.preventDefault();e.preventDefault(),w()}),x=h(e=>{e.key===D.Space?(e.preventDefault(),w()):e.key===D.Enter&&$(e.currentTarget)}),v=h(e=>e.preventDefault()),G=P(()=>({checked:i}),[i]),R={id:c,ref:C,role:\"switch\",type:W(r,f),tabIndex:r.tabIndex===-1?0:(E=r.tabIndex)!=null?E:0,\"aria-checked\":i,\"aria-labelledby\":t==null?void 0:t.labelledby,\"aria-describedby\":t==null?void 0:t.describedby,disabled:a,onClick:L,onKeyUp:x,onKeyPress:v},k=B();return M(()=>{var _;let e=(_=f.current)==null?void 0:_.closest(\"form\");e&&o!==void 0&&k.addEventListener(e,\"reset\",()=>{s(o)})},[f,s]),l.createElement(l.Fragment,null,d!=null&&i&&l.createElement(X,{features:J.Hidden,...q({as:\"input\",type:\"checkbox\",hidden:!0,readOnly:!0,disabled:a,form:u,checked:i,name:d,value:y})}),g({ourProps:R,theirProps:m,slot:G,defaultTag:ne,name:\"Switch\"}))}let oe=z(re),ie=te,_e=Object.assign(oe,{Group:ie,Label:Y,Description:Q});export{_e as Switch};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,eAAe,IAAIC,CAAC,QAAK,iCAAiC;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,gCAAgC;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,oBAAoB,IAAIC,CAAC,QAAK,wCAAwC;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,wBAAwB,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAAOC,OAAO,IAAIC,CAAC,EAACC,gBAAgB,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,WAAW,IAAIC,CAAC,EAACC,eAAe,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,IAAI,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAAOC,KAAK,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,QAAK,mBAAmB;AAAC,IAAIC,CAAC,GAACjD,CAAC,CAAC,IAAI,CAAC;AAACiD,CAAC,CAACC,WAAW,GAAC,cAAc;AAAC,IAAIC,EAAE,GAACjD,CAAC;AAAC,SAASkD,EAAEA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC;EAAC,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,GAAC5C,CAAC,CAAC,IAAI,CAAC;IAAC,CAAC6C,CAAC,EAACC,CAAC,CAAC,GAACV,CAAC,CAAC,CAAC;IAAC,CAACW,CAAC,EAACC,CAAC,CAAC,GAAClB,CAAC,CAAC,CAAC;IAACmB,CAAC,GAACrD,CAAC,CAAC,OAAK;MAACsD,MAAM,EAACP,CAAC;MAACQ,SAAS,EAACP,CAAC;MAACQ,UAAU,EAACP,CAAC;MAACQ,WAAW,EAACN;IAAC,CAAC,CAAC,EAAC,CAACJ,CAAC,EAACC,CAAC,EAACC,CAAC,EAACE,CAAC,CAAC,CAAC;IAACO,CAAC,GAAC,CAAC,CAAC;IAACC,CAAC,GAACd,CAAC;EAAC,OAAOvD,CAAC,CAACsE,aAAa,CAACR,CAAC,EAAC;IAACS,IAAI,EAAC;EAAoB,CAAC,EAACvE,CAAC,CAACsE,aAAa,CAACV,CAAC,EAAC;IAACW,IAAI,EAAC,cAAc;IAACC,KAAK,EAAC;MAACC,OAAO,EAAC,CAACjB,CAAC,GAACO,CAAC,CAACC,MAAM,KAAG,IAAI,GAAC,KAAK,CAAC,GAACR,CAAC,CAACkB,EAAE;MAACC,OAAOA,CAACC,CAAC,EAAC;QAACnB,CAAC,KAAGmB,CAAC,CAACC,aAAa,CAACC,OAAO,KAAG,OAAO,IAAEF,CAAC,CAACG,cAAc,CAAC,CAAC,EAACtB,CAAC,CAACuB,KAAK,CAAC,CAAC,EAACvB,CAAC,CAACwB,KAAK,CAAC;UAACC,aAAa,EAAC,CAAC;QAAC,CAAC,CAAC,CAAC;MAAA;IAAC;EAAC,CAAC,EAAClF,CAAC,CAACsE,aAAa,CAACnB,CAAC,CAACgC,QAAQ,EAAC;IAACC,KAAK,EAACrB;EAAC,CAAC,EAACvB,CAAC,CAAC;IAAC6C,QAAQ,EAACjB,CAAC;IAACkB,UAAU,EAACjB,CAAC;IAACkB,UAAU,EAAClC,EAAE;IAACkB,IAAI,EAAC;EAAc,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIiB,EAAE,GAAC,QAAQ;AAAC,SAASC,EAAEA,CAAClC,CAAC,EAACE,CAAC,EAAC;EAAC,IAAIiC,CAAC;EAAC,IAAIhC,CAAC,GAACpC,CAAC,CAAC,CAAC;IAAC;MAACoD,EAAE,EAACf,CAAC,GAAC,qBAAqBD,CAAC,EAAE;MAACiC,OAAO,EAAC/B,CAAC;MAACgC,cAAc,EAAC/B,CAAC,GAAC,CAAC,CAAC;MAACgC,QAAQ,EAAC/B,CAAC;MAACgC,QAAQ,EAAC/B,CAAC,GAAC,CAAC,CAAC;MAACQ,IAAI,EAACH,CAAC;MAACgB,KAAK,EAACf,CAAC;MAAC0B,IAAI,EAACvC,CAAC;MAAC,GAAGoB;IAAC,CAAC,GAACrB,CAAC;IAACyC,CAAC,GAAC1F,CAAC,CAAC6C,CAAC,CAAC;IAAC8C,CAAC,GAACrF,CAAC,CAAC,IAAI,CAAC;IAACsF,CAAC,GAACxE,CAAC,CAACuE,CAAC,EAACxC,CAAC,EAACuC,CAAC,KAAG,IAAI,GAAC,IAAI,GAACA,CAAC,CAAC/B,SAAS,CAAC;IAAC,CAACkC,CAAC,EAACC,CAAC,CAAC,GAACpF,CAAC,CAAC4C,CAAC,EAACE,CAAC,EAACD,CAAC,CAAC;IAACwC,CAAC,GAACjF,CAAC,CAAC,MAAIgF,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC,CAACD,CAAC,CAAC,CAAC;IAACG,CAAC,GAAClF,CAAC,CAACmF,CAAC,IAAE;MAAC,IAAGvE,CAAC,CAACuE,CAAC,CAAC1B,aAAa,CAAC,EAAC,OAAO0B,CAAC,CAACxB,cAAc,CAAC,CAAC;MAACwB,CAAC,CAACxB,cAAc,CAAC,CAAC,EAACsB,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACG,CAAC,GAACpF,CAAC,CAACmF,CAAC,IAAE;MAACA,CAAC,CAACE,GAAG,KAAG3D,CAAC,CAAC4D,KAAK,IAAEH,CAAC,CAACxB,cAAc,CAAC,CAAC,EAACsB,CAAC,CAAC,CAAC,IAAEE,CAAC,CAACE,GAAG,KAAG3D,CAAC,CAAC6D,KAAK,IAAEzE,CAAC,CAACqE,CAAC,CAAC1B,aAAa,CAAC;IAAA,CAAC,CAAC;IAAC+B,CAAC,GAACxF,CAAC,CAACmF,CAAC,IAAEA,CAAC,CAACxB,cAAc,CAAC,CAAC,CAAC;IAAC8B,CAAC,GAACnG,CAAC,CAAC,OAAK;MAACiF,OAAO,EAACQ;IAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;IAACW,CAAC,GAAC;MAACpC,EAAE,EAACf,CAAC;MAACoD,GAAG,EAACb,CAAC;MAACc,IAAI,EAAC,QAAQ;MAACC,IAAI,EAACzF,CAAC,CAAC+B,CAAC,EAAC0C,CAAC,CAAC;MAACiB,QAAQ,EAAC3D,CAAC,CAAC2D,QAAQ,KAAG,CAAC,CAAC,GAAC,CAAC,GAAC,CAACxB,CAAC,GAACnC,CAAC,CAAC2D,QAAQ,KAAG,IAAI,GAACxB,CAAC,GAAC,CAAC;MAAC,cAAc,EAACS,CAAC;MAAC,iBAAiB,EAACH,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC9B,UAAU;MAAC,kBAAkB,EAAC8B,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC7B,WAAW;MAAC2B,QAAQ,EAAC/B,CAAC;MAACY,OAAO,EAAC2B,CAAC;MAACa,OAAO,EAACX,CAAC;MAACY,UAAU,EAACR;IAAC,CAAC;IAACS,CAAC,GAACnG,CAAC,CAAC,CAAC;EAAC,OAAOV,CAAC,CAAC,MAAI;IAAC,IAAI8G,CAAC;IAAC,IAAIf,CAAC,GAAC,CAACe,CAAC,GAACrB,CAAC,CAACsB,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACD,CAAC,CAACE,OAAO,CAAC,MAAM,CAAC;IAACjB,CAAC,IAAE1C,CAAC,KAAG,KAAK,CAAC,IAAEwD,CAAC,CAACI,gBAAgB,CAAClB,CAAC,EAAC,OAAO,EAAC,MAAI;MAACH,CAAC,CAACvC,CAAC,CAAC;IAAA,CAAC,CAAC;EAAA,CAAC,EAAC,CAACoC,CAAC,EAACG,CAAC,CAAC,CAAC,EAACpG,CAAC,CAACsE,aAAa,CAACtE,CAAC,CAACG,QAAQ,EAAC,IAAI,EAACiE,CAAC,IAAE,IAAI,IAAE+B,CAAC,IAAEnG,CAAC,CAACsE,aAAa,CAACxC,CAAC,EAAC;IAAC4F,QAAQ,EAAC9F,CAAC,CAACC,MAAM;IAAC,GAAGO,CAAC,CAAC;MAACuF,EAAE,EAAC,OAAO;MAACV,IAAI,EAAC,UAAU;MAACW,MAAM,EAAC,CAAC,CAAC;MAACC,QAAQ,EAAC,CAAC,CAAC;MAAC/B,QAAQ,EAAC/B,CAAC;MAACgC,IAAI,EAACvC,CAAC;MAACmC,OAAO,EAACQ,CAAC;MAAC5B,IAAI,EAACH,CAAC;MAACgB,KAAK,EAACf;IAAC,CAAC;EAAC,CAAC,CAAC,EAAC7B,CAAC,CAAC;IAAC6C,QAAQ,EAACyB,CAAC;IAACxB,UAAU,EAACV,CAAC;IAACkD,IAAI,EAACjB,CAAC;IAACtB,UAAU,EAACC,EAAE;IAACjB,IAAI,EAAC;EAAQ,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIwD,EAAE,GAACzF,CAAC,CAACmD,EAAE,CAAC;EAACuC,EAAE,GAAC1E,EAAE;EAAC2E,EAAE,GAACC,MAAM,CAACC,MAAM,CAACJ,EAAE,EAAC;IAACK,KAAK,EAACJ,EAAE;IAACjF,KAAK,EAACC,CAAC;IAACP,WAAW,EAACC;EAAC,CAAC,CAAC;AAAC,SAAOuF,EAAE,IAAII,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}