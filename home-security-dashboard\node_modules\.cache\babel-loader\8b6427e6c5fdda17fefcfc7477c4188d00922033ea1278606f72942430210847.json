{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\Resonance-KLE\\\\home-security-dashboard\\\\src\\\\components\\\\layout\\\\Layout.tsx\";\nimport React from 'react';\nimport { Sidebar } from './Sidebar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Layout = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-950 flex\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"flex-1 overflow-auto\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 9\n  }, this);\n};\n_c = Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "Sidebar", "jsxDEV", "_jsxDEV", "Layout", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/src/components/layout/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport { Sidebar } from './Sidebar';\n\ninterface LayoutProps {\n    children: React.ReactNode;\n}\n\nexport const Layout: React.FC<LayoutProps> = ({ children }) => {\n    return (\n        <div className=\"min-h-screen bg-gray-950 flex\">\n            <Sidebar />\n            <main className=\"flex-1 overflow-auto\">\n                {children}\n            </main>\n        </div>\n    );\n};"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMpC,OAAO,MAAMC,MAA6B,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC3D,oBACIF,OAAA;IAAKG,SAAS,EAAC,+BAA+B;IAAAD,QAAA,gBAC1CF,OAAA,CAACF,OAAO;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXP,OAAA;MAAMG,SAAS,EAAC,sBAAsB;MAAAD,QAAA,EACjCA;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd,CAAC;AAACC,EAAA,GATWP,MAA6B;AAAA,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}