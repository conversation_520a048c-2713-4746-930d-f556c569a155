{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Volume1 = createLucideIcon(\"Volume1\", [[\"polygon\", {\n  points: \"11 5 6 9 2 9 2 15 6 15 11 19 11 5\",\n  key: \"16drj5\"\n}], [\"path\", {\n  d: \"M15.54 8.46a5 5 0 0 1 0 7.07\",\n  key: \"ltjumu\"\n}]]);\nexport { Volume1 as default };", "map": {"version": 3, "names": ["Volume1", "createLucideIcon", "points", "key", "d"], "sources": ["E:\\code\\Resonance-KLE\\home-security-dashboard\\node_modules\\lucide-react\\src\\icons\\volume-1.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Volume1\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjExIDUgNiA5IDIgOSAyIDE1IDYgMTUgMTEgMTkgMTEgNSIgLz4KICA8cGF0aCBkPSJNMTUuNTQgOC40NmE1IDUgMCAwIDEgMCA3LjA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/volume-1\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Volume1 = createLucideIcon('Volume1', [\n  ['polygon', { points: '11 5 6 9 2 9 2 15 6 15 11 19 11 5', key: '16drj5' }],\n  ['path', { d: 'M15.54 8.46a5 5 0 0 1 0 7.07', key: 'ltjumu' }],\n]);\n\nexport default Volume1;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,SAAW;EAAEC,MAAA,EAAQ,mCAAqC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAEC,CAAA,EAAG,8BAAgC;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}