{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\Resonance-KLE\\\\home-security-dashboard\\\\src\\\\components\\\\dashboard\\\\AlertCard.tsx\";\nimport React from 'react';\nimport { AlertTriangle, Shield, Bell } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const AlertCard = ({\n  alert\n}) => {\n  const alerts = [{\n    type: 'intrusion',\n    count: 0,\n    description: 'No intrusions detected',\n    icon: AlertTriangle,\n    color: 'text-red-500'\n  }, {\n    type: 'animal',\n    count: 2,\n    description: 'Pet movements detected',\n    icon: Shield,\n    color: 'text-yellow-500'\n  }, {\n    type: 'object',\n    count: 1,\n    description: 'Object displacement',\n    icon: Bell,\n    color: 'text-blue-500'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-900 rounded-xl p-6 shadow-lg\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-white\",\n        children: \"Alerts Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center text-green-400\",\n        children: [/*#__PURE__*/_jsxDEV(Shield, {\n          className: \"w-5 h-5 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm\",\n          children: \"System Secure\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-3 gap-4\",\n      children: alerts.map(alert => {\n        const Icon = alert.icon;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 rounded-lg p-4 flex flex-col items-center transition-transform hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            className: `w-8 h-8 mb-2 ${alert.color}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-2xl font-bold mb-2 ${alert.count > 0 ? alert.color : 'text-green-500'}`,\n            children: alert.count\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-400 text-center\",\n            children: alert.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 15\n          }, this)]\n        }, alert.type, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = AlertCard;\nexport default AlertCard;\nvar _c;\n$RefreshReg$(_c, \"AlertCard\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shield", "Bell", "jsxDEV", "_jsxDEV", "AlertCard", "alert", "alerts", "type", "count", "description", "icon", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "Icon", "_c", "$RefreshReg$"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/src/components/dashboard/AlertCard.tsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Bell } from 'lucide-react';\nimport { Alert } from '../../types';\n\ninterface AlertCardProps {\n  alert?: Alert;\n}\n\nexport const AlertCard: React.FC<AlertCardProps> = ({ alert }) => {\n  const alerts = [\n    { type: 'intrusion', count: 0, description: 'No intrusions detected', icon: AlertTriangle, color: 'text-red-500' },\n    { type: 'animal', count: 2, description: 'Pet movements detected', icon: Shield, color: 'text-yellow-500' },\n    { type: 'object', count: 1, description: 'Object displacement', icon: Bell, color: 'text-blue-500' }\n  ];\n\n  return (\n    <div className=\"bg-gray-900 rounded-xl p-6 shadow-lg\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-xl font-semibold text-white\">Alerts Overview</h2>\n        <div className=\"flex items-center text-green-400\">\n          <Shield className=\"w-5 h-5 mr-2\" />\n          <span className=\"text-sm\">System Secure</span>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-3 gap-4\">\n        {alerts.map((alert) => {\n          const Icon = alert.icon;\n          return (\n            <div\n              key={alert.type}\n              className=\"bg-gray-800 rounded-lg p-4 flex flex-col items-center transition-transform hover:scale-105\"\n            >\n              <Icon className={`w-8 h-8 mb-2 ${alert.color}`} />\n              <div className={`text-2xl font-bold mb-2 ${\n                alert.count > 0 ? alert.color : 'text-green-500'\n              }`}>\n                {alert.count}\n              </div>\n              <div className=\"text-sm text-gray-400 text-center\">\n                {alert.description}\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default AlertCard;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,MAAM,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO3D,OAAO,MAAMC,SAAmC,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAChE,MAAMC,MAAM,GAAG,CACb;IAAEC,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE,CAAC;IAAEC,WAAW,EAAE,wBAAwB;IAAEC,IAAI,EAAEX,aAAa;IAAEY,KAAK,EAAE;EAAe,CAAC,EAClH;IAAEJ,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,CAAC;IAAEC,WAAW,EAAE,wBAAwB;IAAEC,IAAI,EAAEV,MAAM;IAAEW,KAAK,EAAE;EAAkB,CAAC,EAC3G;IAAEJ,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,CAAC;IAAEC,WAAW,EAAE,qBAAqB;IAAEC,IAAI,EAAET,IAAI;IAAEU,KAAK,EAAE;EAAgB,CAAC,CACrG;EAED,oBACER,OAAA;IAAKS,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBACnDV,OAAA;MAAKS,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDV,OAAA;QAAIS,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrEd,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CV,OAAA,CAACH,MAAM;UAACY,SAAS,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnCd,OAAA;UAAMS,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENd,OAAA;MAAKS,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EACpCP,MAAM,CAACY,GAAG,CAAEb,KAAK,IAAK;QACrB,MAAMc,IAAI,GAAGd,KAAK,CAACK,IAAI;QACvB,oBACEP,OAAA;UAEES,SAAS,EAAC,4FAA4F;UAAAC,QAAA,gBAEtGV,OAAA,CAACgB,IAAI;YAACP,SAAS,EAAE,gBAAgBP,KAAK,CAACM,KAAK;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDd,OAAA;YAAKS,SAAS,EAAE,2BACdP,KAAK,CAACG,KAAK,GAAG,CAAC,GAAGH,KAAK,CAACM,KAAK,GAAG,gBAAgB,EAC/C;YAAAE,QAAA,EACAR,KAAK,CAACG;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC/CR,KAAK,CAACI;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA,GAXDZ,KAAK,CAACE,IAAI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYZ,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GAxCWhB,SAAmC;AA0ChD,eAAeA,SAAS;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}