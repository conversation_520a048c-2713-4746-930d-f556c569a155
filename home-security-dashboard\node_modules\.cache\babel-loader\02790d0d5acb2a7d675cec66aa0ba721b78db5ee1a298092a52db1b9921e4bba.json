{"ast": null, "code": "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfYear} function options.\n */\n\n/**\n * @name startOfYear\n * @category Year Helpers\n * @summary Return the start of a year for the given date.\n *\n * @description\n * Return the start of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a year\n *\n * @example\n * // The start of a year for 2 September 2014 11:55:00:\n * const result = startOfYear(new Date(2014, 8, 2, 11, 55, 00))\n * //=> Wed Jan 01 2014 00:00:00\n */\nexport function startOfYear(date, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setFullYear(date_.getFullYear(), 0, 1);\n  date_.setHours(0, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default startOfYear;", "map": {"version": 3, "names": ["toDate", "startOfYear", "date", "options", "date_", "in", "setFullYear", "getFullYear", "setHours"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/startOfYear.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfYear} function options.\n */\n\n/**\n * @name startOfYear\n * @category Year Helpers\n * @summary Return the start of a year for the given date.\n *\n * @description\n * Return the start of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a year\n *\n * @example\n * // The start of a year for 2 September 2014 11:55:00:\n * const result = startOfYear(new Date(2014, 8, 2, 11, 55, 00))\n * //=> Wed Jan 01 2014 00:00:00\n */\nexport function startOfYear(date, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setFullYear(date_.getFullYear(), 0, 1);\n  date_.setHours(0, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default startOfYear;\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvCD,KAAK,CAACE,WAAW,CAACF,KAAK,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5CH,KAAK,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1B,OAAOJ,KAAK;AACd;;AAEA;AACA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}