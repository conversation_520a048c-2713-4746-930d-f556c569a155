{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\Resonance-KLE\\\\home-security-dashboard\\\\src\\\\components\\\\dashboard\\\\CameraFeed.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Maximize2 } from 'lucide-react';\nimport { Dialog } from '@headlessui/react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const CameraFeed = ({\n  streamUrl = '/stream',\n  location = 'Main Entrance'\n}) => {\n  _s();\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"aspect-video bg-gray-800 rounded-lg overflow-hidden relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: streamUrl,\n        alt: \"Live Camera Feed\",\n        className: \"w-full h-full object-cover\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-4 left-4 flex items-center space-x-2 bg-black bg-opacity-50 rounded-full px-3 py-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-2 h-2 rounded-full bg-green-500 animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-white text-sm\",\n          children: \"Live\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-4 left-4 right-4 flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-black bg-opacity-50 rounded-lg px-3 py-1\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white text-sm\",\n            children: location\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => setIsFullscreen(!isFullscreen),\n          className: \"bg-black bg-opacity-50 rounded-lg p-2 text-white hover:bg-opacity-75 transition-all\",\n          children: /*#__PURE__*/_jsxDEV(Maximize2, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4 grid grid-cols-2 gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-400\",\n            children: \"Motion Detected\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-green-400\",\n            children: \"Yes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 w-full h-1 bg-gray-700 rounded-full overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"h-full bg-green-500\",\n            initial: {\n              width: \"0%\"\n            },\n            animate: {\n              width: \"75%\"\n            },\n            transition: {\n              duration: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-800 rounded-lg p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-400\",\n            children: \"Signal Strength\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-blue-400\",\n            children: \"Excellent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 w-full h-1 bg-gray-700 rounded-full overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"h-full bg-blue-500\",\n            initial: {\n              width: \"0%\"\n            },\n            animate: {\n              width: \"90%\"\n            },\n            transition: {\n              duration: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: isFullscreen,\n      onClose: () => setIsFullscreen(false),\n      className: \"fixed inset-0 z-50 flex items-center justify-center\",\n      children: [/*#__PURE__*/_jsxDEV(Dialog.Overlay, {\n        className: \"fixed inset-0 bg-black opacity-75\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative w-full max-w-5xl p-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: streamUrl,\n          alt: \"Live Camera Feed\",\n          className: \"w-full h-full object-contain rounded-lg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsFullscreen(false),\n          className: \"absolute top-6 right-6 text-white hover:text-gray-300\",\n          children: /*#__PURE__*/_jsxDEV(Maximize2, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 9\n  }, this);\n};\n_s(CameraFeed, \"LI0KkFuciCdLvQ6T7dAtFOAXj0Y=\");\n_c = CameraFeed;\nvar _c;\n$RefreshReg$(_c, \"CameraFeed\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Maximize2", "Dialog", "jsxDEV", "_jsxDEV", "CameraFeed", "streamUrl", "location", "_s", "isFullscreen", "setIsFullscreen", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "button", "whileHover", "scale", "whileTap", "onClick", "div", "initial", "width", "animate", "transition", "duration", "open", "onClose", "Overlay", "_c", "$RefreshReg$"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/src/components/dashboard/CameraFeed.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Maximize2 } from 'lucide-react';\nimport { Dialog } from '@headlessui/react';\n\ninterface CameraFeedProps {\n    streamUrl?: string;\n    location?: string;\n}\n\nexport const CameraFeed: React.FC<CameraFeedProps> = ({\n    streamUrl = '/stream',\n    location = 'Main Entrance'\n}) => {\n    const [isFullscreen, setIsFullscreen] = useState(false);\n\n    return (\n        <div className=\"relative\">\n            <div className=\"aspect-video bg-gray-800 rounded-lg overflow-hidden relative\">\n                <img\n                    src={streamUrl}\n                    alt=\"Live Camera Feed\"\n                    className=\"w-full h-full object-cover\"\n                />\n\n                {/* Live Status Indicator */}\n                <div className=\"absolute top-4 left-4 flex items-center space-x-2 bg-black bg-opacity-50 rounded-full px-3 py-1\">\n                    <div className=\"w-2 h-2 rounded-full bg-green-500 animate-pulse\" />\n                    <span className=\"text-white text-sm\">Live</span>\n                </div>\n\n                {/* Camera Info */}\n                <div className=\"absolute bottom-4 left-4 right-4 flex justify-between items-center\">\n                    <div className=\"bg-black bg-opacity-50 rounded-lg px-3 py-1\">\n                        <span className=\"text-white text-sm\">{location}</span>\n                    </div>\n\n                    <motion.button\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                        onClick={() => setIsFullscreen(!isFullscreen)}\n                        className=\"bg-black bg-opacity-50 rounded-lg p-2 text-white hover:bg-opacity-75 transition-all\"\n                    >\n                        <Maximize2 className=\"w-5 h-5\" />\n                    </motion.button>\n                </div>\n            </div>\n\n            {/* Motion Detection Indicators */}\n            <div className=\"mt-4 grid grid-cols-2 gap-4\">\n                <div className=\"bg-gray-800 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-400\">Motion Detected</span>\n                        <span className=\"text-green-400\">Yes</span>\n                    </div>\n                    <div className=\"mt-2 w-full h-1 bg-gray-700 rounded-full overflow-hidden\">\n                        <motion.div\n                            className=\"h-full bg-green-500\"\n                            initial={{ width: \"0%\" }}\n                            animate={{ width: \"75%\" }}\n                            transition={{ duration: 1 }}\n                        />\n                    </div>\n                </div>\n\n                <div className=\"bg-gray-800 rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between\">\n                        <span className=\"text-gray-400\">Signal Strength</span>\n                        <span className=\"text-blue-400\">Excellent</span>\n                    </div>\n                    <div className=\"mt-2 w-full h-1 bg-gray-700 rounded-full overflow-hidden\">\n                        <motion.div\n                            className=\"h-full bg-blue-500\"\n                            initial={{ width: \"0%\" }}\n                            animate={{ width: \"90%\" }}\n                            transition={{ duration: 1 }}\n                        />\n                    </div>\n                </div>\n            </div>\n\n            {/* Fullscreen Modal */}\n            <Dialog\n                open={isFullscreen}\n                onClose={() => setIsFullscreen(false)}\n                className=\"fixed inset-0 z-50 flex items-center justify-center\"\n            >\n                <Dialog.Overlay className=\"fixed inset-0 bg-black opacity-75\" />\n\n                <div className=\"relative w-full max-w-5xl p-4\">\n                    <img\n                        src={streamUrl}\n                        alt=\"Live Camera Feed\"\n                        className=\"w-full h-full object-contain rounded-lg\"\n                    />\n                    <button\n                        onClick={() => setIsFullscreen(false)}\n                        className=\"absolute top-6 right-6 text-white hover:text-gray-300\"\n                    >\n                        <Maximize2 className=\"w-6 h-6\" />\n                    </button>\n                </div>\n            </Dialog>\n        </div>\n    );\n};"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,MAAM,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO3C,OAAO,MAAMC,UAAqC,GAAGA,CAAC;EAClDC,SAAS,GAAG,SAAS;EACrBC,QAAQ,GAAG;AACf,CAAC,KAAK;EAAAC,EAAA;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EAEvD,oBACIK,OAAA;IAAKO,SAAS,EAAC,UAAU;IAAAC,QAAA,gBACrBR,OAAA;MAAKO,SAAS,EAAC,8DAA8D;MAAAC,QAAA,gBACzER,OAAA;QACIS,GAAG,EAAEP,SAAU;QACfQ,GAAG,EAAC,kBAAkB;QACtBH,SAAS,EAAC;MAA4B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,eAGFd,OAAA;QAAKO,SAAS,EAAC,iGAAiG;QAAAC,QAAA,gBAC5GR,OAAA;UAAKO,SAAS,EAAC;QAAiD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnEd,OAAA;UAAMO,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eAGNd,OAAA;QAAKO,SAAS,EAAC,oEAAoE;QAAAC,QAAA,gBAC/ER,OAAA;UAAKO,SAAS,EAAC,6CAA6C;UAAAC,QAAA,eACxDR,OAAA;YAAMO,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEL;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAENd,OAAA,CAACJ,MAAM,CAACmB,MAAM;UACVC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BE,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,CAACD,YAAY,CAAE;UAC9CE,SAAS,EAAC,qFAAqF;UAAAC,QAAA,eAE/FR,OAAA,CAACH,SAAS;YAACU,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNd,OAAA;MAAKO,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBACxCR,OAAA;QAAKO,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACvCR,OAAA;UAAKO,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAC9CR,OAAA;YAAMO,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtDd,OAAA;YAAMO,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNd,OAAA;UAAKO,SAAS,EAAC,0DAA0D;UAAAC,QAAA,eACrER,OAAA,CAACJ,MAAM,CAACwB,GAAG;YACPb,SAAS,EAAC,qBAAqB;YAC/Bc,OAAO,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YACzBC,OAAO,EAAE;cAAED,KAAK,EAAE;YAAM,CAAE;YAC1BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAE;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENd,OAAA;QAAKO,SAAS,EAAC,4BAA4B;QAAAC,QAAA,gBACvCR,OAAA;UAAKO,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAC9CR,OAAA;YAAMO,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAe;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtDd,OAAA;YAAMO,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACNd,OAAA;UAAKO,SAAS,EAAC,0DAA0D;UAAAC,QAAA,eACrER,OAAA,CAACJ,MAAM,CAACwB,GAAG;YACPb,SAAS,EAAC,oBAAoB;YAC9Bc,OAAO,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YACzBC,OAAO,EAAE;cAAED,KAAK,EAAE;YAAM,CAAE;YAC1BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAE;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNd,OAAA,CAACF,MAAM;MACH4B,IAAI,EAAErB,YAAa;MACnBsB,OAAO,EAAEA,CAAA,KAAMrB,eAAe,CAAC,KAAK,CAAE;MACtCC,SAAS,EAAC,qDAAqD;MAAAC,QAAA,gBAE/DR,OAAA,CAACF,MAAM,CAAC8B,OAAO;QAACrB,SAAS,EAAC;MAAmC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEhEd,OAAA;QAAKO,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC1CR,OAAA;UACIS,GAAG,EAAEP,SAAU;UACfQ,GAAG,EAAC,kBAAkB;UACtBH,SAAS,EAAC;QAAyC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACFd,OAAA;UACImB,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,KAAK,CAAE;UACtCC,SAAS,EAAC,uDAAuD;UAAAC,QAAA,eAEjER,OAAA,CAACH,SAAS;YAACU,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAACV,EAAA,CA/FWH,UAAqC;AAAA4B,EAAA,GAArC5B,UAAqC;AAAA,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}