{"ast": null, "code": "import { useEffect as m } from \"react\";\nimport { useLatestValue as c } from './use-latest-value.js';\nfunction d(e, r, n) {\n  let o = c(r);\n  m(() => {\n    function t(u) {\n      o.current(u);\n    }\n    return document.addEventListener(e, t, n), () => document.removeEventListener(e, t, n);\n  }, [e, n]);\n}\nexport { d as useDocumentEvent };", "map": {"version": 3, "names": ["useEffect", "m", "useLatestValue", "c", "d", "e", "r", "n", "o", "t", "u", "current", "document", "addEventListener", "removeEventListener", "useDocumentEvent"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/@headlessui/react/dist/hooks/use-document-event.js"], "sourcesContent": ["import{useEffect as m}from\"react\";import{useLatestValue as c}from'./use-latest-value.js';function d(e,r,n){let o=c(r);m(()=>{function t(u){o.current(u)}return document.addEventListener(e,t,n),()=>document.removeEventListener(e,t,n)},[e,n])}export{d as useDocumentEvent};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACL,CAAC,CAACG,CAAC,CAAC;EAACL,CAAC,CAAC,MAAI;IAAC,SAASQ,CAACA,CAACC,CAAC,EAAC;MAACF,CAAC,CAACG,OAAO,CAACD,CAAC,CAAC;IAAA;IAAC,OAAOE,QAAQ,CAACC,gBAAgB,CAACR,CAAC,EAACI,CAAC,EAACF,CAAC,CAAC,EAAC,MAAIK,QAAQ,CAACE,mBAAmB,CAACT,CAAC,EAACI,CAAC,EAACF,CAAC,CAAC;EAAA,CAAC,EAAC,CAACF,CAAC,EAACE,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOH,CAAC,IAAIW,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}