{"ast": null, "code": "import { useCallback as n, useState as f } from \"react\";\nimport { useIsMounted as i } from './use-is-mounted.js';\nfunction c(a = 0) {\n  let [l, r] = f(a),\n    t = i(),\n    o = n(e => {\n      t.current && r(u => u | e);\n    }, [l, t]),\n    m = n(e => Boolean(l & e), [l]),\n    s = n(e => {\n      t.current && r(u => u & ~e);\n    }, [r, t]),\n    g = n(e => {\n      t.current && r(u => u ^ e);\n    }, [r]);\n  return {\n    flags: l,\n    addFlag: o,\n    hasFlag: m,\n    removeFlag: s,\n    toggleFlag: g\n  };\n}\nexport { c as useFlags };", "map": {"version": 3, "names": ["useCallback", "n", "useState", "f", "useIsMounted", "i", "c", "a", "l", "r", "t", "o", "e", "current", "u", "m", "Boolean", "s", "g", "flags", "addFlag", "hasFlag", "removeFlag", "toggleFlag", "useFlags"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/@headlessui/react/dist/hooks/use-flags.js"], "sourcesContent": ["import{useCallback as n,useState as f}from\"react\";import{useIsMounted as i}from'./use-is-mounted.js';function c(a=0){let[l,r]=f(a),t=i(),o=n(e=>{t.current&&r(u=>u|e)},[l,t]),m=n(e=>Boolean(l&e),[l]),s=n(e=>{t.current&&r(u=>u&~e)},[r,t]),g=n(e=>{t.current&&r(u=>u^e)},[r]);return{flags:l,addFlag:o,hasFlag:m,removeFlag:s,toggleFlag:g}}export{c as useFlags};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAASC,CAACA,CAACC,CAAC,GAAC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,GAACN,CAAC,CAACI,CAAC,CAAC;IAACG,CAAC,GAACL,CAAC,CAAC,CAAC;IAACM,CAAC,GAACV,CAAC,CAACW,CAAC,IAAE;MAACF,CAAC,CAACG,OAAO,IAAEJ,CAAC,CAACK,CAAC,IAAEA,CAAC,GAACF,CAAC,CAAC;IAAA,CAAC,EAAC,CAACJ,CAAC,EAACE,CAAC,CAAC,CAAC;IAACK,CAAC,GAACd,CAAC,CAACW,CAAC,IAAEI,OAAO,CAACR,CAAC,GAACI,CAAC,CAAC,EAAC,CAACJ,CAAC,CAAC,CAAC;IAACS,CAAC,GAAChB,CAAC,CAACW,CAAC,IAAE;MAACF,CAAC,CAACG,OAAO,IAAEJ,CAAC,CAACK,CAAC,IAAEA,CAAC,GAAC,CAACF,CAAC,CAAC;IAAA,CAAC,EAAC,CAACH,CAAC,EAACC,CAAC,CAAC,CAAC;IAACQ,CAAC,GAACjB,CAAC,CAACW,CAAC,IAAE;MAACF,CAAC,CAACG,OAAO,IAAEJ,CAAC,CAACK,CAAC,IAAEA,CAAC,GAACF,CAAC,CAAC;IAAA,CAAC,EAAC,CAACH,CAAC,CAAC,CAAC;EAAC,OAAM;IAACU,KAAK,EAACX,CAAC;IAACY,OAAO,EAACT,CAAC;IAACU,OAAO,EAACN,CAAC;IAACO,UAAU,EAACL,CAAC;IAACM,UAAU,EAACL;EAAC,CAAC;AAAA;AAAC,SAAOZ,CAAC,IAAIkB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}