{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\Resonance-KLE\\\\home-security-dashboard\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Monitor, Wifi, HardDrive, Camera, Mic, MicOff, Volume2, VolumeX, Maximize2, Settings, Phone } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [isMuted, setIsMuted] = useState(false);\n  const [isMicOn, setIsMicOn] = useState(false);\n  const cameras = [{\n    id: 1,\n    name: 'Camera Hostel',\n    status: 'online',\n    location: 'Hostel Area'\n  }, {\n    id: 2,\n    name: 'Camera Room',\n    status: 'online',\n    location: 'Room 101'\n  }, {\n    id: 3,\n    name: 'Camera Garden',\n    status: 'online',\n    location: 'Garden View'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-950\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col justify-center\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-5xl font-bold text-white mb-6 leading-tight\",\n              children: [\"Highest Level of\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-400\",\n                children: \"Protection\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 43,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400 text-lg mb-8 leading-relaxed\",\n              children: [\"We protected of 3 million people with our\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), \"fort police security system.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 rounded-full bg-green-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 52,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-400 text-sm\",\n                  children: \"Management security\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 rounded-full bg-green-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 56,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-400 text-sm\",\n                  children: \"Management security\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 57,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            className: \"relative\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-64 h-32 bg-gradient-to-r from-green-400 to-green-500 rounded-full transform rotate-12 shadow-2xl\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-4 bg-gray-900 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-red-500 rounded-full animate-pulse\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 75,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 74,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute -right-8 top-8\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-16 h-16 text-green-400\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    fill: \"currentColor\",\n                    className: \"w-full h-full\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 82,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 81,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.3\n          },\n          className: \"bg-gray-900 rounded-xl p-6 border border-gray-800\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(Monitor, {\n              className: \"w-6 h-6 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-white font-semibold\",\n              children: \"Other Device\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-sm\",\n            children: \"We protected of 3 million people with our fort police security system.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.4\n          },\n          className: \"bg-gray-900 rounded-xl p-6 border border-gray-800\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(Wifi, {\n              className: \"w-6 h-6 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-white font-semibold\",\n              children: \"Network Monitoring\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-sm\",\n            children: \"We protected of 3 million people with our fort police security system.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.5\n          },\n          className: \"bg-gray-900 rounded-xl p-6 border border-gray-800\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(HardDrive, {\n              className: \"w-6 h-6 text-gray-400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-white font-semibold\",\n              children: \"Backup\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-sm\",\n            children: \"We protected of 3 million people with our fort police security system.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.6\n        },\n        className: \"bg-gray-900 rounded-xl overflow-hidden border border-gray-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800 px-6 py-4 flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white font-medium\",\n              children: \"Managed by:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-400\",\n              children: \"10.1.1.30\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4 text-gray-400 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Server Connected\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Last than a minute ago\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Version: 11.9.0.351\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative aspect-video bg-gray-800\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: \"/captured_images/img_20250514_185115_951.jpg\",\n            alt: \"Live Camera Feed\",\n            className: \"w-full h-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-4 left-4 flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsMicOn(!isMicOn),\n              className: `p-2 rounded-full ${isMicOn ? 'bg-green-500' : 'bg-gray-700'} transition-colors`,\n              children: isMicOn ? /*#__PURE__*/_jsxDEV(Mic, {\n                className: \"w-4 h-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 28\n              }, this) : /*#__PURE__*/_jsxDEV(MicOff, {\n                className: \"w-4 h-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 69\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsMuted(!isMuted),\n              className: `p-2 rounded-full ${!isMuted ? 'bg-green-500' : 'bg-gray-700'} transition-colors`,\n              children: !isMuted ? /*#__PURE__*/_jsxDEV(Volume2, {\n                className: \"w-4 h-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 29\n              }, this) : /*#__PURE__*/_jsxDEV(VolumeX, {\n                className: \"w-4 h-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 74\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Camera, {\n                className: \"w-4 h-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Maximize2, {\n                className: \"w-4 h-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-4 right-4 flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                className: \"w-4 h-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Phone, {\n                className: \"w-4 h-4 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-80 bg-gray-900 border-l border-gray-800 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-white font-semibold mb-6\",\n        children: \"Camera Status\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: cameras.map((camera, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            delay: 0.7 + index * 0.1\n          },\n          className: \"bg-gray-800 rounded-lg p-4 border border-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white font-medium\",\n              children: camera.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 rounded-full bg-green-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-400 text-sm\",\n                children: \"Online\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 text-sm\",\n            children: camera.location\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this)]\n        }, camera.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"gQn5f0d373GZxKqZM7+griOZXU0=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Monitor", "Wifi", "HardDrive", "Camera", "Mic", "<PERSON><PERSON><PERSON><PERSON>", "Volume2", "VolumeX", "Maximize2", "Settings", "Phone", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "isMuted", "setIsMuted", "isMicOn", "setIsMicOn", "cameras", "id", "name", "status", "location", "className", "children", "div", "initial", "opacity", "x", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "scale", "delay", "viewBox", "fill", "d", "y", "src", "alt", "onClick", "map", "camera", "index", "_c", "$RefreshReg$"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  Monitor,\n  Wifi,\n  HardDrive,\n  Camera,\n  Mic,\n  MicOff,\n  Volume2,\n  VolumeX,\n  Maximize2,\n  Settings,\n  Phone\n} from 'lucide-react';\n\nconst Dashboard: React.FC = () => {\n  const [isMuted, setIsMuted] = useState(false);\n  const [isMicOn, setIsMicOn] = useState(false);\n\n  const cameras = [\n    { id: 1, name: 'Camera Hostel', status: 'online', location: 'Hostel Area' },\n    { id: 2, name: 'Camera Room', status: 'online', location: 'Room 101' },\n    { id: 3, name: 'Camera Garden', status: 'online', location: 'Garden View' },\n  ];\n\n  return (\n    <div className=\"flex h-screen bg-gray-950\">\n      {/* Main Content */}\n      <div className=\"flex-1 p-8\">\n        {/* Hero Section */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12\">\n          {/* Left Side - Text Content */}\n          <div className=\"flex flex-col justify-center\">\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n            >\n              <h1 className=\"text-5xl font-bold text-white mb-6 leading-tight\">\n                Highest Level of\n                <br />\n                <span className=\"text-green-400\">Protection</span>\n              </h1>\n              <p className=\"text-gray-400 text-lg mb-8 leading-relaxed\">\n                We protected of 3 million people with our\n                <br />\n                fort police security system.\n              </p>\n              <div className=\"flex items-center space-x-8\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 rounded-full bg-green-400\"></div>\n                  <span className=\"text-gray-400 text-sm\">Management security</span>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 rounded-full bg-green-400\"></div>\n                  <span className=\"text-gray-400 text-sm\">Management security</span>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Right Side - Camera Illustration */}\n          <div className=\"flex items-center justify-center\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"relative\"\n            >\n              {/* Green Camera Illustration */}\n              <div className=\"relative\">\n                <div className=\"w-64 h-32 bg-gradient-to-r from-green-400 to-green-500 rounded-full transform rotate-12 shadow-2xl\">\n                  <div className=\"absolute inset-4 bg-gray-900 rounded-full flex items-center justify-center\">\n                    <div className=\"w-8 h-8 bg-red-500 rounded-full animate-pulse\"></div>\n                  </div>\n                </div>\n                {/* DNA Helix Icon */}\n                <div className=\"absolute -right-8 top-8\">\n                  <div className=\"w-16 h-16 text-green-400\">\n                    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" className=\"w-full h-full\">\n                      <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                    </svg>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Feature Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n            className=\"bg-gray-900 rounded-xl p-6 border border-gray-800\"\n          >\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <Monitor className=\"w-6 h-6 text-gray-400\" />\n              <h3 className=\"text-white font-semibold\">Other Device</h3>\n            </div>\n            <p className=\"text-gray-400 text-sm\">\n              We protected of 3 million people with our fort police security system.\n            </p>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4 }}\n            className=\"bg-gray-900 rounded-xl p-6 border border-gray-800\"\n          >\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <Wifi className=\"w-6 h-6 text-gray-400\" />\n              <h3 className=\"text-white font-semibold\">Network Monitoring</h3>\n            </div>\n            <p className=\"text-gray-400 text-sm\">\n              We protected of 3 million people with our fort police security system.\n            </p>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.5 }}\n            className=\"bg-gray-900 rounded-xl p-6 border border-gray-800\"\n          >\n            <div className=\"flex items-center space-x-3 mb-4\">\n              <HardDrive className=\"w-6 h-6 text-gray-400\" />\n              <h3 className=\"text-white font-semibold\">Backup</h3>\n            </div>\n            <p className=\"text-gray-400 text-sm\">\n              We protected of 3 million people with our fort police security system.\n            </p>\n          </motion.div>\n        </div>\n\n        {/* Camera Feed Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.6 }}\n          className=\"bg-gray-900 rounded-xl overflow-hidden border border-gray-800\"\n        >\n          {/* Camera Feed Header */}\n          <div className=\"bg-gray-800 px-6 py-4 flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-white font-medium\">Managed by:</span>\n              <span className=\"text-gray-400\">10.1.1.30</span>\n            </div>\n            <div className=\"flex items-center space-x-4 text-gray-400 text-sm\">\n              <span>Server Connected</span>\n              <span>Last than a minute ago</span>\n              <span>Version: 11.9.0.351</span>\n            </div>\n          </div>\n\n          {/* Video Feed */}\n          <div className=\"relative aspect-video bg-gray-800\">\n            <img\n              src=\"/captured_images/img_20250514_185115_951.jpg\"\n              alt=\"Live Camera Feed\"\n              className=\"w-full h-full object-cover\"\n            />\n\n            {/* Video Controls */}\n            <div className=\"absolute bottom-4 left-4 flex items-center space-x-3\">\n              <button\n                onClick={() => setIsMicOn(!isMicOn)}\n                className={`p-2 rounded-full ${isMicOn ? 'bg-green-500' : 'bg-gray-700'} transition-colors`}\n              >\n                {isMicOn ? <Mic className=\"w-4 h-4 text-white\" /> : <MicOff className=\"w-4 h-4 text-white\" />}\n              </button>\n              <button\n                onClick={() => setIsMuted(!isMuted)}\n                className={`p-2 rounded-full ${!isMuted ? 'bg-green-500' : 'bg-gray-700'} transition-colors`}\n              >\n                {!isMuted ? <Volume2 className=\"w-4 h-4 text-white\" /> : <VolumeX className=\"w-4 h-4 text-white\" />}\n              </button>\n              <button className=\"p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors\">\n                <Camera className=\"w-4 h-4 text-white\" />\n              </button>\n              <button className=\"p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors\">\n                <Maximize2 className=\"w-4 h-4 text-white\" />\n              </button>\n            </div>\n\n            {/* Bottom Action Buttons */}\n            <div className=\"absolute bottom-4 right-4 flex items-center space-x-3\">\n              <button className=\"p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors\">\n                <Settings className=\"w-4 h-4 text-white\" />\n              </button>\n              <button className=\"p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors\">\n                <Phone className=\"w-4 h-4 text-white\" />\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Right Sidebar - Camera List */}\n      <div className=\"w-80 bg-gray-900 border-l border-gray-800 p-6\">\n        <h3 className=\"text-white font-semibold mb-6\">Camera Status</h3>\n        <div className=\"space-y-4\">\n          {cameras.map((camera, index) => (\n            <motion.div\n              key={camera.id}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.7 + index * 0.1 }}\n              className=\"bg-gray-800 rounded-lg p-4 border border-gray-700\"\n            >\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-white font-medium\">{camera.name}</span>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 rounded-full bg-green-400\"></div>\n                  <span className=\"text-green-400 text-sm\">Online</span>\n                </div>\n              </div>\n              <p className=\"text-gray-400 text-sm\">{camera.location}</p>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,OAAO,EACPC,SAAS,EACTC,QAAQ,EACRC,KAAK,QACA,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMqB,OAAO,GAAG,CACd;IAAEC,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,eAAe;IAAEC,MAAM,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAc,CAAC,EAC3E;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,aAAa;IAAEC,MAAM,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAW,CAAC,EACtE;IAAEH,EAAE,EAAE,CAAC;IAAEC,IAAI,EAAE,eAAe;IAAEC,MAAM,EAAE,QAAQ;IAAEC,QAAQ,EAAE;EAAc,CAAC,CAC5E;EAED,oBACEX,OAAA;IAAKY,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBAExCb,OAAA;MAAKY,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAEzBb,OAAA;QAAKY,SAAS,EAAC,8CAA8C;QAAAC,QAAA,gBAE3Db,OAAA;UAAKY,SAAS,EAAC,8BAA8B;UAAAC,QAAA,eAC3Cb,OAAA,CAACb,MAAM,CAAC2B,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAP,QAAA,gBAE9Bb,OAAA;cAAIY,SAAS,EAAC,kDAAkD;cAAAC,QAAA,GAAC,kBAE/D,eAAAb,OAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNxB,OAAA;gBAAMY,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAC;cAAU;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACLxB,OAAA;cAAGY,SAAS,EAAC,4CAA4C;cAAAC,QAAA,GAAC,2CAExD,eAAAb,OAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gCAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJxB,OAAA;cAAKY,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1Cb,OAAA;gBAAKY,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1Cb,OAAA;kBAAKY,SAAS,EAAC;gBAAmC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzDxB,OAAA;kBAAMY,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNxB,OAAA;gBAAKY,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1Cb,OAAA;kBAAKY,SAAS,EAAC;gBAAmC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzDxB,OAAA;kBAAMY,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNxB,OAAA;UAAKY,SAAS,EAAC,kCAAkC;UAAAC,QAAA,eAC/Cb,OAAA,CAACb,MAAM,CAAC2B,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAES,KAAK,EAAE;YAAI,CAAE;YACpCP,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAES,KAAK,EAAE;YAAE,CAAE;YAClCN,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEM,KAAK,EAAE;YAAI,CAAE;YAC1Cd,SAAS,EAAC,UAAU;YAAAC,QAAA,eAGpBb,OAAA;cAAKY,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBb,OAAA;gBAAKY,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,eACjHb,OAAA;kBAAKY,SAAS,EAAC,4EAA4E;kBAAAC,QAAA,eACzFb,OAAA;oBAAKY,SAAS,EAAC;kBAA+C;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxB,OAAA;gBAAKY,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,eACtCb,OAAA;kBAAKY,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,eACvCb,OAAA;oBAAK2B,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,cAAc;oBAAChB,SAAS,EAAC,eAAe;oBAAAC,QAAA,eACpEb,OAAA;sBAAM6B,CAAC,EAAC;oBAAuH;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9H;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxB,OAAA;QAAKY,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1Db,OAAA,CAACb,MAAM,CAAC2B,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAG,CAAE;UAC/BZ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAC9BX,UAAU,EAAE;YAAEO,KAAK,EAAE;UAAI,CAAE;UAC3Bd,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAE7Db,OAAA;YAAKY,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/Cb,OAAA,CAACZ,OAAO;cAACwB,SAAS,EAAC;YAAuB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CxB,OAAA;cAAIY,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAY;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNxB,OAAA;YAAGY,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbxB,OAAA,CAACb,MAAM,CAAC2B,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAG,CAAE;UAC/BZ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAC9BX,UAAU,EAAE;YAAEO,KAAK,EAAE;UAAI,CAAE;UAC3Bd,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAE7Db,OAAA;YAAKY,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/Cb,OAAA,CAACX,IAAI;cAACuB,SAAS,EAAC;YAAuB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1CxB,OAAA;cAAIY,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAkB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACNxB,OAAA;YAAGY,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEbxB,OAAA,CAACb,MAAM,CAAC2B,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAG,CAAE;UAC/BZ,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEc,CAAC,EAAE;UAAE,CAAE;UAC9BX,UAAU,EAAE;YAAEO,KAAK,EAAE;UAAI,CAAE;UAC3Bd,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAE7Db,OAAA;YAAKY,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/Cb,OAAA,CAACV,SAAS;cAACsB,SAAS,EAAC;YAAuB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CxB,OAAA;cAAIY,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAM;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNxB,OAAA;YAAGY,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNxB,OAAA,CAACb,MAAM,CAAC2B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEc,CAAC,EAAE;QAAG,CAAE;QAC/BZ,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEc,CAAC,EAAE;QAAE,CAAE;QAC9BX,UAAU,EAAE;UAAEO,KAAK,EAAE;QAAI,CAAE;QAC3Bd,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAGzEb,OAAA;UAAKY,SAAS,EAAC,yDAAyD;UAAAC,QAAA,gBACtEb,OAAA;YAAKY,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1Cb,OAAA;cAAMY,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DxB,OAAA;cAAMY,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNxB,OAAA;YAAKY,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChEb,OAAA;cAAAa,QAAA,EAAM;YAAgB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7BxB,OAAA;cAAAa,QAAA,EAAM;YAAsB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnCxB,OAAA;cAAAa,QAAA,EAAM;YAAmB;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxB,OAAA;UAAKY,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDb,OAAA;YACE+B,GAAG,EAAC,8CAA8C;YAClDC,GAAG,EAAC,kBAAkB;YACtBpB,SAAS,EAAC;UAA4B;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eAGFxB,OAAA;YAAKY,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnEb,OAAA;cACEiC,OAAO,EAAEA,CAAA,KAAM3B,UAAU,CAAC,CAACD,OAAO,CAAE;cACpCO,SAAS,EAAE,oBAAoBP,OAAO,GAAG,cAAc,GAAG,aAAa,oBAAqB;cAAAQ,QAAA,EAE3FR,OAAO,gBAAGL,OAAA,CAACR,GAAG;gBAACoB,SAAS,EAAC;cAAoB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGxB,OAAA,CAACP,MAAM;gBAACmB,SAAS,EAAC;cAAoB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvF,CAAC,eACTxB,OAAA;cACEiC,OAAO,EAAEA,CAAA,KAAM7B,UAAU,CAAC,CAACD,OAAO,CAAE;cACpCS,SAAS,EAAE,oBAAoB,CAACT,OAAO,GAAG,cAAc,GAAG,aAAa,oBAAqB;cAAAU,QAAA,EAE5F,CAACV,OAAO,gBAAGH,OAAA,CAACN,OAAO;gBAACkB,SAAS,EAAC;cAAoB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGxB,OAAA,CAACL,OAAO;gBAACiB,SAAS,EAAC;cAAoB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,eACTxB,OAAA;cAAQY,SAAS,EAAC,kEAAkE;cAAAC,QAAA,eAClFb,OAAA,CAACT,MAAM;gBAACqB,SAAS,EAAC;cAAoB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACTxB,OAAA;cAAQY,SAAS,EAAC,kEAAkE;cAAAC,QAAA,eAClFb,OAAA,CAACJ,SAAS;gBAACgB,SAAS,EAAC;cAAoB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNxB,OAAA;YAAKY,SAAS,EAAC,uDAAuD;YAAAC,QAAA,gBACpEb,OAAA;cAAQY,SAAS,EAAC,kEAAkE;cAAAC,QAAA,eAClFb,OAAA,CAACH,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACTxB,OAAA;cAAQY,SAAS,EAAC,kEAAkE;cAAAC,QAAA,eAClFb,OAAA,CAACF,KAAK;gBAACc,SAAS,EAAC;cAAoB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNxB,OAAA;MAAKY,SAAS,EAAC,+CAA+C;MAAAC,QAAA,gBAC5Db,OAAA;QAAIY,SAAS,EAAC,+BAA+B;QAAAC,QAAA,EAAC;MAAa;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChExB,OAAA;QAAKY,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvBN,OAAO,CAAC2B,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBpC,OAAA,CAACb,MAAM,CAAC2B,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEO,KAAK,EAAE,GAAG,GAAGU,KAAK,GAAG;UAAI,CAAE;UACzCxB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAE7Db,OAAA;YAAKY,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDb,OAAA;cAAMY,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAEsB,MAAM,CAAC1B;YAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7DxB,OAAA;cAAKY,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1Cb,OAAA;gBAAKY,SAAS,EAAC;cAAmC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDxB,OAAA;gBAAMY,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAM;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNxB,OAAA;YAAGY,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEsB,MAAM,CAACxB;UAAQ;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GAbrDW,MAAM,CAAC3B,EAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcJ,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CAnNID,SAAmB;AAAAoC,EAAA,GAAnBpC,SAAmB;AAqNzB,eAAeA,SAAS;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}