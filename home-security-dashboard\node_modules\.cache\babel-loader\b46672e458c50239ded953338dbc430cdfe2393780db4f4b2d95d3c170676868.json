{"ast": null, "code": "import { millisecondsInMinute } from \"./constants.js\";\n\n/**\n * @name minutesToMilliseconds\n * @category Conversion Helpers\n * @summary Convert minutes to milliseconds.\n *\n * @description\n * Convert a number of minutes to a full number of milliseconds.\n *\n * @param minutes - The number of minutes to be converted\n *\n * @returns The number of minutes converted in milliseconds\n *\n * @example\n * // Convert 2 minutes to milliseconds\n * const result = minutesToMilliseconds(2)\n * //=> 120000\n */\nexport function minutesToMilliseconds(minutes) {\n  return Math.trunc(minutes * millisecondsInMinute);\n}\n\n// Fallback for modularized imports:\nexport default minutesToMilliseconds;", "map": {"version": 3, "names": ["millisecondsInMinute", "minutesToMilliseconds", "minutes", "Math", "trunc"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/minutesToMilliseconds.js"], "sourcesContent": ["import { millisecondsInMinute } from \"./constants.js\";\n\n/**\n * @name minutesToMilliseconds\n * @category Conversion Helpers\n * @summary Convert minutes to milliseconds.\n *\n * @description\n * Convert a number of minutes to a full number of milliseconds.\n *\n * @param minutes - The number of minutes to be converted\n *\n * @returns The number of minutes converted in milliseconds\n *\n * @example\n * // Convert 2 minutes to milliseconds\n * const result = minutesToMilliseconds(2)\n * //=> 120000\n */\nexport function minutesToMilliseconds(minutes) {\n  return Math.trunc(minutes * millisecondsInMinute);\n}\n\n// Fallback for modularized imports:\nexport default minutesToMilliseconds;\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,gBAAgB;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EAC7C,OAAOC,IAAI,CAACC,KAAK,CAACF,OAAO,GAAGF,oBAAoB,CAAC;AACnD;;AAEA;AACA,eAAeC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}