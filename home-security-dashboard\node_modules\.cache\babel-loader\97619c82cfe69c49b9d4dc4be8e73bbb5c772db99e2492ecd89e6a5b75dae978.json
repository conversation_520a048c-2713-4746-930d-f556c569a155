{"ast": null, "code": "import { isSVGElement } from '../../render/dom/utils/is-svg-element.mjs';\nimport { SVGVisualElement } from '../../render/svg/SVGVisualElement.mjs';\nimport { HTMLVisualElement } from '../../render/html/HTMLVisualElement.mjs';\nimport { visualElementStore } from '../../render/store.mjs';\nfunction createVisualElement(element) {\n  const options = {\n    presenceContext: null,\n    props: {},\n    visualState: {\n      renderState: {\n        transform: {},\n        transformOrigin: {},\n        style: {},\n        vars: {},\n        attrs: {}\n      },\n      latestValues: {}\n    }\n  };\n  const node = isSVGElement(element) ? new SVGVisualElement(options, {\n    enableHardwareAcceleration: false\n  }) : new HTMLVisualElement(options, {\n    enableHardwareAcceleration: true\n  });\n  node.mount(element);\n  visualElementStore.set(element, node);\n}\nexport { createVisualElement };", "map": {"version": 3, "names": ["isSVGElement", "SVGVisualElement", "HTMLVisualElement", "visualElementStore", "createVisualElement", "element", "options", "presenceContext", "props", "visualState", "renderState", "transform", "transform<PERSON><PERSON>in", "style", "vars", "attrs", "latestValues", "node", "enableHardwareAcceleration", "mount", "set"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/framer-motion/dist/es/animation/utils/create-visual-element.mjs"], "sourcesContent": ["import { isSVGElement } from '../../render/dom/utils/is-svg-element.mjs';\nimport { SVGVisualElement } from '../../render/svg/SVGVisualElement.mjs';\nimport { HTMLVisualElement } from '../../render/html/HTMLVisualElement.mjs';\nimport { visualElementStore } from '../../render/store.mjs';\n\nfunction createVisualElement(element) {\n    const options = {\n        presenceContext: null,\n        props: {},\n        visualState: {\n            renderState: {\n                transform: {},\n                transformOrigin: {},\n                style: {},\n                vars: {},\n                attrs: {},\n            },\n            latestValues: {},\n        },\n    };\n    const node = isSVGElement(element)\n        ? new SVGVisualElement(options, {\n            enableHardwareAcceleration: false,\n        })\n        : new HTMLVisualElement(options, {\n            enableHardwareAcceleration: true,\n        });\n    node.mount(element);\n    visualElementStore.set(element, node);\n}\n\nexport { createVisualElement };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,2CAA2C;AACxE,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,iBAAiB,QAAQ,yCAAyC;AAC3E,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3D,SAASC,mBAAmBA,CAACC,OAAO,EAAE;EAClC,MAAMC,OAAO,GAAG;IACZC,eAAe,EAAE,IAAI;IACrBC,KAAK,EAAE,CAAC,CAAC;IACTC,WAAW,EAAE;MACTC,WAAW,EAAE;QACTC,SAAS,EAAE,CAAC,CAAC;QACbC,eAAe,EAAE,CAAC,CAAC;QACnBC,KAAK,EAAE,CAAC,CAAC;QACTC,IAAI,EAAE,CAAC,CAAC;QACRC,KAAK,EAAE,CAAC;MACZ,CAAC;MACDC,YAAY,EAAE,CAAC;IACnB;EACJ,CAAC;EACD,MAAMC,IAAI,GAAGjB,YAAY,CAACK,OAAO,CAAC,GAC5B,IAAIJ,gBAAgB,CAACK,OAAO,EAAE;IAC5BY,0BAA0B,EAAE;EAChC,CAAC,CAAC,GACA,IAAIhB,iBAAiB,CAACI,OAAO,EAAE;IAC7BY,0BAA0B,EAAE;EAChC,CAAC,CAAC;EACND,IAAI,CAACE,KAAK,CAACd,OAAO,CAAC;EACnBF,kBAAkB,CAACiB,GAAG,CAACf,OAAO,EAAEY,IAAI,CAAC;AACzC;AAEA,SAASb,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}