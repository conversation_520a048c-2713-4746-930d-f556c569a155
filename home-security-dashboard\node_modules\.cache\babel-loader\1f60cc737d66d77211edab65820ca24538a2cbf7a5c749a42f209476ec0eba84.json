{"ast": null, "code": "import { constructFrom } from \"../../constructFrom.js\";\nimport { transpose } from \"../../transpose.js\";\nconst TIMEZONE_UNIT_PRIORITY = 10;\nexport class Setter {\n  subPriority = 0;\n  validate(_utcDate, _options) {\n    return true;\n  }\n}\nexport class ValueSetter extends Setter {\n  constructor(value, validateValue, setValue, priority, subPriority) {\n    super();\n    this.value = value;\n    this.validateValue = validateValue;\n    this.setValue = setValue;\n    this.priority = priority;\n    if (subPriority) {\n      this.subPriority = subPriority;\n    }\n  }\n  validate(date, options) {\n    return this.validateValue(date, this.value, options);\n  }\n  set(date, flags, options) {\n    return this.setValue(date, flags, this.value, options);\n  }\n}\nexport class DateTimezoneSetter extends Setter {\n  priority = TIMEZONE_UNIT_PRIORITY;\n  subPriority = -1;\n  constructor(context, reference) {\n    super();\n    this.context = context || (date => constructFrom(reference, date));\n  }\n  set(date, flags) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(date, transpose(date, this.context));\n  }\n}", "map": {"version": 3, "names": ["constructFrom", "transpose", "TIMEZONE_UNIT_PRIORITY", "<PERSON>ter", "subPriority", "validate", "_utcDate", "_options", "ValueSetter", "constructor", "value", "validate<PERSON><PERSON>ue", "setValue", "priority", "date", "options", "set", "flags", "DateTimezoneSetter", "context", "reference", "timestampIsSet"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/parse/_lib/Setter.js"], "sourcesContent": ["import { constructFrom } from \"../../constructFrom.js\";\nimport { transpose } from \"../../transpose.js\";\n\nconst TIMEZONE_UNIT_PRIORITY = 10;\n\nexport class Setter {\n  subPriority = 0;\n\n  validate(_utcDate, _options) {\n    return true;\n  }\n}\n\nexport class ValueSetter extends Setter {\n  constructor(\n    value,\n\n    validateValue,\n\n    setValue,\n\n    priority,\n    subPriority,\n  ) {\n    super();\n    this.value = value;\n    this.validateValue = validateValue;\n    this.setValue = setValue;\n    this.priority = priority;\n    if (subPriority) {\n      this.subPriority = subPriority;\n    }\n  }\n\n  validate(date, options) {\n    return this.validateValue(date, this.value, options);\n  }\n\n  set(date, flags, options) {\n    return this.setValue(date, flags, this.value, options);\n  }\n}\n\nexport class DateTimezoneSetter extends Setter {\n  priority = TIMEZONE_UNIT_PRIORITY;\n  subPriority = -1;\n\n  constructor(context, reference) {\n    super();\n    this.context = context || ((date) => constructFrom(reference, date));\n  }\n\n  set(date, flags) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(date, transpose(date, this.context));\n  }\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,wBAAwB;AACtD,SAASC,SAAS,QAAQ,oBAAoB;AAE9C,MAAMC,sBAAsB,GAAG,EAAE;AAEjC,OAAO,MAAMC,MAAM,CAAC;EAClBC,WAAW,GAAG,CAAC;EAEfC,QAAQA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC3B,OAAO,IAAI;EACb;AACF;AAEA,OAAO,MAAMC,WAAW,SAASL,MAAM,CAAC;EACtCM,WAAWA,CACTC,KAAK,EAELC,aAAa,EAEbC,QAAQ,EAERC,QAAQ,EACRT,WAAW,EACX;IACA,KAAK,CAAC,CAAC;IACP,IAAI,CAACM,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAIT,WAAW,EAAE;MACf,IAAI,CAACA,WAAW,GAAGA,WAAW;IAChC;EACF;EAEAC,QAAQA,CAACS,IAAI,EAAEC,OAAO,EAAE;IACtB,OAAO,IAAI,CAACJ,aAAa,CAACG,IAAI,EAAE,IAAI,CAACJ,KAAK,EAAEK,OAAO,CAAC;EACtD;EAEAC,GAAGA,CAACF,IAAI,EAAEG,KAAK,EAAEF,OAAO,EAAE;IACxB,OAAO,IAAI,CAACH,QAAQ,CAACE,IAAI,EAAEG,KAAK,EAAE,IAAI,CAACP,KAAK,EAAEK,OAAO,CAAC;EACxD;AACF;AAEA,OAAO,MAAMG,kBAAkB,SAASf,MAAM,CAAC;EAC7CU,QAAQ,GAAGX,sBAAsB;EACjCE,WAAW,GAAG,CAAC,CAAC;EAEhBK,WAAWA,CAACU,OAAO,EAAEC,SAAS,EAAE;IAC9B,KAAK,CAAC,CAAC;IACP,IAAI,CAACD,OAAO,GAAGA,OAAO,KAAML,IAAI,IAAKd,aAAa,CAACoB,SAAS,EAAEN,IAAI,CAAC,CAAC;EACtE;EAEAE,GAAGA,CAACF,IAAI,EAAEG,KAAK,EAAE;IACf,IAAIA,KAAK,CAACI,cAAc,EAAE,OAAOP,IAAI;IACrC,OAAOd,aAAa,CAACc,IAAI,EAAEb,SAAS,CAACa,IAAI,EAAE,IAAI,CAACK,OAAO,CAAC,CAAC;EAC3D;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}