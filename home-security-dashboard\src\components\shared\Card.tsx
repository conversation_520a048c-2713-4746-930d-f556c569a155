import React from 'react';
import { motion } from 'framer-motion';

interface CardProps {
    title?: string;
    icon?: React.ReactNode;
    actionButton?: React.ReactNode;
    children?: React.ReactNode;
    className?: string;
}

export const Card: React.FC<CardProps> = ({
    title,
    icon,
    actionButton,
    children,
    className = ''
}) => {
    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`bg-gray-900 rounded-xl p-6 shadow-lg ${className}`}
        >
            {(title || icon || actionButton) && (
                <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                        {icon && <span className="text-gray-400">{icon}</span>}
                        {title && <h2 className="text-xl font-semibold text-white">{title}</h2>}
                    </div>
                    {actionButton && <div>{actionButton}</div>}
                </div>
            )}
            {children}
        </motion.div>
    );
};