{"ast": null, "code": "import { useVirtualizer as Ee } from \"@tanstack/react-virtual\";\nimport w, { createContext as ie, createRef as P<PERSON>, Fragment as me, use<PERSON><PERSON>back as Ie, useContext as ue, useEffect as Ve, useMemo as U, useReducer as _e, useRef as B, useState as Fe } from \"react\";\nimport { useComputed as pe } from '../../hooks/use-computed.js';\nimport { useControllable as Le } from '../../hooks/use-controllable.js';\nimport { useDisposables as se } from '../../hooks/use-disposables.js';\nimport { useEvent as m } from '../../hooks/use-event.js';\nimport { useId as Q } from '../../hooks/use-id.js';\nimport { useIsoMorphicEffect as H } from '../../hooks/use-iso-morphic-effect.js';\nimport { useLatestValue as De } from '../../hooks/use-latest-value.js';\nimport { useOutsideClick as Me } from '../../hooks/use-outside-click.js';\nimport { useOwnerDocument as he } from '../../hooks/use-owner.js';\nimport { useResolveButtonType as Be } from '../../hooks/use-resolve-button-type.js';\nimport { useSyncRefs as Z } from '../../hooks/use-sync-refs.js';\nimport { useTrackedPointer as ke } from '../../hooks/use-tracked-pointer.js';\nimport { useTreeWalker as we } from '../../hooks/use-tree-walker.js';\nimport { useWatch as Te } from '../../hooks/use-watch.js';\nimport { Features as Ue, Hidden as He } from '../../internal/hidden.js';\nimport { OpenClosedProvider as Ne, State as re, useOpenClosed as Ge } from '../../internal/open-closed.js';\nimport { history as xe } from '../../utils/active-element-history.js';\nimport { isDisabledReactIssue7711 as Xe } from '../../utils/bugs.js';\nimport { calculateActiveIndex as ge, Focus as y } from '../../utils/calculate-active-index.js';\nimport { disposables as ve } from '../../utils/disposables.js';\nimport { sortByDomNode as je } from '../../utils/focus-management.js';\nimport { objectToFormEntries as Je } from '../../utils/form.js';\nimport { match as W } from '../../utils/match.js';\nimport { isMobile as Ke } from '../../utils/platform.js';\nimport { compact as We, Features as Oe, forwardRefWithAs as $, render as q } from '../../utils/render.js';\nimport { Keys as M } from '../keyboard.js';\nvar $e = (o => (o[o.Open = 0] = \"Open\", o[o.Closed = 1] = \"Closed\", o))($e || {}),\n  qe = (o => (o[o.Single = 0] = \"Single\", o[o.Multi = 1] = \"Multi\", o))(qe || {}),\n  ze = (a => (a[a.Pointer = 0] = \"Pointer\", a[a.Focus = 1] = \"Focus\", a[a.Other = 2] = \"Other\", a))(ze || {}),\n  Ye = (e => (e[e.OpenCombobox = 0] = \"OpenCombobox\", e[e.CloseCombobox = 1] = \"CloseCombobox\", e[e.GoToOption = 2] = \"GoToOption\", e[e.RegisterOption = 3] = \"RegisterOption\", e[e.UnregisterOption = 4] = \"UnregisterOption\", e[e.RegisterLabel = 5] = \"RegisterLabel\", e[e.SetActivationTrigger = 6] = \"SetActivationTrigger\", e[e.UpdateVirtualOptions = 7] = \"UpdateVirtualOptions\", e))(Ye || {});\nfunction de(t, r = o => o) {\n  let o = t.activeOptionIndex !== null ? t.options[t.activeOptionIndex] : null,\n    a = r(t.options.slice()),\n    i = a.length > 0 && a[0].dataRef.current.order !== null ? a.sort((p, c) => p.dataRef.current.order - c.dataRef.current.order) : je(a, p => p.dataRef.current.domRef.current),\n    u = o ? i.indexOf(o) : null;\n  return u === -1 && (u = null), {\n    options: i,\n    activeOptionIndex: u\n  };\n}\nlet Qe = {\n    [1](t) {\n      var r;\n      return (r = t.dataRef.current) != null && r.disabled || t.comboboxState === 1 ? t : {\n        ...t,\n        activeOptionIndex: null,\n        comboboxState: 1\n      };\n    },\n    [0](t) {\n      var r, o;\n      if ((r = t.dataRef.current) != null && r.disabled || t.comboboxState === 0) return t;\n      if ((o = t.dataRef.current) != null && o.value) {\n        let a = t.dataRef.current.calculateIndex(t.dataRef.current.value);\n        if (a !== -1) return {\n          ...t,\n          activeOptionIndex: a,\n          comboboxState: 0\n        };\n      }\n      return {\n        ...t,\n        comboboxState: 0\n      };\n    },\n    [2](t, r) {\n      var u, p, c, e, l;\n      if ((u = t.dataRef.current) != null && u.disabled || (p = t.dataRef.current) != null && p.optionsRef.current && !((c = t.dataRef.current) != null && c.optionsPropsRef.current.static) && t.comboboxState === 1) return t;\n      if (t.virtual) {\n        let T = r.focus === y.Specific ? r.idx : ge(r, {\n            resolveItems: () => t.virtual.options,\n            resolveActiveIndex: () => {\n              var f, v;\n              return (v = (f = t.activeOptionIndex) != null ? f : t.virtual.options.findIndex(S => !t.virtual.disabled(S))) != null ? v : null;\n            },\n            resolveDisabled: t.virtual.disabled,\n            resolveId() {\n              throw new Error(\"Function not implemented.\");\n            }\n          }),\n          g = (e = r.trigger) != null ? e : 2;\n        return t.activeOptionIndex === T && t.activationTrigger === g ? t : {\n          ...t,\n          activeOptionIndex: T,\n          activationTrigger: g\n        };\n      }\n      let o = de(t);\n      if (o.activeOptionIndex === null) {\n        let T = o.options.findIndex(g => !g.dataRef.current.disabled);\n        T !== -1 && (o.activeOptionIndex = T);\n      }\n      let a = r.focus === y.Specific ? r.idx : ge(r, {\n          resolveItems: () => o.options,\n          resolveActiveIndex: () => o.activeOptionIndex,\n          resolveId: T => T.id,\n          resolveDisabled: T => T.dataRef.current.disabled\n        }),\n        i = (l = r.trigger) != null ? l : 2;\n      return t.activeOptionIndex === a && t.activationTrigger === i ? t : {\n        ...t,\n        ...o,\n        activeOptionIndex: a,\n        activationTrigger: i\n      };\n    },\n    [3]: (t, r) => {\n      var u, p, c;\n      if ((u = t.dataRef.current) != null && u.virtual) return {\n        ...t,\n        options: [...t.options, r.payload]\n      };\n      let o = r.payload,\n        a = de(t, e => (e.push(o), e));\n      t.activeOptionIndex === null && (p = t.dataRef.current) != null && p.isSelected(r.payload.dataRef.current.value) && (a.activeOptionIndex = a.options.indexOf(o));\n      let i = {\n        ...t,\n        ...a,\n        activationTrigger: 2\n      };\n      return (c = t.dataRef.current) != null && c.__demoMode && t.dataRef.current.value === void 0 && (i.activeOptionIndex = 0), i;\n    },\n    [4]: (t, r) => {\n      var a;\n      if ((a = t.dataRef.current) != null && a.virtual) return {\n        ...t,\n        options: t.options.filter(i => i.id !== r.id)\n      };\n      let o = de(t, i => {\n        let u = i.findIndex(p => p.id === r.id);\n        return u !== -1 && i.splice(u, 1), i;\n      });\n      return {\n        ...t,\n        ...o,\n        activationTrigger: 2\n      };\n    },\n    [5]: (t, r) => t.labelId === r.id ? t : {\n      ...t,\n      labelId: r.id\n    },\n    [6]: (t, r) => t.activationTrigger === r.trigger ? t : {\n      ...t,\n      activationTrigger: r.trigger\n    },\n    [7]: (t, r) => {\n      var a;\n      if (((a = t.virtual) == null ? void 0 : a.options) === r.options) return t;\n      let o = t.activeOptionIndex;\n      if (t.activeOptionIndex !== null) {\n        let i = r.options.indexOf(t.virtual.options[t.activeOptionIndex]);\n        i !== -1 ? o = i : o = null;\n      }\n      return {\n        ...t,\n        activeOptionIndex: o,\n        virtual: Object.assign({}, t.virtual, {\n          options: r.options\n        })\n      };\n    }\n  },\n  be = ie(null);\nbe.displayName = \"ComboboxActionsContext\";\nfunction ee(t) {\n  let r = ue(be);\n  if (r === null) {\n    let o = new Error(`<${t} /> is missing a parent <Combobox /> component.`);\n    throw Error.captureStackTrace && Error.captureStackTrace(o, ee), o;\n  }\n  return r;\n}\nlet Ce = ie(null);\nfunction Ze(t) {\n  var c;\n  let r = j(\"VirtualProvider\"),\n    [o, a] = U(() => {\n      let e = r.optionsRef.current;\n      if (!e) return [0, 0];\n      let l = window.getComputedStyle(e);\n      return [parseFloat(l.paddingBlockStart || l.paddingTop), parseFloat(l.paddingBlockEnd || l.paddingBottom)];\n    }, [r.optionsRef.current]),\n    i = Ee({\n      scrollPaddingStart: o,\n      scrollPaddingEnd: a,\n      count: r.virtual.options.length,\n      estimateSize() {\n        return 40;\n      },\n      getScrollElement() {\n        var e;\n        return (e = r.optionsRef.current) != null ? e : null;\n      },\n      overscan: 12\n    }),\n    [u, p] = Fe(0);\n  return H(() => {\n    p(e => e + 1);\n  }, [(c = r.virtual) == null ? void 0 : c.options]), w.createElement(Ce.Provider, {\n    value: i\n  }, w.createElement(\"div\", {\n    style: {\n      position: \"relative\",\n      width: \"100%\",\n      height: `${i.getTotalSize()}px`\n    },\n    ref: e => {\n      if (e) {\n        if (typeof process != \"undefined\" && process.env.JEST_WORKER_ID !== void 0 || r.activationTrigger === 0) return;\n        r.activeOptionIndex !== null && r.virtual.options.length > r.activeOptionIndex && i.scrollToIndex(r.activeOptionIndex);\n      }\n    }\n  }, i.getVirtualItems().map(e => {\n    var l;\n    return w.createElement(me, {\n      key: e.key\n    }, w.cloneElement((l = t.children) == null ? void 0 : l.call(t, {\n      option: r.virtual.options[e.index],\n      open: r.comboboxState === 0\n    }), {\n      key: `${u}-${e.key}`,\n      \"data-index\": e.index,\n      \"aria-setsize\": r.virtual.options.length,\n      \"aria-posinset\": e.index + 1,\n      style: {\n        position: \"absolute\",\n        top: 0,\n        left: 0,\n        transform: `translateY(${e.start}px)`,\n        overflowAnchor: \"none\"\n      }\n    }));\n  })));\n}\nlet ce = ie(null);\nce.displayName = \"ComboboxDataContext\";\nfunction j(t) {\n  let r = ue(ce);\n  if (r === null) {\n    let o = new Error(`<${t} /> is missing a parent <Combobox /> component.`);\n    throw Error.captureStackTrace && Error.captureStackTrace(o, j), o;\n  }\n  return r;\n}\nfunction et(t, r) {\n  return W(r.type, Qe, t, r);\n}\nlet tt = me;\nfunction ot(t, r) {\n  var fe;\n  let {\n      value: o,\n      defaultValue: a,\n      onChange: i,\n      form: u,\n      name: p,\n      by: c = null,\n      disabled: e = !1,\n      __demoMode: l = !1,\n      nullable: T = !1,\n      multiple: g = !1,\n      immediate: f = !1,\n      virtual: v = null,\n      ...S\n    } = t,\n    R = !1,\n    s = null,\n    [I = g ? [] : void 0, V] = Le(o, i, a),\n    [_, E] = _e(et, {\n      dataRef: Pe(),\n      comboboxState: l ? 0 : 1,\n      options: [],\n      virtual: s ? {\n        options: s.options,\n        disabled: (fe = s.disabled) != null ? fe : () => !1\n      } : null,\n      activeOptionIndex: null,\n      activationTrigger: 2,\n      labelId: null\n    }),\n    k = B(!1),\n    J = B({\n      static: !1,\n      hold: !1\n    }),\n    K = B(null),\n    z = B(null),\n    te = B(null),\n    X = B(null),\n    x = m(typeof c == \"string\" ? (d, b) => {\n      let P = c;\n      return (d == null ? void 0 : d[P]) === (b == null ? void 0 : b[P]);\n    } : c != null ? c : (d, b) => d === b),\n    O = m(d => s ? c === null ? s.options.indexOf(d) : s.options.findIndex(b => x(b, d)) : _.options.findIndex(b => x(b.dataRef.current.value, d))),\n    L = Ie(d => W(n.mode, {\n      [1]: () => I.some(b => x(b, d)),\n      [0]: () => x(I, d)\n    }), [I]),\n    oe = m(d => _.activeOptionIndex === O(d)),\n    n = U(() => ({\n      ..._,\n      immediate: R,\n      optionsPropsRef: J,\n      labelRef: K,\n      inputRef: z,\n      buttonRef: te,\n      optionsRef: X,\n      value: I,\n      defaultValue: a,\n      disabled: e,\n      mode: g ? 1 : 0,\n      virtual: _.virtual,\n      get activeOptionIndex() {\n        if (k.current && _.activeOptionIndex === null && (s ? s.options.length > 0 : _.options.length > 0)) {\n          if (s) {\n            let b = s.options.findIndex(P => {\n              var G, Y;\n              return !((Y = (G = s == null ? void 0 : s.disabled) == null ? void 0 : G.call(s, P)) != null && Y);\n            });\n            if (b !== -1) return b;\n          }\n          let d = _.options.findIndex(b => !b.dataRef.current.disabled);\n          if (d !== -1) return d;\n        }\n        return _.activeOptionIndex;\n      },\n      calculateIndex: O,\n      compare: x,\n      isSelected: L,\n      isActive: oe,\n      nullable: T,\n      __demoMode: l\n    }), [I, a, e, g, T, l, _, s]);\n  H(() => {\n    s && E({\n      type: 7,\n      options: s.options\n    });\n  }, [s, s == null ? void 0 : s.options]), H(() => {\n    _.dataRef.current = n;\n  }, [n]), Me([n.buttonRef, n.inputRef, n.optionsRef], () => le.closeCombobox(), n.comboboxState === 0);\n  let F = U(() => {\n      var d, b, P;\n      return {\n        open: n.comboboxState === 0,\n        disabled: e,\n        activeIndex: n.activeOptionIndex,\n        activeOption: n.activeOptionIndex === null ? null : n.virtual ? n.virtual.options[(d = n.activeOptionIndex) != null ? d : 0] : (P = (b = n.options[n.activeOptionIndex]) == null ? void 0 : b.dataRef.current.value) != null ? P : null,\n        value: I\n      };\n    }, [n, e, I]),\n    A = m(() => {\n      if (n.activeOptionIndex !== null) {\n        if (n.virtual) ae(n.virtual.options[n.activeOptionIndex]);else {\n          let {\n            dataRef: d\n          } = n.options[n.activeOptionIndex];\n          ae(d.current.value);\n        }\n        le.goToOption(y.Specific, n.activeOptionIndex);\n      }\n    }),\n    h = m(() => {\n      E({\n        type: 0\n      }), k.current = !0;\n    }),\n    C = m(() => {\n      E({\n        type: 1\n      }), k.current = !1;\n    }),\n    D = m((d, b, P) => (k.current = !1, d === y.Specific ? E({\n      type: 2,\n      focus: y.Specific,\n      idx: b,\n      trigger: P\n    }) : E({\n      type: 2,\n      focus: d,\n      trigger: P\n    }))),\n    N = m((d, b) => (E({\n      type: 3,\n      payload: {\n        id: d,\n        dataRef: b\n      }\n    }), () => {\n      n.isActive(b.current.value) && (k.current = !0), E({\n        type: 4,\n        id: d\n      });\n    })),\n    ye = m(d => (E({\n      type: 5,\n      id: d\n    }), () => E({\n      type: 5,\n      id: null\n    }))),\n    ae = m(d => W(n.mode, {\n      [0]() {\n        return V == null ? void 0 : V(d);\n      },\n      [1]() {\n        let b = n.value.slice(),\n          P = b.findIndex(G => x(G, d));\n        return P === -1 ? b.push(d) : b.splice(P, 1), V == null ? void 0 : V(b);\n      }\n    })),\n    Re = m(d => {\n      E({\n        type: 6,\n        trigger: d\n      });\n    }),\n    le = U(() => ({\n      onChange: ae,\n      registerOption: N,\n      registerLabel: ye,\n      goToOption: D,\n      closeCombobox: C,\n      openCombobox: h,\n      setActivationTrigger: Re,\n      selectActiveOption: A\n    }), []),\n    Ae = r === null ? {} : {\n      ref: r\n    },\n    ne = B(null),\n    Se = se();\n  return Ve(() => {\n    ne.current && a !== void 0 && Se.addEventListener(ne.current, \"reset\", () => {\n      V == null || V(a);\n    });\n  }, [ne, V]), w.createElement(be.Provider, {\n    value: le\n  }, w.createElement(ce.Provider, {\n    value: n\n  }, w.createElement(Ne, {\n    value: W(n.comboboxState, {\n      [0]: re.Open,\n      [1]: re.Closed\n    })\n  }, p != null && I != null && Je({\n    [p]: I\n  }).map(([d, b], P) => w.createElement(He, {\n    features: Ue.Hidden,\n    ref: P === 0 ? G => {\n      var Y;\n      ne.current = (Y = G == null ? void 0 : G.closest(\"form\")) != null ? Y : null;\n    } : void 0,\n    ...We({\n      key: d,\n      as: \"input\",\n      type: \"hidden\",\n      hidden: !0,\n      readOnly: !0,\n      form: u,\n      disabled: e,\n      name: d,\n      value: b\n    })\n  })), q({\n    ourProps: Ae,\n    theirProps: S,\n    slot: F,\n    defaultTag: tt,\n    name: \"Combobox\"\n  }))));\n}\nlet nt = \"input\";\nfunction rt(t, r) {\n  var X, x, O, L, oe;\n  let o = Q(),\n    {\n      id: a = `headlessui-combobox-input-${o}`,\n      onChange: i,\n      displayValue: u,\n      type: p = \"text\",\n      ...c\n    } = t,\n    e = j(\"Combobox.Input\"),\n    l = ee(\"Combobox.Input\"),\n    T = Z(e.inputRef, r),\n    g = he(e.inputRef),\n    f = B(!1),\n    v = se(),\n    S = m(() => {\n      l.onChange(null), e.optionsRef.current && (e.optionsRef.current.scrollTop = 0), l.goToOption(y.Nothing);\n    }),\n    R = function () {\n      var n;\n      return typeof u == \"function\" && e.value !== void 0 ? (n = u(e.value)) != null ? n : \"\" : typeof e.value == \"string\" ? e.value : \"\";\n    }();\n  Te(([n, F], [A, h]) => {\n    if (f.current) return;\n    let C = e.inputRef.current;\n    C && ((h === 0 && F === 1 || n !== A) && (C.value = n), requestAnimationFrame(() => {\n      if (f.current || !C || (g == null ? void 0 : g.activeElement) !== C) return;\n      let {\n        selectionStart: D,\n        selectionEnd: N\n      } = C;\n      Math.abs((N != null ? N : 0) - (D != null ? D : 0)) === 0 && D === 0 && C.setSelectionRange(C.value.length, C.value.length);\n    }));\n  }, [R, e.comboboxState, g]), Te(([n], [F]) => {\n    if (n === 0 && F === 1) {\n      if (f.current) return;\n      let A = e.inputRef.current;\n      if (!A) return;\n      let h = A.value,\n        {\n          selectionStart: C,\n          selectionEnd: D,\n          selectionDirection: N\n        } = A;\n      A.value = \"\", A.value = h, N !== null ? A.setSelectionRange(C, D, N) : A.setSelectionRange(C, D);\n    }\n  }, [e.comboboxState]);\n  let s = B(!1),\n    I = m(() => {\n      s.current = !0;\n    }),\n    V = m(() => {\n      v.nextFrame(() => {\n        s.current = !1;\n      });\n    }),\n    _ = m(n => {\n      switch (f.current = !0, n.key) {\n        case M.Enter:\n          if (f.current = !1, e.comboboxState !== 0 || s.current) return;\n          if (n.preventDefault(), n.stopPropagation(), e.activeOptionIndex === null) {\n            l.closeCombobox();\n            return;\n          }\n          l.selectActiveOption(), e.mode === 0 && l.closeCombobox();\n          break;\n        case M.ArrowDown:\n          return f.current = !1, n.preventDefault(), n.stopPropagation(), W(e.comboboxState, {\n            [0]: () => l.goToOption(y.Next),\n            [1]: () => l.openCombobox()\n          });\n        case M.ArrowUp:\n          return f.current = !1, n.preventDefault(), n.stopPropagation(), W(e.comboboxState, {\n            [0]: () => l.goToOption(y.Previous),\n            [1]: () => {\n              l.openCombobox(), v.nextFrame(() => {\n                e.value || l.goToOption(y.Last);\n              });\n            }\n          });\n        case M.Home:\n          if (n.shiftKey) break;\n          return f.current = !1, n.preventDefault(), n.stopPropagation(), l.goToOption(y.First);\n        case M.PageUp:\n          return f.current = !1, n.preventDefault(), n.stopPropagation(), l.goToOption(y.First);\n        case M.End:\n          if (n.shiftKey) break;\n          return f.current = !1, n.preventDefault(), n.stopPropagation(), l.goToOption(y.Last);\n        case M.PageDown:\n          return f.current = !1, n.preventDefault(), n.stopPropagation(), l.goToOption(y.Last);\n        case M.Escape:\n          return f.current = !1, e.comboboxState !== 0 ? void 0 : (n.preventDefault(), e.optionsRef.current && !e.optionsPropsRef.current.static && n.stopPropagation(), e.nullable && e.mode === 0 && e.value === null && S(), l.closeCombobox());\n        case M.Tab:\n          if (f.current = !1, e.comboboxState !== 0) return;\n          e.mode === 0 && e.activationTrigger !== 1 && l.selectActiveOption(), l.closeCombobox();\n          break;\n      }\n    }),\n    E = m(n => {\n      i == null || i(n), e.nullable && e.mode === 0 && n.target.value === \"\" && S(), l.openCombobox();\n    }),\n    k = m(n => {\n      var A, h, C;\n      let F = (A = n.relatedTarget) != null ? A : xe.find(D => D !== n.currentTarget);\n      if (f.current = !1, !((h = e.optionsRef.current) != null && h.contains(F)) && !((C = e.buttonRef.current) != null && C.contains(F)) && e.comboboxState === 0) return n.preventDefault(), e.mode === 0 && (e.nullable && e.value === null ? S() : e.activationTrigger !== 1 && l.selectActiveOption()), l.closeCombobox();\n    }),\n    J = m(n => {\n      var A, h, C;\n      let F = (A = n.relatedTarget) != null ? A : xe.find(D => D !== n.currentTarget);\n      (h = e.buttonRef.current) != null && h.contains(F) || (C = e.optionsRef.current) != null && C.contains(F) || e.disabled || e.immediate && e.comboboxState !== 0 && (l.openCombobox(), v.nextFrame(() => {\n        l.setActivationTrigger(1);\n      }));\n    }),\n    K = pe(() => {\n      if (e.labelId) return [e.labelId].join(\" \");\n    }, [e.labelId]),\n    z = U(() => ({\n      open: e.comboboxState === 0,\n      disabled: e.disabled\n    }), [e]),\n    te = {\n      ref: T,\n      id: a,\n      role: \"combobox\",\n      type: p,\n      \"aria-controls\": (X = e.optionsRef.current) == null ? void 0 : X.id,\n      \"aria-expanded\": e.comboboxState === 0,\n      \"aria-activedescendant\": e.activeOptionIndex === null ? void 0 : e.virtual ? (x = e.options.find(n => {\n        var F;\n        return !((F = e.virtual) != null && F.disabled(n.dataRef.current.value)) && e.compare(n.dataRef.current.value, e.virtual.options[e.activeOptionIndex]);\n      })) == null ? void 0 : x.id : (O = e.options[e.activeOptionIndex]) == null ? void 0 : O.id,\n      \"aria-labelledby\": K,\n      \"aria-autocomplete\": \"list\",\n      defaultValue: (oe = (L = t.defaultValue) != null ? L : e.defaultValue !== void 0 ? u == null ? void 0 : u(e.defaultValue) : null) != null ? oe : e.defaultValue,\n      disabled: e.disabled,\n      onCompositionStart: I,\n      onCompositionEnd: V,\n      onKeyDown: _,\n      onChange: E,\n      onFocus: J,\n      onBlur: k\n    };\n  return q({\n    ourProps: te,\n    theirProps: c,\n    slot: z,\n    defaultTag: nt,\n    name: \"Combobox.Input\"\n  });\n}\nlet at = \"button\";\nfunction lt(t, r) {\n  var S;\n  let o = j(\"Combobox.Button\"),\n    a = ee(\"Combobox.Button\"),\n    i = Z(o.buttonRef, r),\n    u = Q(),\n    {\n      id: p = `headlessui-combobox-button-${u}`,\n      ...c\n    } = t,\n    e = se(),\n    l = m(R => {\n      switch (R.key) {\n        case M.ArrowDown:\n          return R.preventDefault(), R.stopPropagation(), o.comboboxState === 1 && a.openCombobox(), e.nextFrame(() => {\n            var s;\n            return (s = o.inputRef.current) == null ? void 0 : s.focus({\n              preventScroll: !0\n            });\n          });\n        case M.ArrowUp:\n          return R.preventDefault(), R.stopPropagation(), o.comboboxState === 1 && (a.openCombobox(), e.nextFrame(() => {\n            o.value || a.goToOption(y.Last);\n          })), e.nextFrame(() => {\n            var s;\n            return (s = o.inputRef.current) == null ? void 0 : s.focus({\n              preventScroll: !0\n            });\n          });\n        case M.Escape:\n          return o.comboboxState !== 0 ? void 0 : (R.preventDefault(), o.optionsRef.current && !o.optionsPropsRef.current.static && R.stopPropagation(), a.closeCombobox(), e.nextFrame(() => {\n            var s;\n            return (s = o.inputRef.current) == null ? void 0 : s.focus({\n              preventScroll: !0\n            });\n          }));\n        default:\n          return;\n      }\n    }),\n    T = m(R => {\n      if (Xe(R.currentTarget)) return R.preventDefault();\n      o.comboboxState === 0 ? a.closeCombobox() : (R.preventDefault(), a.openCombobox()), e.nextFrame(() => {\n        var s;\n        return (s = o.inputRef.current) == null ? void 0 : s.focus({\n          preventScroll: !0\n        });\n      });\n    }),\n    g = pe(() => {\n      if (o.labelId) return [o.labelId, p].join(\" \");\n    }, [o.labelId, p]),\n    f = U(() => ({\n      open: o.comboboxState === 0,\n      disabled: o.disabled,\n      value: o.value\n    }), [o]),\n    v = {\n      ref: i,\n      id: p,\n      type: Be(t, o.buttonRef),\n      tabIndex: -1,\n      \"aria-haspopup\": \"listbox\",\n      \"aria-controls\": (S = o.optionsRef.current) == null ? void 0 : S.id,\n      \"aria-expanded\": o.comboboxState === 0,\n      \"aria-labelledby\": g,\n      disabled: o.disabled,\n      onClick: T,\n      onKeyDown: l\n    };\n  return q({\n    ourProps: v,\n    theirProps: c,\n    slot: f,\n    defaultTag: at,\n    name: \"Combobox.Button\"\n  });\n}\nlet it = \"label\";\nfunction ut(t, r) {\n  let o = Q(),\n    {\n      id: a = `headlessui-combobox-label-${o}`,\n      ...i\n    } = t,\n    u = j(\"Combobox.Label\"),\n    p = ee(\"Combobox.Label\"),\n    c = Z(u.labelRef, r);\n  H(() => p.registerLabel(a), [a]);\n  let e = m(() => {\n      var g;\n      return (g = u.inputRef.current) == null ? void 0 : g.focus({\n        preventScroll: !0\n      });\n    }),\n    l = U(() => ({\n      open: u.comboboxState === 0,\n      disabled: u.disabled\n    }), [u]);\n  return q({\n    ourProps: {\n      ref: c,\n      id: a,\n      onClick: e\n    },\n    theirProps: i,\n    slot: l,\n    defaultTag: it,\n    name: \"Combobox.Label\"\n  });\n}\nlet pt = \"ul\",\n  st = Oe.RenderStrategy | Oe.Static;\nfunction dt(t, r) {\n  let o = Q(),\n    {\n      id: a = `headlessui-combobox-options-${o}`,\n      hold: i = !1,\n      ...u\n    } = t,\n    p = j(\"Combobox.Options\"),\n    c = Z(p.optionsRef, r),\n    e = Ge(),\n    l = (() => e !== null ? (e & re.Open) === re.Open : p.comboboxState === 0)();\n  H(() => {\n    var v;\n    p.optionsPropsRef.current.static = (v = t.static) != null ? v : !1;\n  }, [p.optionsPropsRef, t.static]), H(() => {\n    p.optionsPropsRef.current.hold = i;\n  }, [p.optionsPropsRef, i]), we({\n    container: p.optionsRef.current,\n    enabled: p.comboboxState === 0,\n    accept(v) {\n      return v.getAttribute(\"role\") === \"option\" ? NodeFilter.FILTER_REJECT : v.hasAttribute(\"role\") ? NodeFilter.FILTER_SKIP : NodeFilter.FILTER_ACCEPT;\n    },\n    walk(v) {\n      v.setAttribute(\"role\", \"none\");\n    }\n  });\n  let T = pe(() => {\n      var v, S;\n      return (S = p.labelId) != null ? S : (v = p.buttonRef.current) == null ? void 0 : v.id;\n    }, [p.labelId, p.buttonRef.current]),\n    g = U(() => ({\n      open: p.comboboxState === 0,\n      option: void 0\n    }), [p]),\n    f = {\n      \"aria-labelledby\": T,\n      role: \"listbox\",\n      \"aria-multiselectable\": p.mode === 1 ? !0 : void 0,\n      id: a,\n      ref: c\n    };\n  return p.virtual && p.comboboxState === 0 && Object.assign(u, {\n    children: w.createElement(Ze, null, u.children)\n  }), q({\n    ourProps: f,\n    theirProps: u,\n    slot: g,\n    defaultTag: pt,\n    features: st,\n    visible: l,\n    name: \"Combobox.Options\"\n  });\n}\nlet bt = \"li\";\nfunction ct(t, r) {\n  var X;\n  let o = Q(),\n    {\n      id: a = `headlessui-combobox-option-${o}`,\n      disabled: i = !1,\n      value: u,\n      order: p = null,\n      ...c\n    } = t,\n    e = j(\"Combobox.Option\"),\n    l = ee(\"Combobox.Option\"),\n    T = e.virtual ? e.activeOptionIndex === e.calculateIndex(u) : e.activeOptionIndex === null ? !1 : ((X = e.options[e.activeOptionIndex]) == null ? void 0 : X.id) === a,\n    g = e.isSelected(u),\n    f = B(null),\n    v = De({\n      disabled: i,\n      value: u,\n      domRef: f,\n      order: p\n    }),\n    S = ue(Ce),\n    R = Z(r, f, S ? S.measureElement : null),\n    s = m(() => l.onChange(u));\n  H(() => l.registerOption(a, v), [v, a]);\n  let I = B(!(e.virtual || e.__demoMode));\n  H(() => {\n    if (!e.virtual || !e.__demoMode) return;\n    let x = ve();\n    return x.requestAnimationFrame(() => {\n      I.current = !0;\n    }), x.dispose;\n  }, [e.virtual, e.__demoMode]), H(() => {\n    if (!I.current || e.comboboxState !== 0 || !T || e.activationTrigger === 0) return;\n    let x = ve();\n    return x.requestAnimationFrame(() => {\n      var O, L;\n      (L = (O = f.current) == null ? void 0 : O.scrollIntoView) == null || L.call(O, {\n        block: \"nearest\"\n      });\n    }), x.dispose;\n  }, [f, T, e.comboboxState, e.activationTrigger, e.activeOptionIndex]);\n  let V = m(x => {\n      var O;\n      if (i || (O = e.virtual) != null && O.disabled(u)) return x.preventDefault();\n      s(), Ke() || requestAnimationFrame(() => {\n        var L;\n        return (L = e.inputRef.current) == null ? void 0 : L.focus({\n          preventScroll: !0\n        });\n      }), e.mode === 0 && requestAnimationFrame(() => l.closeCombobox());\n    }),\n    _ = m(() => {\n      var O;\n      if (i || (O = e.virtual) != null && O.disabled(u)) return l.goToOption(y.Nothing);\n      let x = e.calculateIndex(u);\n      l.goToOption(y.Specific, x);\n    }),\n    E = ke(),\n    k = m(x => E.update(x)),\n    J = m(x => {\n      var L;\n      if (!E.wasMoved(x) || i || (L = e.virtual) != null && L.disabled(u) || T) return;\n      let O = e.calculateIndex(u);\n      l.goToOption(y.Specific, O, 0);\n    }),\n    K = m(x => {\n      var O;\n      E.wasMoved(x) && (i || (O = e.virtual) != null && O.disabled(u) || T && (e.optionsPropsRef.current.hold || l.goToOption(y.Nothing)));\n    }),\n    z = U(() => ({\n      active: T,\n      selected: g,\n      disabled: i\n    }), [T, g, i]);\n  return q({\n    ourProps: {\n      id: a,\n      ref: R,\n      role: \"option\",\n      tabIndex: i === !0 ? void 0 : -1,\n      \"aria-disabled\": i === !0 ? !0 : void 0,\n      \"aria-selected\": g,\n      disabled: void 0,\n      onClick: V,\n      onFocus: _,\n      onPointerEnter: k,\n      onMouseEnter: k,\n      onPointerMove: J,\n      onMouseMove: J,\n      onPointerLeave: K,\n      onMouseLeave: K\n    },\n    theirProps: c,\n    slot: z,\n    defaultTag: bt,\n    name: \"Combobox.Option\"\n  });\n}\nlet ft = $(ot),\n  mt = $(lt),\n  Tt = $(rt),\n  xt = $(ut),\n  gt = $(dt),\n  vt = $(ct),\n  qt = Object.assign(ft, {\n    Input: Tt,\n    Button: mt,\n    Label: xt,\n    Options: gt,\n    Option: vt\n  });\nexport { qt as Combobox };", "map": {"version": 3, "names": ["useVirtualizer", "Ee", "w", "createContext", "ie", "createRef", "Pe", "Fragment", "me", "useCallback", "Ie", "useContext", "ue", "useEffect", "Ve", "useMemo", "U", "useReducer", "_e", "useRef", "B", "useState", "Fe", "useComputed", "pe", "useControllable", "Le", "useDisposables", "se", "useEvent", "m", "useId", "Q", "useIsoMorphicEffect", "H", "useLatestValue", "De", "useOutsideClick", "Me", "useOwnerDocument", "he", "useResolveButtonType", "Be", "useSyncRefs", "Z", "useTrackedPointer", "ke", "useTreeWalker", "we", "useWatch", "Te", "Features", "Ue", "Hidden", "He", "OpenClosedProvider", "Ne", "State", "re", "useOpenClosed", "Ge", "history", "xe", "isDisabledReactIssue7711", "Xe", "calculateActiveIndex", "ge", "Focus", "y", "disposables", "ve", "sortByDomNode", "je", "objectToFormEntries", "Je", "match", "W", "isMobile", "<PERSON>", "compact", "We", "Oe", "forwardRefWithAs", "$", "render", "q", "Keys", "M", "$e", "o", "Open", "Closed", "qe", "Single", "Multi", "ze", "a", "Pointer", "Other", "Ye", "e", "OpenCombobox", "CloseCombobox", "GoToOption", "RegisterOption", "UnregisterOption", "RegisterLabel", "SetActivationTrigger", "UpdateVirtualOptions", "de", "t", "r", "activeOptionIndex", "options", "slice", "i", "length", "dataRef", "current", "order", "sort", "p", "c", "domRef", "u", "indexOf", "Qe", "disabled", "comboboxState", "value", "calculateIndex", "l", "optionsRef", "optionsPropsRef", "static", "virtual", "T", "focus", "Specific", "idx", "resolveItems", "resolveActiveIndex", "f", "v", "findIndex", "S", "resolveDisabled", "resolveId", "Error", "g", "trigger", "activationTrigger", "id", "payload", "push", "isSelected", "__demoMode", "filter", "splice", "labelId", "Object", "assign", "be", "displayName", "ee", "captureStackTrace", "Ce", "Ze", "j", "window", "getComputedStyle", "parseFloat", "paddingBlockStart", "paddingTop", "paddingBlockEnd", "paddingBottom", "scrollPaddingStart", "scrollPaddingEnd", "count", "estimateSize", "getScrollElement", "overscan", "createElement", "Provider", "style", "position", "width", "height", "getTotalSize", "ref", "process", "env", "JEST_WORKER_ID", "scrollToIndex", "getVirtualItems", "map", "key", "cloneElement", "children", "call", "option", "index", "open", "top", "left", "transform", "start", "overflowAnchor", "ce", "et", "type", "tt", "ot", "fe", "defaultValue", "onChange", "form", "name", "by", "nullable", "multiple", "immediate", "R", "s", "I", "V", "_", "E", "k", "J", "hold", "K", "z", "te", "X", "x", "d", "b", "P", "O", "L", "n", "mode", "some", "oe", "labelRef", "inputRef", "buttonRef", "G", "Y", "compare", "isActive", "le", "closeCombobox", "F", "activeIndex", "activeOption", "A", "ae", "goToOption", "h", "C", "D", "N", "ye", "Re", "registerOption", "registerLabel", "openCombobox", "setActivationTrigger", "selectActiveOption", "Ae", "ne", "Se", "addEventListener", "features", "closest", "as", "hidden", "readOnly", "ourProps", "theirProps", "slot", "defaultTag", "nt", "rt", "displayValue", "scrollTop", "Nothing", "requestAnimationFrame", "activeElement", "selectionStart", "selectionEnd", "Math", "abs", "setSelectionRange", "selectionDirection", "next<PERSON><PERSON><PERSON>", "Enter", "preventDefault", "stopPropagation", "ArrowDown", "Next", "ArrowUp", "Previous", "Last", "Home", "shift<PERSON>ey", "First", "PageUp", "End", "PageDown", "Escape", "Tab", "target", "relatedTarget", "find", "currentTarget", "contains", "join", "role", "onCompositionStart", "onCompositionEnd", "onKeyDown", "onFocus", "onBlur", "at", "lt", "preventScroll", "tabIndex", "onClick", "it", "ut", "pt", "st", "RenderStrategy", "Static", "dt", "container", "enabled", "accept", "getAttribute", "Node<PERSON><PERSON><PERSON>", "FILTER_REJECT", "hasAttribute", "FILTER_SKIP", "FILTER_ACCEPT", "walk", "setAttribute", "visible", "bt", "ct", "measureElement", "dispose", "scrollIntoView", "block", "update", "wasMoved", "active", "selected", "onPointerEnter", "onMouseEnter", "onPointerMove", "onMouseMove", "onPointerLeave", "onMouseLeave", "ft", "mt", "Tt", "xt", "gt", "vt", "qt", "Input", "<PERSON><PERSON>", "Label", "Options", "Option", "Combobox"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/@headlessui/react/dist/components/combobox/combobox.js"], "sourcesContent": ["import{useVirtualizer as Ee}from\"@tanstack/react-virtual\";import w,{createContext as ie,createRef as P<PERSON>,Fragment as me,use<PERSON><PERSON>back as Ie,useContext as ue,useEffect as Ve,useMemo as U,useReducer as _e,useRef as B,useState as Fe}from\"react\";import{useComputed as pe}from'../../hooks/use-computed.js';import{useControllable as Le}from'../../hooks/use-controllable.js';import{useDisposables as se}from'../../hooks/use-disposables.js';import{useEvent as m}from'../../hooks/use-event.js';import{useId as Q}from'../../hooks/use-id.js';import{useIsoMorphicEffect as H}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as De}from'../../hooks/use-latest-value.js';import{useOutsideClick as Me}from'../../hooks/use-outside-click.js';import{useOwnerDocument as he}from'../../hooks/use-owner.js';import{useResolveButtonType as Be}from'../../hooks/use-resolve-button-type.js';import{useSyncRefs as Z}from'../../hooks/use-sync-refs.js';import{useTrackedPointer as ke}from'../../hooks/use-tracked-pointer.js';import{useTreeWalker as we}from'../../hooks/use-tree-walker.js';import{useWatch as Te}from'../../hooks/use-watch.js';import{Features as Ue,Hidden as He}from'../../internal/hidden.js';import{OpenClosedProvider as Ne,State as re,useOpenClosed as Ge}from'../../internal/open-closed.js';import{history as xe}from'../../utils/active-element-history.js';import{isDisabledReactIssue7711 as Xe}from'../../utils/bugs.js';import{calculateActiveIndex as ge,Focus as y}from'../../utils/calculate-active-index.js';import{disposables as ve}from'../../utils/disposables.js';import{sortByDomNode as je}from'../../utils/focus-management.js';import{objectToFormEntries as Je}from'../../utils/form.js';import{match as W}from'../../utils/match.js';import{isMobile as Ke}from'../../utils/platform.js';import{compact as We,Features as Oe,forwardRefWithAs as $,render as q}from'../../utils/render.js';import{Keys as M}from'../keyboard.js';var $e=(o=>(o[o.Open=0]=\"Open\",o[o.Closed=1]=\"Closed\",o))($e||{}),qe=(o=>(o[o.Single=0]=\"Single\",o[o.Multi=1]=\"Multi\",o))(qe||{}),ze=(a=>(a[a.Pointer=0]=\"Pointer\",a[a.Focus=1]=\"Focus\",a[a.Other=2]=\"Other\",a))(ze||{}),Ye=(e=>(e[e.OpenCombobox=0]=\"OpenCombobox\",e[e.CloseCombobox=1]=\"CloseCombobox\",e[e.GoToOption=2]=\"GoToOption\",e[e.RegisterOption=3]=\"RegisterOption\",e[e.UnregisterOption=4]=\"UnregisterOption\",e[e.RegisterLabel=5]=\"RegisterLabel\",e[e.SetActivationTrigger=6]=\"SetActivationTrigger\",e[e.UpdateVirtualOptions=7]=\"UpdateVirtualOptions\",e))(Ye||{});function de(t,r=o=>o){let o=t.activeOptionIndex!==null?t.options[t.activeOptionIndex]:null,a=r(t.options.slice()),i=a.length>0&&a[0].dataRef.current.order!==null?a.sort((p,c)=>p.dataRef.current.order-c.dataRef.current.order):je(a,p=>p.dataRef.current.domRef.current),u=o?i.indexOf(o):null;return u===-1&&(u=null),{options:i,activeOptionIndex:u}}let Qe={[1](t){var r;return(r=t.dataRef.current)!=null&&r.disabled||t.comboboxState===1?t:{...t,activeOptionIndex:null,comboboxState:1}},[0](t){var r,o;if((r=t.dataRef.current)!=null&&r.disabled||t.comboboxState===0)return t;if((o=t.dataRef.current)!=null&&o.value){let a=t.dataRef.current.calculateIndex(t.dataRef.current.value);if(a!==-1)return{...t,activeOptionIndex:a,comboboxState:0}}return{...t,comboboxState:0}},[2](t,r){var u,p,c,e,l;if((u=t.dataRef.current)!=null&&u.disabled||(p=t.dataRef.current)!=null&&p.optionsRef.current&&!((c=t.dataRef.current)!=null&&c.optionsPropsRef.current.static)&&t.comboboxState===1)return t;if(t.virtual){let T=r.focus===y.Specific?r.idx:ge(r,{resolveItems:()=>t.virtual.options,resolveActiveIndex:()=>{var f,v;return(v=(f=t.activeOptionIndex)!=null?f:t.virtual.options.findIndex(S=>!t.virtual.disabled(S)))!=null?v:null},resolveDisabled:t.virtual.disabled,resolveId(){throw new Error(\"Function not implemented.\")}}),g=(e=r.trigger)!=null?e:2;return t.activeOptionIndex===T&&t.activationTrigger===g?t:{...t,activeOptionIndex:T,activationTrigger:g}}let o=de(t);if(o.activeOptionIndex===null){let T=o.options.findIndex(g=>!g.dataRef.current.disabled);T!==-1&&(o.activeOptionIndex=T)}let a=r.focus===y.Specific?r.idx:ge(r,{resolveItems:()=>o.options,resolveActiveIndex:()=>o.activeOptionIndex,resolveId:T=>T.id,resolveDisabled:T=>T.dataRef.current.disabled}),i=(l=r.trigger)!=null?l:2;return t.activeOptionIndex===a&&t.activationTrigger===i?t:{...t,...o,activeOptionIndex:a,activationTrigger:i}},[3]:(t,r)=>{var u,p,c;if((u=t.dataRef.current)!=null&&u.virtual)return{...t,options:[...t.options,r.payload]};let o=r.payload,a=de(t,e=>(e.push(o),e));t.activeOptionIndex===null&&(p=t.dataRef.current)!=null&&p.isSelected(r.payload.dataRef.current.value)&&(a.activeOptionIndex=a.options.indexOf(o));let i={...t,...a,activationTrigger:2};return(c=t.dataRef.current)!=null&&c.__demoMode&&t.dataRef.current.value===void 0&&(i.activeOptionIndex=0),i},[4]:(t,r)=>{var a;if((a=t.dataRef.current)!=null&&a.virtual)return{...t,options:t.options.filter(i=>i.id!==r.id)};let o=de(t,i=>{let u=i.findIndex(p=>p.id===r.id);return u!==-1&&i.splice(u,1),i});return{...t,...o,activationTrigger:2}},[5]:(t,r)=>t.labelId===r.id?t:{...t,labelId:r.id},[6]:(t,r)=>t.activationTrigger===r.trigger?t:{...t,activationTrigger:r.trigger},[7]:(t,r)=>{var a;if(((a=t.virtual)==null?void 0:a.options)===r.options)return t;let o=t.activeOptionIndex;if(t.activeOptionIndex!==null){let i=r.options.indexOf(t.virtual.options[t.activeOptionIndex]);i!==-1?o=i:o=null}return{...t,activeOptionIndex:o,virtual:Object.assign({},t.virtual,{options:r.options})}}},be=ie(null);be.displayName=\"ComboboxActionsContext\";function ee(t){let r=ue(be);if(r===null){let o=new Error(`<${t} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,ee),o}return r}let Ce=ie(null);function Ze(t){var c;let r=j(\"VirtualProvider\"),[o,a]=U(()=>{let e=r.optionsRef.current;if(!e)return[0,0];let l=window.getComputedStyle(e);return[parseFloat(l.paddingBlockStart||l.paddingTop),parseFloat(l.paddingBlockEnd||l.paddingBottom)]},[r.optionsRef.current]),i=Ee({scrollPaddingStart:o,scrollPaddingEnd:a,count:r.virtual.options.length,estimateSize(){return 40},getScrollElement(){var e;return(e=r.optionsRef.current)!=null?e:null},overscan:12}),[u,p]=Fe(0);return H(()=>{p(e=>e+1)},[(c=r.virtual)==null?void 0:c.options]),w.createElement(Ce.Provider,{value:i},w.createElement(\"div\",{style:{position:\"relative\",width:\"100%\",height:`${i.getTotalSize()}px`},ref:e=>{if(e){if(typeof process!=\"undefined\"&&process.env.JEST_WORKER_ID!==void 0||r.activationTrigger===0)return;r.activeOptionIndex!==null&&r.virtual.options.length>r.activeOptionIndex&&i.scrollToIndex(r.activeOptionIndex)}}},i.getVirtualItems().map(e=>{var l;return w.createElement(me,{key:e.key},w.cloneElement((l=t.children)==null?void 0:l.call(t,{option:r.virtual.options[e.index],open:r.comboboxState===0}),{key:`${u}-${e.key}`,\"data-index\":e.index,\"aria-setsize\":r.virtual.options.length,\"aria-posinset\":e.index+1,style:{position:\"absolute\",top:0,left:0,transform:`translateY(${e.start}px)`,overflowAnchor:\"none\"}}))})))}let ce=ie(null);ce.displayName=\"ComboboxDataContext\";function j(t){let r=ue(ce);if(r===null){let o=new Error(`<${t} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,j),o}return r}function et(t,r){return W(r.type,Qe,t,r)}let tt=me;function ot(t,r){var fe;let{value:o,defaultValue:a,onChange:i,form:u,name:p,by:c=null,disabled:e=!1,__demoMode:l=!1,nullable:T=!1,multiple:g=!1,immediate:f=!1,virtual:v=null,...S}=t,R=!1,s=null,[I=g?[]:void 0,V]=Le(o,i,a),[_,E]=_e(et,{dataRef:Pe(),comboboxState:l?0:1,options:[],virtual:s?{options:s.options,disabled:(fe=s.disabled)!=null?fe:()=>!1}:null,activeOptionIndex:null,activationTrigger:2,labelId:null}),k=B(!1),J=B({static:!1,hold:!1}),K=B(null),z=B(null),te=B(null),X=B(null),x=m(typeof c==\"string\"?(d,b)=>{let P=c;return(d==null?void 0:d[P])===(b==null?void 0:b[P])}:c!=null?c:(d,b)=>d===b),O=m(d=>s?c===null?s.options.indexOf(d):s.options.findIndex(b=>x(b,d)):_.options.findIndex(b=>x(b.dataRef.current.value,d))),L=Ie(d=>W(n.mode,{[1]:()=>I.some(b=>x(b,d)),[0]:()=>x(I,d)}),[I]),oe=m(d=>_.activeOptionIndex===O(d)),n=U(()=>({..._,immediate:R,optionsPropsRef:J,labelRef:K,inputRef:z,buttonRef:te,optionsRef:X,value:I,defaultValue:a,disabled:e,mode:g?1:0,virtual:_.virtual,get activeOptionIndex(){if(k.current&&_.activeOptionIndex===null&&(s?s.options.length>0:_.options.length>0)){if(s){let b=s.options.findIndex(P=>{var G,Y;return!((Y=(G=s==null?void 0:s.disabled)==null?void 0:G.call(s,P))!=null&&Y)});if(b!==-1)return b}let d=_.options.findIndex(b=>!b.dataRef.current.disabled);if(d!==-1)return d}return _.activeOptionIndex},calculateIndex:O,compare:x,isSelected:L,isActive:oe,nullable:T,__demoMode:l}),[I,a,e,g,T,l,_,s]);H(()=>{s&&E({type:7,options:s.options})},[s,s==null?void 0:s.options]),H(()=>{_.dataRef.current=n},[n]),Me([n.buttonRef,n.inputRef,n.optionsRef],()=>le.closeCombobox(),n.comboboxState===0);let F=U(()=>{var d,b,P;return{open:n.comboboxState===0,disabled:e,activeIndex:n.activeOptionIndex,activeOption:n.activeOptionIndex===null?null:n.virtual?n.virtual.options[(d=n.activeOptionIndex)!=null?d:0]:(P=(b=n.options[n.activeOptionIndex])==null?void 0:b.dataRef.current.value)!=null?P:null,value:I}},[n,e,I]),A=m(()=>{if(n.activeOptionIndex!==null){if(n.virtual)ae(n.virtual.options[n.activeOptionIndex]);else{let{dataRef:d}=n.options[n.activeOptionIndex];ae(d.current.value)}le.goToOption(y.Specific,n.activeOptionIndex)}}),h=m(()=>{E({type:0}),k.current=!0}),C=m(()=>{E({type:1}),k.current=!1}),D=m((d,b,P)=>(k.current=!1,d===y.Specific?E({type:2,focus:y.Specific,idx:b,trigger:P}):E({type:2,focus:d,trigger:P}))),N=m((d,b)=>(E({type:3,payload:{id:d,dataRef:b}}),()=>{n.isActive(b.current.value)&&(k.current=!0),E({type:4,id:d})})),ye=m(d=>(E({type:5,id:d}),()=>E({type:5,id:null}))),ae=m(d=>W(n.mode,{[0](){return V==null?void 0:V(d)},[1](){let b=n.value.slice(),P=b.findIndex(G=>x(G,d));return P===-1?b.push(d):b.splice(P,1),V==null?void 0:V(b)}})),Re=m(d=>{E({type:6,trigger:d})}),le=U(()=>({onChange:ae,registerOption:N,registerLabel:ye,goToOption:D,closeCombobox:C,openCombobox:h,setActivationTrigger:Re,selectActiveOption:A}),[]),Ae=r===null?{}:{ref:r},ne=B(null),Se=se();return Ve(()=>{ne.current&&a!==void 0&&Se.addEventListener(ne.current,\"reset\",()=>{V==null||V(a)})},[ne,V]),w.createElement(be.Provider,{value:le},w.createElement(ce.Provider,{value:n},w.createElement(Ne,{value:W(n.comboboxState,{[0]:re.Open,[1]:re.Closed})},p!=null&&I!=null&&Je({[p]:I}).map(([d,b],P)=>w.createElement(He,{features:Ue.Hidden,ref:P===0?G=>{var Y;ne.current=(Y=G==null?void 0:G.closest(\"form\"))!=null?Y:null}:void 0,...We({key:d,as:\"input\",type:\"hidden\",hidden:!0,readOnly:!0,form:u,disabled:e,name:d,value:b})})),q({ourProps:Ae,theirProps:S,slot:F,defaultTag:tt,name:\"Combobox\"}))))}let nt=\"input\";function rt(t,r){var X,x,O,L,oe;let o=Q(),{id:a=`headlessui-combobox-input-${o}`,onChange:i,displayValue:u,type:p=\"text\",...c}=t,e=j(\"Combobox.Input\"),l=ee(\"Combobox.Input\"),T=Z(e.inputRef,r),g=he(e.inputRef),f=B(!1),v=se(),S=m(()=>{l.onChange(null),e.optionsRef.current&&(e.optionsRef.current.scrollTop=0),l.goToOption(y.Nothing)}),R=function(){var n;return typeof u==\"function\"&&e.value!==void 0?(n=u(e.value))!=null?n:\"\":typeof e.value==\"string\"?e.value:\"\"}();Te(([n,F],[A,h])=>{if(f.current)return;let C=e.inputRef.current;C&&((h===0&&F===1||n!==A)&&(C.value=n),requestAnimationFrame(()=>{if(f.current||!C||(g==null?void 0:g.activeElement)!==C)return;let{selectionStart:D,selectionEnd:N}=C;Math.abs((N!=null?N:0)-(D!=null?D:0))===0&&D===0&&C.setSelectionRange(C.value.length,C.value.length)}))},[R,e.comboboxState,g]),Te(([n],[F])=>{if(n===0&&F===1){if(f.current)return;let A=e.inputRef.current;if(!A)return;let h=A.value,{selectionStart:C,selectionEnd:D,selectionDirection:N}=A;A.value=\"\",A.value=h,N!==null?A.setSelectionRange(C,D,N):A.setSelectionRange(C,D)}},[e.comboboxState]);let s=B(!1),I=m(()=>{s.current=!0}),V=m(()=>{v.nextFrame(()=>{s.current=!1})}),_=m(n=>{switch(f.current=!0,n.key){case M.Enter:if(f.current=!1,e.comboboxState!==0||s.current)return;if(n.preventDefault(),n.stopPropagation(),e.activeOptionIndex===null){l.closeCombobox();return}l.selectActiveOption(),e.mode===0&&l.closeCombobox();break;case M.ArrowDown:return f.current=!1,n.preventDefault(),n.stopPropagation(),W(e.comboboxState,{[0]:()=>l.goToOption(y.Next),[1]:()=>l.openCombobox()});case M.ArrowUp:return f.current=!1,n.preventDefault(),n.stopPropagation(),W(e.comboboxState,{[0]:()=>l.goToOption(y.Previous),[1]:()=>{l.openCombobox(),v.nextFrame(()=>{e.value||l.goToOption(y.Last)})}});case M.Home:if(n.shiftKey)break;return f.current=!1,n.preventDefault(),n.stopPropagation(),l.goToOption(y.First);case M.PageUp:return f.current=!1,n.preventDefault(),n.stopPropagation(),l.goToOption(y.First);case M.End:if(n.shiftKey)break;return f.current=!1,n.preventDefault(),n.stopPropagation(),l.goToOption(y.Last);case M.PageDown:return f.current=!1,n.preventDefault(),n.stopPropagation(),l.goToOption(y.Last);case M.Escape:return f.current=!1,e.comboboxState!==0?void 0:(n.preventDefault(),e.optionsRef.current&&!e.optionsPropsRef.current.static&&n.stopPropagation(),e.nullable&&e.mode===0&&e.value===null&&S(),l.closeCombobox());case M.Tab:if(f.current=!1,e.comboboxState!==0)return;e.mode===0&&e.activationTrigger!==1&&l.selectActiveOption(),l.closeCombobox();break}}),E=m(n=>{i==null||i(n),e.nullable&&e.mode===0&&n.target.value===\"\"&&S(),l.openCombobox()}),k=m(n=>{var A,h,C;let F=(A=n.relatedTarget)!=null?A:xe.find(D=>D!==n.currentTarget);if(f.current=!1,!((h=e.optionsRef.current)!=null&&h.contains(F))&&!((C=e.buttonRef.current)!=null&&C.contains(F))&&e.comboboxState===0)return n.preventDefault(),e.mode===0&&(e.nullable&&e.value===null?S():e.activationTrigger!==1&&l.selectActiveOption()),l.closeCombobox()}),J=m(n=>{var A,h,C;let F=(A=n.relatedTarget)!=null?A:xe.find(D=>D!==n.currentTarget);(h=e.buttonRef.current)!=null&&h.contains(F)||(C=e.optionsRef.current)!=null&&C.contains(F)||e.disabled||e.immediate&&e.comboboxState!==0&&(l.openCombobox(),v.nextFrame(()=>{l.setActivationTrigger(1)}))}),K=pe(()=>{if(e.labelId)return[e.labelId].join(\" \")},[e.labelId]),z=U(()=>({open:e.comboboxState===0,disabled:e.disabled}),[e]),te={ref:T,id:a,role:\"combobox\",type:p,\"aria-controls\":(X=e.optionsRef.current)==null?void 0:X.id,\"aria-expanded\":e.comboboxState===0,\"aria-activedescendant\":e.activeOptionIndex===null?void 0:e.virtual?(x=e.options.find(n=>{var F;return!((F=e.virtual)!=null&&F.disabled(n.dataRef.current.value))&&e.compare(n.dataRef.current.value,e.virtual.options[e.activeOptionIndex])}))==null?void 0:x.id:(O=e.options[e.activeOptionIndex])==null?void 0:O.id,\"aria-labelledby\":K,\"aria-autocomplete\":\"list\",defaultValue:(oe=(L=t.defaultValue)!=null?L:e.defaultValue!==void 0?u==null?void 0:u(e.defaultValue):null)!=null?oe:e.defaultValue,disabled:e.disabled,onCompositionStart:I,onCompositionEnd:V,onKeyDown:_,onChange:E,onFocus:J,onBlur:k};return q({ourProps:te,theirProps:c,slot:z,defaultTag:nt,name:\"Combobox.Input\"})}let at=\"button\";function lt(t,r){var S;let o=j(\"Combobox.Button\"),a=ee(\"Combobox.Button\"),i=Z(o.buttonRef,r),u=Q(),{id:p=`headlessui-combobox-button-${u}`,...c}=t,e=se(),l=m(R=>{switch(R.key){case M.ArrowDown:return R.preventDefault(),R.stopPropagation(),o.comboboxState===1&&a.openCombobox(),e.nextFrame(()=>{var s;return(s=o.inputRef.current)==null?void 0:s.focus({preventScroll:!0})});case M.ArrowUp:return R.preventDefault(),R.stopPropagation(),o.comboboxState===1&&(a.openCombobox(),e.nextFrame(()=>{o.value||a.goToOption(y.Last)})),e.nextFrame(()=>{var s;return(s=o.inputRef.current)==null?void 0:s.focus({preventScroll:!0})});case M.Escape:return o.comboboxState!==0?void 0:(R.preventDefault(),o.optionsRef.current&&!o.optionsPropsRef.current.static&&R.stopPropagation(),a.closeCombobox(),e.nextFrame(()=>{var s;return(s=o.inputRef.current)==null?void 0:s.focus({preventScroll:!0})}));default:return}}),T=m(R=>{if(Xe(R.currentTarget))return R.preventDefault();o.comboboxState===0?a.closeCombobox():(R.preventDefault(),a.openCombobox()),e.nextFrame(()=>{var s;return(s=o.inputRef.current)==null?void 0:s.focus({preventScroll:!0})})}),g=pe(()=>{if(o.labelId)return[o.labelId,p].join(\" \")},[o.labelId,p]),f=U(()=>({open:o.comboboxState===0,disabled:o.disabled,value:o.value}),[o]),v={ref:i,id:p,type:Be(t,o.buttonRef),tabIndex:-1,\"aria-haspopup\":\"listbox\",\"aria-controls\":(S=o.optionsRef.current)==null?void 0:S.id,\"aria-expanded\":o.comboboxState===0,\"aria-labelledby\":g,disabled:o.disabled,onClick:T,onKeyDown:l};return q({ourProps:v,theirProps:c,slot:f,defaultTag:at,name:\"Combobox.Button\"})}let it=\"label\";function ut(t,r){let o=Q(),{id:a=`headlessui-combobox-label-${o}`,...i}=t,u=j(\"Combobox.Label\"),p=ee(\"Combobox.Label\"),c=Z(u.labelRef,r);H(()=>p.registerLabel(a),[a]);let e=m(()=>{var g;return(g=u.inputRef.current)==null?void 0:g.focus({preventScroll:!0})}),l=U(()=>({open:u.comboboxState===0,disabled:u.disabled}),[u]);return q({ourProps:{ref:c,id:a,onClick:e},theirProps:i,slot:l,defaultTag:it,name:\"Combobox.Label\"})}let pt=\"ul\",st=Oe.RenderStrategy|Oe.Static;function dt(t,r){let o=Q(),{id:a=`headlessui-combobox-options-${o}`,hold:i=!1,...u}=t,p=j(\"Combobox.Options\"),c=Z(p.optionsRef,r),e=Ge(),l=(()=>e!==null?(e&re.Open)===re.Open:p.comboboxState===0)();H(()=>{var v;p.optionsPropsRef.current.static=(v=t.static)!=null?v:!1},[p.optionsPropsRef,t.static]),H(()=>{p.optionsPropsRef.current.hold=i},[p.optionsPropsRef,i]),we({container:p.optionsRef.current,enabled:p.comboboxState===0,accept(v){return v.getAttribute(\"role\")===\"option\"?NodeFilter.FILTER_REJECT:v.hasAttribute(\"role\")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(v){v.setAttribute(\"role\",\"none\")}});let T=pe(()=>{var v,S;return(S=p.labelId)!=null?S:(v=p.buttonRef.current)==null?void 0:v.id},[p.labelId,p.buttonRef.current]),g=U(()=>({open:p.comboboxState===0,option:void 0}),[p]),f={\"aria-labelledby\":T,role:\"listbox\",\"aria-multiselectable\":p.mode===1?!0:void 0,id:a,ref:c};return p.virtual&&p.comboboxState===0&&Object.assign(u,{children:w.createElement(Ze,null,u.children)}),q({ourProps:f,theirProps:u,slot:g,defaultTag:pt,features:st,visible:l,name:\"Combobox.Options\"})}let bt=\"li\";function ct(t,r){var X;let o=Q(),{id:a=`headlessui-combobox-option-${o}`,disabled:i=!1,value:u,order:p=null,...c}=t,e=j(\"Combobox.Option\"),l=ee(\"Combobox.Option\"),T=e.virtual?e.activeOptionIndex===e.calculateIndex(u):e.activeOptionIndex===null?!1:((X=e.options[e.activeOptionIndex])==null?void 0:X.id)===a,g=e.isSelected(u),f=B(null),v=De({disabled:i,value:u,domRef:f,order:p}),S=ue(Ce),R=Z(r,f,S?S.measureElement:null),s=m(()=>l.onChange(u));H(()=>l.registerOption(a,v),[v,a]);let I=B(!(e.virtual||e.__demoMode));H(()=>{if(!e.virtual||!e.__demoMode)return;let x=ve();return x.requestAnimationFrame(()=>{I.current=!0}),x.dispose},[e.virtual,e.__demoMode]),H(()=>{if(!I.current||e.comboboxState!==0||!T||e.activationTrigger===0)return;let x=ve();return x.requestAnimationFrame(()=>{var O,L;(L=(O=f.current)==null?void 0:O.scrollIntoView)==null||L.call(O,{block:\"nearest\"})}),x.dispose},[f,T,e.comboboxState,e.activationTrigger,e.activeOptionIndex]);let V=m(x=>{var O;if(i||(O=e.virtual)!=null&&O.disabled(u))return x.preventDefault();s(),Ke()||requestAnimationFrame(()=>{var L;return(L=e.inputRef.current)==null?void 0:L.focus({preventScroll:!0})}),e.mode===0&&requestAnimationFrame(()=>l.closeCombobox())}),_=m(()=>{var O;if(i||(O=e.virtual)!=null&&O.disabled(u))return l.goToOption(y.Nothing);let x=e.calculateIndex(u);l.goToOption(y.Specific,x)}),E=ke(),k=m(x=>E.update(x)),J=m(x=>{var L;if(!E.wasMoved(x)||i||(L=e.virtual)!=null&&L.disabled(u)||T)return;let O=e.calculateIndex(u);l.goToOption(y.Specific,O,0)}),K=m(x=>{var O;E.wasMoved(x)&&(i||(O=e.virtual)!=null&&O.disabled(u)||T&&(e.optionsPropsRef.current.hold||l.goToOption(y.Nothing)))}),z=U(()=>({active:T,selected:g,disabled:i}),[T,g,i]);return q({ourProps:{id:a,ref:R,role:\"option\",tabIndex:i===!0?void 0:-1,\"aria-disabled\":i===!0?!0:void 0,\"aria-selected\":g,disabled:void 0,onClick:V,onFocus:_,onPointerEnter:k,onMouseEnter:k,onPointerMove:J,onMouseMove:J,onPointerLeave:K,onMouseLeave:K},theirProps:c,slot:z,defaultTag:bt,name:\"Combobox.Option\"})}let ft=$(ot),mt=$(lt),Tt=$(rt),xt=$(ut),gt=$(dt),vt=$(ct),qt=Object.assign(ft,{Input:Tt,Button:mt,Label:xt,Options:gt,Option:vt});export{qt as Combobox};\n"], "mappings": "AAAA,SAAOA,cAAc,IAAIC,EAAE,QAAK,yBAAyB;AAAC,OAAOC,CAAC,IAAEC,aAAa,IAAIC,EAAE,EAACC,SAAS,IAAIC,EAAE,EAACC,QAAQ,IAAIC,EAAE,EAACC,WAAW,IAAIC,EAAE,EAACC,UAAU,IAAIC,EAAE,EAACC,SAAS,IAAIC,EAAE,EAACC,OAAO,IAAIC,CAAC,EAACC,UAAU,IAAIC,EAAE,EAACC,MAAM,IAAIC,CAAC,EAACC,QAAQ,IAAIC,EAAE,QAAK,OAAO;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,6BAA6B;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,gCAAgC;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,kCAAkC;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,oBAAoB,IAAIC,EAAE,QAAK,wCAAwC;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,iBAAiB,IAAIC,EAAE,QAAK,oCAAoC;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,gCAAgC;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,QAAQ,IAAIC,EAAE,EAACC,MAAM,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,kBAAkB,IAAIC,EAAE,EAACC,KAAK,IAAIC,EAAE,EAACC,aAAa,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,OAAO,IAAIC,EAAE,QAAK,uCAAuC;AAAC,SAAOC,wBAAwB,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,oBAAoB,IAAIC,EAAE,EAACC,KAAK,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,WAAW,IAAIC,EAAE,QAAK,4BAA4B;AAAC,SAAOC,aAAa,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,mBAAmB,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,QAAQ,IAAIC,EAAE,QAAK,yBAAyB;AAAC,SAAOC,OAAO,IAAIC,EAAE,EAAC7B,QAAQ,IAAI8B,EAAE,EAACC,gBAAgB,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,IAAI,IAAIC,CAAC,QAAK,gBAAgB;AAAC,IAAIC,EAAE,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACD,CAAC,CAACA,CAAC,CAACE,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACF,CAAC,CAAC,EAAED,EAAE,IAAE,CAAC,CAAC,CAAC;EAACI,EAAE,GAAC,CAACH,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACI,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACJ,CAAC,CAACA,CAAC,CAACK,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACL,CAAC,CAAC,EAAEG,EAAE,IAAE,CAAC,CAAC,CAAC;EAACG,EAAE,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,OAAO,GAAC,CAAC,CAAC,GAAC,SAAS,EAACD,CAAC,CAACA,CAAC,CAAC7B,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAAC6B,CAAC,CAACA,CAAC,CAACE,KAAK,GAAC,CAAC,CAAC,GAAC,OAAO,EAACF,CAAC,CAAC,EAAED,EAAE,IAAE,CAAC,CAAC,CAAC;EAACI,EAAE,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,YAAY,GAAC,CAAC,CAAC,GAAC,cAAc,EAACD,CAAC,CAACA,CAAC,CAACE,aAAa,GAAC,CAAC,CAAC,GAAC,eAAe,EAACF,CAAC,CAACA,CAAC,CAACG,UAAU,GAAC,CAAC,CAAC,GAAC,YAAY,EAACH,CAAC,CAACA,CAAC,CAACI,cAAc,GAAC,CAAC,CAAC,GAAC,gBAAgB,EAACJ,CAAC,CAACA,CAAC,CAACK,gBAAgB,GAAC,CAAC,CAAC,GAAC,kBAAkB,EAACL,CAAC,CAACA,CAAC,CAACM,aAAa,GAAC,CAAC,CAAC,GAAC,eAAe,EAACN,CAAC,CAACA,CAAC,CAACO,oBAAoB,GAAC,CAAC,CAAC,GAAC,sBAAsB,EAACP,CAAC,CAACA,CAAC,CAACQ,oBAAoB,GAAC,CAAC,CAAC,GAAC,sBAAsB,EAACR,CAAC,CAAC,EAAED,EAAE,IAAE,CAAC,CAAC,CAAC;AAAC,SAASU,EAAEA,CAACC,CAAC,EAACC,CAAC,GAACtB,CAAC,IAAEA,CAAC,EAAC;EAAC,IAAIA,CAAC,GAACqB,CAAC,CAACE,iBAAiB,KAAG,IAAI,GAACF,CAAC,CAACG,OAAO,CAACH,CAAC,CAACE,iBAAiB,CAAC,GAAC,IAAI;IAAChB,CAAC,GAACe,CAAC,CAACD,CAAC,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC;IAACC,CAAC,GAACnB,CAAC,CAACoB,MAAM,GAAC,CAAC,IAAEpB,CAAC,CAAC,CAAC,CAAC,CAACqB,OAAO,CAACC,OAAO,CAACC,KAAK,KAAG,IAAI,GAACvB,CAAC,CAACwB,IAAI,CAAC,CAACC,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACJ,OAAO,CAACC,OAAO,CAACC,KAAK,GAACG,CAAC,CAACL,OAAO,CAACC,OAAO,CAACC,KAAK,CAAC,GAAC/C,EAAE,CAACwB,CAAC,EAACyB,CAAC,IAAEA,CAAC,CAACJ,OAAO,CAACC,OAAO,CAACK,MAAM,CAACL,OAAO,CAAC;IAACM,CAAC,GAACnC,CAAC,GAAC0B,CAAC,CAACU,OAAO,CAACpC,CAAC,CAAC,GAAC,IAAI;EAAC,OAAOmC,CAAC,KAAG,CAAC,CAAC,KAAGA,CAAC,GAAC,IAAI,CAAC,EAAC;IAACX,OAAO,EAACE,CAAC;IAACH,iBAAiB,EAACY;EAAC,CAAC;AAAA;AAAC,IAAIE,EAAE,GAAC;IAAC,CAAC,CAAC,EAAEhB,CAAC,EAAC;MAAC,IAAIC,CAAC;MAAC,OAAM,CAACA,CAAC,GAACD,CAAC,CAACO,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEP,CAAC,CAACgB,QAAQ,IAAEjB,CAAC,CAACkB,aAAa,KAAG,CAAC,GAAClB,CAAC,GAAC;QAAC,GAAGA,CAAC;QAACE,iBAAiB,EAAC,IAAI;QAACgB,aAAa,EAAC;MAAC,CAAC;IAAA,CAAC;IAAC,CAAC,CAAC,EAAElB,CAAC,EAAC;MAAC,IAAIC,CAAC,EAACtB,CAAC;MAAC,IAAG,CAACsB,CAAC,GAACD,CAAC,CAACO,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEP,CAAC,CAACgB,QAAQ,IAAEjB,CAAC,CAACkB,aAAa,KAAG,CAAC,EAAC,OAAOlB,CAAC;MAAC,IAAG,CAACrB,CAAC,GAACqB,CAAC,CAACO,OAAO,CAACC,OAAO,KAAG,IAAI,IAAE7B,CAAC,CAACwC,KAAK,EAAC;QAAC,IAAIjC,CAAC,GAACc,CAAC,CAACO,OAAO,CAACC,OAAO,CAACY,cAAc,CAACpB,CAAC,CAACO,OAAO,CAACC,OAAO,CAACW,KAAK,CAAC;QAAC,IAAGjC,CAAC,KAAG,CAAC,CAAC,EAAC,OAAM;UAAC,GAAGc,CAAC;UAACE,iBAAiB,EAAChB,CAAC;UAACgC,aAAa,EAAC;QAAC,CAAC;MAAA;MAAC,OAAM;QAAC,GAAGlB,CAAC;QAACkB,aAAa,EAAC;MAAC,CAAC;IAAA,CAAC;IAAC,CAAC,CAAC,EAAElB,CAAC,EAACC,CAAC,EAAC;MAAC,IAAIa,CAAC,EAACH,CAAC,EAACC,CAAC,EAACtB,CAAC,EAAC+B,CAAC;MAAC,IAAG,CAACP,CAAC,GAACd,CAAC,CAACO,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEM,CAAC,CAACG,QAAQ,IAAE,CAACN,CAAC,GAACX,CAAC,CAACO,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEG,CAAC,CAACW,UAAU,CAACd,OAAO,IAAE,EAAE,CAACI,CAAC,GAACZ,CAAC,CAACO,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEI,CAAC,CAACW,eAAe,CAACf,OAAO,CAACgB,MAAM,CAAC,IAAExB,CAAC,CAACkB,aAAa,KAAG,CAAC,EAAC,OAAOlB,CAAC;MAAC,IAAGA,CAAC,CAACyB,OAAO,EAAC;QAAC,IAAIC,CAAC,GAACzB,CAAC,CAAC0B,KAAK,KAAGrE,CAAC,CAACsE,QAAQ,GAAC3B,CAAC,CAAC4B,GAAG,GAACzE,EAAE,CAAC6C,CAAC,EAAC;YAAC6B,YAAY,EAACA,CAAA,KAAI9B,CAAC,CAACyB,OAAO,CAACtB,OAAO;YAAC4B,kBAAkB,EAACA,CAAA,KAAI;cAAC,IAAIC,CAAC,EAACC,CAAC;cAAC,OAAM,CAACA,CAAC,GAAC,CAACD,CAAC,GAAChC,CAAC,CAACE,iBAAiB,KAAG,IAAI,GAAC8B,CAAC,GAAChC,CAAC,CAACyB,OAAO,CAACtB,OAAO,CAAC+B,SAAS,CAACC,CAAC,IAAE,CAACnC,CAAC,CAACyB,OAAO,CAACR,QAAQ,CAACkB,CAAC,CAAC,CAAC,KAAG,IAAI,GAACF,CAAC,GAAC,IAAI;YAAA,CAAC;YAACG,eAAe,EAACpC,CAAC,CAACyB,OAAO,CAACR,QAAQ;YAACoB,SAASA,CAAA,EAAE;cAAC,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;YAAA;UAAC,CAAC,CAAC;UAACC,CAAC,GAAC,CAACjD,CAAC,GAACW,CAAC,CAACuC,OAAO,KAAG,IAAI,GAAClD,CAAC,GAAC,CAAC;QAAC,OAAOU,CAAC,CAACE,iBAAiB,KAAGwB,CAAC,IAAE1B,CAAC,CAACyC,iBAAiB,KAAGF,CAAC,GAACvC,CAAC,GAAC;UAAC,GAAGA,CAAC;UAACE,iBAAiB,EAACwB,CAAC;UAACe,iBAAiB,EAACF;QAAC,CAAC;MAAA;MAAC,IAAI5D,CAAC,GAACoB,EAAE,CAACC,CAAC,CAAC;MAAC,IAAGrB,CAAC,CAACuB,iBAAiB,KAAG,IAAI,EAAC;QAAC,IAAIwB,CAAC,GAAC/C,CAAC,CAACwB,OAAO,CAAC+B,SAAS,CAACK,CAAC,IAAE,CAACA,CAAC,CAAChC,OAAO,CAACC,OAAO,CAACS,QAAQ,CAAC;QAACS,CAAC,KAAG,CAAC,CAAC,KAAG/C,CAAC,CAACuB,iBAAiB,GAACwB,CAAC,CAAC;MAAA;MAAC,IAAIxC,CAAC,GAACe,CAAC,CAAC0B,KAAK,KAAGrE,CAAC,CAACsE,QAAQ,GAAC3B,CAAC,CAAC4B,GAAG,GAACzE,EAAE,CAAC6C,CAAC,EAAC;UAAC6B,YAAY,EAACA,CAAA,KAAInD,CAAC,CAACwB,OAAO;UAAC4B,kBAAkB,EAACA,CAAA,KAAIpD,CAAC,CAACuB,iBAAiB;UAACmC,SAAS,EAACX,CAAC,IAAEA,CAAC,CAACgB,EAAE;UAACN,eAAe,EAACV,CAAC,IAAEA,CAAC,CAACnB,OAAO,CAACC,OAAO,CAACS;QAAQ,CAAC,CAAC;QAACZ,CAAC,GAAC,CAACgB,CAAC,GAACpB,CAAC,CAACuC,OAAO,KAAG,IAAI,GAACnB,CAAC,GAAC,CAAC;MAAC,OAAOrB,CAAC,CAACE,iBAAiB,KAAGhB,CAAC,IAAEc,CAAC,CAACyC,iBAAiB,KAAGpC,CAAC,GAACL,CAAC,GAAC;QAAC,GAAGA,CAAC;QAAC,GAAGrB,CAAC;QAACuB,iBAAiB,EAAChB,CAAC;QAACuD,iBAAiB,EAACpC;MAAC,CAAC;IAAA,CAAC;IAAC,CAAC,CAAC,GAAE,CAACL,CAAC,EAACC,CAAC,KAAG;MAAC,IAAIa,CAAC,EAACH,CAAC,EAACC,CAAC;MAAC,IAAG,CAACE,CAAC,GAACd,CAAC,CAACO,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEM,CAAC,CAACW,OAAO,EAAC,OAAM;QAAC,GAAGzB,CAAC;QAACG,OAAO,EAAC,CAAC,GAAGH,CAAC,CAACG,OAAO,EAACF,CAAC,CAAC0C,OAAO;MAAC,CAAC;MAAC,IAAIhE,CAAC,GAACsB,CAAC,CAAC0C,OAAO;QAACzD,CAAC,GAACa,EAAE,CAACC,CAAC,EAACV,CAAC,KAAGA,CAAC,CAACsD,IAAI,CAACjE,CAAC,CAAC,EAACW,CAAC,CAAC,CAAC;MAACU,CAAC,CAACE,iBAAiB,KAAG,IAAI,IAAE,CAACS,CAAC,GAACX,CAAC,CAACO,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEG,CAAC,CAACkC,UAAU,CAAC5C,CAAC,CAAC0C,OAAO,CAACpC,OAAO,CAACC,OAAO,CAACW,KAAK,CAAC,KAAGjC,CAAC,CAACgB,iBAAiB,GAAChB,CAAC,CAACiB,OAAO,CAACY,OAAO,CAACpC,CAAC,CAAC,CAAC;MAAC,IAAI0B,CAAC,GAAC;QAAC,GAAGL,CAAC;QAAC,GAAGd,CAAC;QAACuD,iBAAiB,EAAC;MAAC,CAAC;MAAC,OAAM,CAAC7B,CAAC,GAACZ,CAAC,CAACO,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEI,CAAC,CAACkC,UAAU,IAAE9C,CAAC,CAACO,OAAO,CAACC,OAAO,CAACW,KAAK,KAAG,KAAK,CAAC,KAAGd,CAAC,CAACH,iBAAiB,GAAC,CAAC,CAAC,EAACG,CAAC;IAAA,CAAC;IAAC,CAAC,CAAC,GAAE,CAACL,CAAC,EAACC,CAAC,KAAG;MAAC,IAAIf,CAAC;MAAC,IAAG,CAACA,CAAC,GAACc,CAAC,CAACO,OAAO,CAACC,OAAO,KAAG,IAAI,IAAEtB,CAAC,CAACuC,OAAO,EAAC,OAAM;QAAC,GAAGzB,CAAC;QAACG,OAAO,EAACH,CAAC,CAACG,OAAO,CAAC4C,MAAM,CAAC1C,CAAC,IAAEA,CAAC,CAACqC,EAAE,KAAGzC,CAAC,CAACyC,EAAE;MAAC,CAAC;MAAC,IAAI/D,CAAC,GAACoB,EAAE,CAACC,CAAC,EAACK,CAAC,IAAE;QAAC,IAAIS,CAAC,GAACT,CAAC,CAAC6B,SAAS,CAACvB,CAAC,IAAEA,CAAC,CAAC+B,EAAE,KAAGzC,CAAC,CAACyC,EAAE,CAAC;QAAC,OAAO5B,CAAC,KAAG,CAAC,CAAC,IAAET,CAAC,CAAC2C,MAAM,CAAClC,CAAC,EAAC,CAAC,CAAC,EAACT,CAAC;MAAA,CAAC,CAAC;MAAC,OAAM;QAAC,GAAGL,CAAC;QAAC,GAAGrB,CAAC;QAAC8D,iBAAiB,EAAC;MAAC,CAAC;IAAA,CAAC;IAAC,CAAC,CAAC,GAAE,CAACzC,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACiD,OAAO,KAAGhD,CAAC,CAACyC,EAAE,GAAC1C,CAAC,GAAC;MAAC,GAAGA,CAAC;MAACiD,OAAO,EAAChD,CAAC,CAACyC;IAAE,CAAC;IAAC,CAAC,CAAC,GAAE,CAAC1C,CAAC,EAACC,CAAC,KAAGD,CAAC,CAACyC,iBAAiB,KAAGxC,CAAC,CAACuC,OAAO,GAACxC,CAAC,GAAC;MAAC,GAAGA,CAAC;MAACyC,iBAAiB,EAACxC,CAAC,CAACuC;IAAO,CAAC;IAAC,CAAC,CAAC,GAAE,CAACxC,CAAC,EAACC,CAAC,KAAG;MAAC,IAAIf,CAAC;MAAC,IAAG,CAAC,CAACA,CAAC,GAACc,CAAC,CAACyB,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACvC,CAAC,CAACiB,OAAO,MAAIF,CAAC,CAACE,OAAO,EAAC,OAAOH,CAAC;MAAC,IAAIrB,CAAC,GAACqB,CAAC,CAACE,iBAAiB;MAAC,IAAGF,CAAC,CAACE,iBAAiB,KAAG,IAAI,EAAC;QAAC,IAAIG,CAAC,GAACJ,CAAC,CAACE,OAAO,CAACY,OAAO,CAACf,CAAC,CAACyB,OAAO,CAACtB,OAAO,CAACH,CAAC,CAACE,iBAAiB,CAAC,CAAC;QAACG,CAAC,KAAG,CAAC,CAAC,GAAC1B,CAAC,GAAC0B,CAAC,GAAC1B,CAAC,GAAC,IAAI;MAAA;MAAC,OAAM;QAAC,GAAGqB,CAAC;QAACE,iBAAiB,EAACvB,CAAC;QAAC8C,OAAO,EAACyB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAACnD,CAAC,CAACyB,OAAO,EAAC;UAACtB,OAAO,EAACF,CAAC,CAACE;QAAO,CAAC;MAAC,CAAC;IAAA;EAAC,CAAC;EAACiD,EAAE,GAAC9J,EAAE,CAAC,IAAI,CAAC;AAAC8J,EAAE,CAACC,WAAW,GAAC,wBAAwB;AAAC,SAASC,EAAEA,CAACtD,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACnG,EAAE,CAACsJ,EAAE,CAAC;EAAC,IAAGnD,CAAC,KAAG,IAAI,EAAC;IAAC,IAAItB,CAAC,GAAC,IAAI2D,KAAK,CAAC,IAAItC,CAAC,iDAAiD,CAAC;IAAC,MAAMsC,KAAK,CAACiB,iBAAiB,IAAEjB,KAAK,CAACiB,iBAAiB,CAAC5E,CAAC,EAAC2E,EAAE,CAAC,EAAC3E,CAAC;EAAA;EAAC,OAAOsB,CAAC;AAAA;AAAC,IAAIuD,EAAE,GAAClK,EAAE,CAAC,IAAI,CAAC;AAAC,SAASmK,EAAEA,CAACzD,CAAC,EAAC;EAAC,IAAIY,CAAC;EAAC,IAAIX,CAAC,GAACyD,CAAC,CAAC,iBAAiB,CAAC;IAAC,CAAC/E,CAAC,EAACO,CAAC,CAAC,GAAChF,CAAC,CAAC,MAAI;MAAC,IAAIoF,CAAC,GAACW,CAAC,CAACqB,UAAU,CAACd,OAAO;MAAC,IAAG,CAAClB,CAAC,EAAC,OAAM,CAAC,CAAC,EAAC,CAAC,CAAC;MAAC,IAAI+B,CAAC,GAACsC,MAAM,CAACC,gBAAgB,CAACtE,CAAC,CAAC;MAAC,OAAM,CAACuE,UAAU,CAACxC,CAAC,CAACyC,iBAAiB,IAAEzC,CAAC,CAAC0C,UAAU,CAAC,EAACF,UAAU,CAACxC,CAAC,CAAC2C,eAAe,IAAE3C,CAAC,CAAC4C,aAAa,CAAC,CAAC;IAAA,CAAC,EAAC,CAAChE,CAAC,CAACqB,UAAU,CAACd,OAAO,CAAC,CAAC;IAACH,CAAC,GAAClH,EAAE,CAAC;MAAC+K,kBAAkB,EAACvF,CAAC;MAACwF,gBAAgB,EAACjF,CAAC;MAACkF,KAAK,EAACnE,CAAC,CAACwB,OAAO,CAACtB,OAAO,CAACG,MAAM;MAAC+D,YAAYA,CAAA,EAAE;QAAC,OAAO,EAAE;MAAA,CAAC;MAACC,gBAAgBA,CAAA,EAAE;QAAC,IAAIhF,CAAC;QAAC,OAAM,CAACA,CAAC,GAACW,CAAC,CAACqB,UAAU,CAACd,OAAO,KAAG,IAAI,GAAClB,CAAC,GAAC,IAAI;MAAA,CAAC;MAACiF,QAAQ,EAAC;IAAE,CAAC,CAAC;IAAC,CAACzD,CAAC,EAACH,CAAC,CAAC,GAACnG,EAAE,CAAC,CAAC,CAAC;EAAC,OAAOY,CAAC,CAAC,MAAI;IAACuF,CAAC,CAACrB,CAAC,IAAEA,CAAC,GAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC,CAACsB,CAAC,GAACX,CAAC,CAACwB,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACb,CAAC,CAACT,OAAO,CAAC,CAAC,EAAC/G,CAAC,CAACoL,aAAa,CAAChB,EAAE,CAACiB,QAAQ,EAAC;IAACtD,KAAK,EAACd;EAAC,CAAC,EAACjH,CAAC,CAACoL,aAAa,CAAC,KAAK,EAAC;IAACE,KAAK,EAAC;MAACC,QAAQ,EAAC,UAAU;MAACC,KAAK,EAAC,MAAM;MAACC,MAAM,EAAC,GAAGxE,CAAC,CAACyE,YAAY,CAAC,CAAC;IAAI,CAAC;IAACC,GAAG,EAACzF,CAAC,IAAE;MAAC,IAAGA,CAAC,EAAC;QAAC,IAAG,OAAO0F,OAAO,IAAE,WAAW,IAAEA,OAAO,CAACC,GAAG,CAACC,cAAc,KAAG,KAAK,CAAC,IAAEjF,CAAC,CAACwC,iBAAiB,KAAG,CAAC,EAAC;QAAOxC,CAAC,CAACC,iBAAiB,KAAG,IAAI,IAAED,CAAC,CAACwB,OAAO,CAACtB,OAAO,CAACG,MAAM,GAACL,CAAC,CAACC,iBAAiB,IAAEG,CAAC,CAAC8E,aAAa,CAAClF,CAAC,CAACC,iBAAiB,CAAC;MAAA;IAAC;EAAC,CAAC,EAACG,CAAC,CAAC+E,eAAe,CAAC,CAAC,CAACC,GAAG,CAAC/F,CAAC,IAAE;IAAC,IAAI+B,CAAC;IAAC,OAAOjI,CAAC,CAACoL,aAAa,CAAC9K,EAAE,EAAC;MAAC4L,GAAG,EAAChG,CAAC,CAACgG;IAAG,CAAC,EAAClM,CAAC,CAACmM,YAAY,CAAC,CAAClE,CAAC,GAACrB,CAAC,CAACwF,QAAQ,KAAG,IAAI,GAAC,KAAK,CAAC,GAACnE,CAAC,CAACoE,IAAI,CAACzF,CAAC,EAAC;MAAC0F,MAAM,EAACzF,CAAC,CAACwB,OAAO,CAACtB,OAAO,CAACb,CAAC,CAACqG,KAAK,CAAC;MAACC,IAAI,EAAC3F,CAAC,CAACiB,aAAa,KAAG;IAAC,CAAC,CAAC,EAAC;MAACoE,GAAG,EAAC,GAAGxE,CAAC,IAAIxB,CAAC,CAACgG,GAAG,EAAE;MAAC,YAAY,EAAChG,CAAC,CAACqG,KAAK;MAAC,cAAc,EAAC1F,CAAC,CAACwB,OAAO,CAACtB,OAAO,CAACG,MAAM;MAAC,eAAe,EAAChB,CAAC,CAACqG,KAAK,GAAC,CAAC;MAACjB,KAAK,EAAC;QAACC,QAAQ,EAAC,UAAU;QAACkB,GAAG,EAAC,CAAC;QAACC,IAAI,EAAC,CAAC;QAACC,SAAS,EAAC,cAAczG,CAAC,CAAC0G,KAAK,KAAK;QAACC,cAAc,EAAC;MAAM;IAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIC,EAAE,GAAC5M,EAAE,CAAC,IAAI,CAAC;AAAC4M,EAAE,CAAC7C,WAAW,GAAC,qBAAqB;AAAC,SAASK,CAACA,CAAC1D,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACnG,EAAE,CAACoM,EAAE,CAAC;EAAC,IAAGjG,CAAC,KAAG,IAAI,EAAC;IAAC,IAAItB,CAAC,GAAC,IAAI2D,KAAK,CAAC,IAAItC,CAAC,iDAAiD,CAAC;IAAC,MAAMsC,KAAK,CAACiB,iBAAiB,IAAEjB,KAAK,CAACiB,iBAAiB,CAAC5E,CAAC,EAAC+E,CAAC,CAAC,EAAC/E,CAAC;EAAA;EAAC,OAAOsB,CAAC;AAAA;AAAC,SAASkG,EAAEA,CAACnG,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOnC,CAAC,CAACmC,CAAC,CAACmG,IAAI,EAACpF,EAAE,EAAChB,CAAC,EAACC,CAAC,CAAC;AAAA;AAAC,IAAIoG,EAAE,GAAC3M,EAAE;AAAC,SAAS4M,EAAEA,CAACtG,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIsG,EAAE;EAAC,IAAG;MAACpF,KAAK,EAACxC,CAAC;MAAC6H,YAAY,EAACtH,CAAC;MAACuH,QAAQ,EAACpG,CAAC;MAACqG,IAAI,EAAC5F,CAAC;MAAC6F,IAAI,EAAChG,CAAC;MAACiG,EAAE,EAAChG,CAAC,GAAC,IAAI;MAACK,QAAQ,EAAC3B,CAAC,GAAC,CAAC,CAAC;MAACwD,UAAU,EAACzB,CAAC,GAAC,CAAC,CAAC;MAACwF,QAAQ,EAACnF,CAAC,GAAC,CAAC,CAAC;MAACoF,QAAQ,EAACvE,CAAC,GAAC,CAAC,CAAC;MAACwE,SAAS,EAAC/E,CAAC,GAAC,CAAC,CAAC;MAACP,OAAO,EAACQ,CAAC,GAAC,IAAI;MAAC,GAAGE;IAAC,CAAC,GAACnC,CAAC;IAACgH,CAAC,GAAC,CAAC,CAAC;IAACC,CAAC,GAAC,IAAI;IAAC,CAACC,CAAC,GAAC3E,CAAC,GAAC,EAAE,GAAC,KAAK,CAAC,EAAC4E,CAAC,CAAC,GAACvM,EAAE,CAAC+D,CAAC,EAAC0B,CAAC,EAACnB,CAAC,CAAC;IAAC,CAACkI,CAAC,EAACC,CAAC,CAAC,GAACjN,EAAE,CAAC+L,EAAE,EAAC;MAAC5F,OAAO,EAAC/G,EAAE,CAAC,CAAC;MAAC0H,aAAa,EAACG,CAAC,GAAC,CAAC,GAAC,CAAC;MAAClB,OAAO,EAAC,EAAE;MAACsB,OAAO,EAACwF,CAAC,GAAC;QAAC9G,OAAO,EAAC8G,CAAC,CAAC9G,OAAO;QAACc,QAAQ,EAAC,CAACsF,EAAE,GAACU,CAAC,CAAChG,QAAQ,KAAG,IAAI,GAACsF,EAAE,GAAC,MAAI,CAAC;MAAC,CAAC,GAAC,IAAI;MAACrG,iBAAiB,EAAC,IAAI;MAACuC,iBAAiB,EAAC,CAAC;MAACQ,OAAO,EAAC;IAAI,CAAC,CAAC;IAACqE,CAAC,GAAChN,CAAC,CAAC,CAAC,CAAC,CAAC;IAACiN,CAAC,GAACjN,CAAC,CAAC;MAACkH,MAAM,EAAC,CAAC,CAAC;MAACgG,IAAI,EAAC,CAAC;IAAC,CAAC,CAAC;IAACC,CAAC,GAACnN,CAAC,CAAC,IAAI,CAAC;IAACoN,CAAC,GAACpN,CAAC,CAAC,IAAI,CAAC;IAACqN,EAAE,GAACrN,CAAC,CAAC,IAAI,CAAC;IAACsN,CAAC,GAACtN,CAAC,CAAC,IAAI,CAAC;IAACuN,CAAC,GAAC7M,CAAC,CAAC,OAAO4F,CAAC,IAAE,QAAQ,GAAC,CAACkH,CAAC,EAACC,CAAC,KAAG;MAAC,IAAIC,CAAC,GAACpH,CAAC;MAAC,OAAM,CAACkH,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACE,CAAC,CAAC,OAAKD,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACC,CAAC,CAAC,CAAC;IAAA,CAAC,GAACpH,CAAC,IAAE,IAAI,GAACA,CAAC,GAAC,CAACkH,CAAC,EAACC,CAAC,KAAGD,CAAC,KAAGC,CAAC,CAAC;IAACE,CAAC,GAACjN,CAAC,CAAC8M,CAAC,IAAEb,CAAC,GAACrG,CAAC,KAAG,IAAI,GAACqG,CAAC,CAAC9G,OAAO,CAACY,OAAO,CAAC+G,CAAC,CAAC,GAACb,CAAC,CAAC9G,OAAO,CAAC+B,SAAS,CAAC6F,CAAC,IAAEF,CAAC,CAACE,CAAC,EAACD,CAAC,CAAC,CAAC,GAACV,CAAC,CAACjH,OAAO,CAAC+B,SAAS,CAAC6F,CAAC,IAAEF,CAAC,CAACE,CAAC,CAACxH,OAAO,CAACC,OAAO,CAACW,KAAK,EAAC2G,CAAC,CAAC,CAAC,CAAC;IAACI,CAAC,GAACtO,EAAE,CAACkO,CAAC,IAAEhK,CAAC,CAACqK,CAAC,CAACC,IAAI,EAAC;MAAC,CAAC,CAAC,GAAE,MAAIlB,CAAC,CAACmB,IAAI,CAACN,CAAC,IAAEF,CAAC,CAACE,CAAC,EAACD,CAAC,CAAC,CAAC;MAAC,CAAC,CAAC,GAAE,MAAID,CAAC,CAACX,CAAC,EAACY,CAAC;IAAC,CAAC,CAAC,EAAC,CAACZ,CAAC,CAAC,CAAC;IAACoB,EAAE,GAACtN,CAAC,CAAC8M,CAAC,IAAEV,CAAC,CAAClH,iBAAiB,KAAG+H,CAAC,CAACH,CAAC,CAAC,CAAC;IAACK,CAAC,GAACjO,CAAC,CAAC,OAAK;MAAC,GAAGkN,CAAC;MAACL,SAAS,EAACC,CAAC;MAACzF,eAAe,EAACgG,CAAC;MAACgB,QAAQ,EAACd,CAAC;MAACe,QAAQ,EAACd,CAAC;MAACe,SAAS,EAACd,EAAE;MAACrG,UAAU,EAACsG,CAAC;MAACzG,KAAK,EAAC+F,CAAC;MAACV,YAAY,EAACtH,CAAC;MAAC+B,QAAQ,EAAC3B,CAAC;MAAC8I,IAAI,EAAC7F,CAAC,GAAC,CAAC,GAAC,CAAC;MAACd,OAAO,EAAC2F,CAAC,CAAC3F,OAAO;MAAC,IAAIvB,iBAAiBA,CAAA,EAAE;QAAC,IAAGoH,CAAC,CAAC9G,OAAO,IAAE4G,CAAC,CAAClH,iBAAiB,KAAG,IAAI,KAAG+G,CAAC,GAACA,CAAC,CAAC9G,OAAO,CAACG,MAAM,GAAC,CAAC,GAAC8G,CAAC,CAACjH,OAAO,CAACG,MAAM,GAAC,CAAC,CAAC,EAAC;UAAC,IAAG2G,CAAC,EAAC;YAAC,IAAIc,CAAC,GAACd,CAAC,CAAC9G,OAAO,CAAC+B,SAAS,CAAC8F,CAAC,IAAE;cAAC,IAAIU,CAAC,EAACC,CAAC;cAAC,OAAM,EAAE,CAACA,CAAC,GAAC,CAACD,CAAC,GAACzB,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAChG,QAAQ,KAAG,IAAI,GAAC,KAAK,CAAC,GAACyH,CAAC,CAACjD,IAAI,CAACwB,CAAC,EAACe,CAAC,CAAC,KAAG,IAAI,IAAEW,CAAC,CAAC;YAAA,CAAC,CAAC;YAAC,IAAGZ,CAAC,KAAG,CAAC,CAAC,EAAC,OAAOA,CAAC;UAAA;UAAC,IAAID,CAAC,GAACV,CAAC,CAACjH,OAAO,CAAC+B,SAAS,CAAC6F,CAAC,IAAE,CAACA,CAAC,CAACxH,OAAO,CAACC,OAAO,CAACS,QAAQ,CAAC;UAAC,IAAG6G,CAAC,KAAG,CAAC,CAAC,EAAC,OAAOA,CAAC;QAAA;QAAC,OAAOV,CAAC,CAAClH,iBAAiB;MAAA,CAAC;MAACkB,cAAc,EAAC6G,CAAC;MAACW,OAAO,EAACf,CAAC;MAAChF,UAAU,EAACqF,CAAC;MAACW,QAAQ,EAACP,EAAE;MAACzB,QAAQ,EAACnF,CAAC;MAACoB,UAAU,EAACzB;IAAC,CAAC,CAAC,EAAC,CAAC6F,CAAC,EAAChI,CAAC,EAACI,CAAC,EAACiD,CAAC,EAACb,CAAC,EAACL,CAAC,EAAC+F,CAAC,EAACH,CAAC,CAAC,CAAC;EAAC7L,CAAC,CAAC,MAAI;IAAC6L,CAAC,IAAEI,CAAC,CAAC;MAACjB,IAAI,EAAC,CAAC;MAACjG,OAAO,EAAC8G,CAAC,CAAC9G;IAAO,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC8G,CAAC,EAACA,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC9G,OAAO,CAAC,CAAC,EAAC/E,CAAC,CAAC,MAAI;IAACgM,CAAC,CAAC7G,OAAO,CAACC,OAAO,GAAC2H,CAAC;EAAA,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC,EAAC3M,EAAE,CAAC,CAAC2M,CAAC,CAACM,SAAS,EAACN,CAAC,CAACK,QAAQ,EAACL,CAAC,CAAC7G,UAAU,CAAC,EAAC,MAAIwH,EAAE,CAACC,aAAa,CAAC,CAAC,EAACZ,CAAC,CAACjH,aAAa,KAAG,CAAC,CAAC;EAAC,IAAI8H,CAAC,GAAC9O,CAAC,CAAC,MAAI;MAAC,IAAI4N,CAAC,EAACC,CAAC,EAACC,CAAC;MAAC,OAAM;QAACpC,IAAI,EAACuC,CAAC,CAACjH,aAAa,KAAG,CAAC;QAACD,QAAQ,EAAC3B,CAAC;QAAC2J,WAAW,EAACd,CAAC,CAACjI,iBAAiB;QAACgJ,YAAY,EAACf,CAAC,CAACjI,iBAAiB,KAAG,IAAI,GAAC,IAAI,GAACiI,CAAC,CAAC1G,OAAO,GAAC0G,CAAC,CAAC1G,OAAO,CAACtB,OAAO,CAAC,CAAC2H,CAAC,GAACK,CAAC,CAACjI,iBAAiB,KAAG,IAAI,GAAC4H,CAAC,GAAC,CAAC,CAAC,GAAC,CAACE,CAAC,GAAC,CAACD,CAAC,GAACI,CAAC,CAAChI,OAAO,CAACgI,CAAC,CAACjI,iBAAiB,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC6H,CAAC,CAACxH,OAAO,CAACC,OAAO,CAACW,KAAK,KAAG,IAAI,GAAC6G,CAAC,GAAC,IAAI;QAAC7G,KAAK,EAAC+F;MAAC,CAAC;IAAA,CAAC,EAAC,CAACiB,CAAC,EAAC7I,CAAC,EAAC4H,CAAC,CAAC,CAAC;IAACiC,CAAC,GAACnO,CAAC,CAAC,MAAI;MAAC,IAAGmN,CAAC,CAACjI,iBAAiB,KAAG,IAAI,EAAC;QAAC,IAAGiI,CAAC,CAAC1G,OAAO,EAAC2H,EAAE,CAACjB,CAAC,CAAC1G,OAAO,CAACtB,OAAO,CAACgI,CAAC,CAACjI,iBAAiB,CAAC,CAAC,CAAC,KAAI;UAAC,IAAG;YAACK,OAAO,EAACuH;UAAC,CAAC,GAACK,CAAC,CAAChI,OAAO,CAACgI,CAAC,CAACjI,iBAAiB,CAAC;UAACkJ,EAAE,CAACtB,CAAC,CAACtH,OAAO,CAACW,KAAK,CAAC;QAAA;QAAC2H,EAAE,CAACO,UAAU,CAAC/L,CAAC,CAACsE,QAAQ,EAACuG,CAAC,CAACjI,iBAAiB,CAAC;MAAA;IAAC,CAAC,CAAC;IAACoJ,CAAC,GAACtO,CAAC,CAAC,MAAI;MAACqM,CAAC,CAAC;QAACjB,IAAI,EAAC;MAAC,CAAC,CAAC,EAACkB,CAAC,CAAC9G,OAAO,GAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAAC+I,CAAC,GAACvO,CAAC,CAAC,MAAI;MAACqM,CAAC,CAAC;QAACjB,IAAI,EAAC;MAAC,CAAC,CAAC,EAACkB,CAAC,CAAC9G,OAAO,GAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACgJ,CAAC,GAACxO,CAAC,CAAC,CAAC8M,CAAC,EAACC,CAAC,EAACC,CAAC,MAAIV,CAAC,CAAC9G,OAAO,GAAC,CAAC,CAAC,EAACsH,CAAC,KAAGxK,CAAC,CAACsE,QAAQ,GAACyF,CAAC,CAAC;MAACjB,IAAI,EAAC,CAAC;MAACzE,KAAK,EAACrE,CAAC,CAACsE,QAAQ;MAACC,GAAG,EAACkG,CAAC;MAACvF,OAAO,EAACwF;IAAC,CAAC,CAAC,GAACX,CAAC,CAAC;MAACjB,IAAI,EAAC,CAAC;MAACzE,KAAK,EAACmG,CAAC;MAACtF,OAAO,EAACwF;IAAC,CAAC,CAAC,CAAC,CAAC;IAACyB,CAAC,GAACzO,CAAC,CAAC,CAAC8M,CAAC,EAACC,CAAC,MAAIV,CAAC,CAAC;MAACjB,IAAI,EAAC,CAAC;MAACzD,OAAO,EAAC;QAACD,EAAE,EAACoF,CAAC;QAACvH,OAAO,EAACwH;MAAC;IAAC,CAAC,CAAC,EAAC,MAAI;MAACI,CAAC,CAACU,QAAQ,CAACd,CAAC,CAACvH,OAAO,CAACW,KAAK,CAAC,KAAGmG,CAAC,CAAC9G,OAAO,GAAC,CAAC,CAAC,CAAC,EAAC6G,CAAC,CAAC;QAACjB,IAAI,EAAC,CAAC;QAAC1D,EAAE,EAACoF;MAAC,CAAC,CAAC;IAAA,CAAC,CAAC,CAAC;IAAC4B,EAAE,GAAC1O,CAAC,CAAC8M,CAAC,KAAGT,CAAC,CAAC;MAACjB,IAAI,EAAC,CAAC;MAAC1D,EAAE,EAACoF;IAAC,CAAC,CAAC,EAAC,MAAIT,CAAC,CAAC;MAACjB,IAAI,EAAC,CAAC;MAAC1D,EAAE,EAAC;IAAI,CAAC,CAAC,CAAC,CAAC;IAAC0G,EAAE,GAACpO,CAAC,CAAC8M,CAAC,IAAEhK,CAAC,CAACqK,CAAC,CAACC,IAAI,EAAC;MAAC,CAAC,CAAC,IAAG;QAAC,OAAOjB,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACW,CAAC,CAAC;MAAA,CAAC;MAAC,CAAC,CAAC,IAAG;QAAC,IAAIC,CAAC,GAACI,CAAC,CAAChH,KAAK,CAACf,KAAK,CAAC,CAAC;UAAC4H,CAAC,GAACD,CAAC,CAAC7F,SAAS,CAACwG,CAAC,IAAEb,CAAC,CAACa,CAAC,EAACZ,CAAC,CAAC,CAAC;QAAC,OAAOE,CAAC,KAAG,CAAC,CAAC,GAACD,CAAC,CAACnF,IAAI,CAACkF,CAAC,CAAC,GAACC,CAAC,CAAC/E,MAAM,CAACgF,CAAC,EAAC,CAAC,CAAC,EAACb,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACY,CAAC,CAAC;MAAA;IAAC,CAAC,CAAC,CAAC;IAAC4B,EAAE,GAAC3O,CAAC,CAAC8M,CAAC,IAAE;MAACT,CAAC,CAAC;QAACjB,IAAI,EAAC,CAAC;QAAC5D,OAAO,EAACsF;MAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACgB,EAAE,GAAC5O,CAAC,CAAC,OAAK;MAACuM,QAAQ,EAAC2C,EAAE;MAACQ,cAAc,EAACH,CAAC;MAACI,aAAa,EAACH,EAAE;MAACL,UAAU,EAACG,CAAC;MAACT,aAAa,EAACQ,CAAC;MAACO,YAAY,EAACR,CAAC;MAACS,oBAAoB,EAACJ,EAAE;MAACK,kBAAkB,EAACb;IAAC,CAAC,CAAC,EAAC,EAAE,CAAC;IAACc,EAAE,GAAChK,CAAC,KAAG,IAAI,GAAC,CAAC,CAAC,GAAC;MAAC8E,GAAG,EAAC9E;IAAC,CAAC;IAACiK,EAAE,GAAC5P,CAAC,CAAC,IAAI,CAAC;IAAC6P,EAAE,GAACrP,EAAE,CAAC,CAAC;EAAC,OAAOd,EAAE,CAAC,MAAI;IAACkQ,EAAE,CAAC1J,OAAO,IAAEtB,CAAC,KAAG,KAAK,CAAC,IAAEiL,EAAE,CAACC,gBAAgB,CAACF,EAAE,CAAC1J,OAAO,EAAC,OAAO,EAAC,MAAI;MAAC2G,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACjI,CAAC,CAAC;IAAA,CAAC,CAAC;EAAA,CAAC,EAAC,CAACgL,EAAE,EAAC/C,CAAC,CAAC,CAAC,EAAC/N,CAAC,CAACoL,aAAa,CAACpB,EAAE,CAACqB,QAAQ,EAAC;IAACtD,KAAK,EAAC2H;EAAE,CAAC,EAAC1P,CAAC,CAACoL,aAAa,CAAC0B,EAAE,CAACzB,QAAQ,EAAC;IAACtD,KAAK,EAACgH;EAAC,CAAC,EAAC/O,CAAC,CAACoL,aAAa,CAAC9H,EAAE,EAAC;IAACyE,KAAK,EAACrD,CAAC,CAACqK,CAAC,CAACjH,aAAa,EAAC;MAAC,CAAC,CAAC,GAAEtE,EAAE,CAACgC,IAAI;MAAC,CAAC,CAAC,GAAEhC,EAAE,CAACiC;IAAM,CAAC;EAAC,CAAC,EAAC8B,CAAC,IAAE,IAAI,IAAEuG,CAAC,IAAE,IAAI,IAAEtJ,EAAE,CAAC;IAAC,CAAC+C,CAAC,GAAEuG;EAAC,CAAC,CAAC,CAAC7B,GAAG,CAAC,CAAC,CAACyC,CAAC,EAACC,CAAC,CAAC,EAACC,CAAC,KAAG5O,CAAC,CAACoL,aAAa,CAAChI,EAAE,EAAC;IAAC6N,QAAQ,EAAC/N,EAAE,CAACC,MAAM;IAACwI,GAAG,EAACiD,CAAC,KAAG,CAAC,GAACU,CAAC,IAAE;MAAC,IAAIC,CAAC;MAACuB,EAAE,CAAC1J,OAAO,GAAC,CAACmI,CAAC,GAACD,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC4B,OAAO,CAAC,MAAM,CAAC,KAAG,IAAI,GAAC3B,CAAC,GAAC,IAAI;IAAA,CAAC,GAAC,KAAK,CAAC;IAAC,GAAGzK,EAAE,CAAC;MAACoH,GAAG,EAACwC,CAAC;MAACyC,EAAE,EAAC,OAAO;MAACnE,IAAI,EAAC,QAAQ;MAACoE,MAAM,EAAC,CAAC,CAAC;MAACC,QAAQ,EAAC,CAAC,CAAC;MAAC/D,IAAI,EAAC5F,CAAC;MAACG,QAAQ,EAAC3B,CAAC;MAACqH,IAAI,EAACmB,CAAC;MAAC3G,KAAK,EAAC4G;IAAC,CAAC;EAAC,CAAC,CAAC,CAAC,EAACxJ,CAAC,CAAC;IAACmM,QAAQ,EAACT,EAAE;IAACU,UAAU,EAACxI,CAAC;IAACyI,IAAI,EAAC5B,CAAC;IAAC6B,UAAU,EAACxE,EAAE;IAACM,IAAI,EAAC;EAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAImE,EAAE,GAAC,OAAO;AAAC,SAASC,EAAEA,CAAC/K,CAAC,EAACC,CAAC,EAAC;EAAC,IAAI2H,CAAC,EAACC,CAAC,EAACI,CAAC,EAACC,CAAC,EAACI,EAAE;EAAC,IAAI3J,CAAC,GAACzD,CAAC,CAAC,CAAC;IAAC;MAACwH,EAAE,EAACxD,CAAC,GAAC,6BAA6BP,CAAC,EAAE;MAAC8H,QAAQ,EAACpG,CAAC;MAAC2K,YAAY,EAAClK,CAAC;MAACsF,IAAI,EAACzF,CAAC,GAAC,MAAM;MAAC,GAAGC;IAAC,CAAC,GAACZ,CAAC;IAACV,CAAC,GAACoE,CAAC,CAAC,gBAAgB,CAAC;IAACrC,CAAC,GAACiC,EAAE,CAAC,gBAAgB,CAAC;IAAC5B,CAAC,GAAC5F,CAAC,CAACwD,CAAC,CAACkJ,QAAQ,EAACvI,CAAC,CAAC;IAACsC,CAAC,GAAC7G,EAAE,CAAC4D,CAAC,CAACkJ,QAAQ,CAAC;IAACxG,CAAC,GAAC1H,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC2H,CAAC,GAACnH,EAAE,CAAC,CAAC;IAACqH,CAAC,GAACnH,CAAC,CAAC,MAAI;MAACqG,CAAC,CAACoF,QAAQ,CAAC,IAAI,CAAC,EAACnH,CAAC,CAACgC,UAAU,CAACd,OAAO,KAAGlB,CAAC,CAACgC,UAAU,CAACd,OAAO,CAACyK,SAAS,GAAC,CAAC,CAAC,EAAC5J,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAAC4N,OAAO,CAAC;IAAA,CAAC,CAAC;IAAClE,CAAC,GAAC,YAAU;MAAC,IAAImB,CAAC;MAAC,OAAO,OAAOrH,CAAC,IAAE,UAAU,IAAExB,CAAC,CAAC6B,KAAK,KAAG,KAAK,CAAC,GAAC,CAACgH,CAAC,GAACrH,CAAC,CAACxB,CAAC,CAAC6B,KAAK,CAAC,KAAG,IAAI,GAACgH,CAAC,GAAC,EAAE,GAAC,OAAO7I,CAAC,CAAC6B,KAAK,IAAE,QAAQ,GAAC7B,CAAC,CAAC6B,KAAK,GAAC,EAAE;IAAA,CAAC,CAAC,CAAC;EAAC/E,EAAE,CAAC,CAAC,CAAC+L,CAAC,EAACa,CAAC,CAAC,EAAC,CAACG,CAAC,EAACG,CAAC,CAAC,KAAG;IAAC,IAAGtH,CAAC,CAACxB,OAAO,EAAC;IAAO,IAAI+I,CAAC,GAACjK,CAAC,CAACkJ,QAAQ,CAAChI,OAAO;IAAC+I,CAAC,KAAG,CAACD,CAAC,KAAG,CAAC,IAAEN,CAAC,KAAG,CAAC,IAAEb,CAAC,KAAGgB,CAAC,MAAII,CAAC,CAACpI,KAAK,GAACgH,CAAC,CAAC,EAACgD,qBAAqB,CAAC,MAAI;MAAC,IAAGnJ,CAAC,CAACxB,OAAO,IAAE,CAAC+I,CAAC,IAAE,CAAChH,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC6I,aAAa,MAAI7B,CAAC,EAAC;MAAO,IAAG;QAAC8B,cAAc,EAAC7B,CAAC;QAAC8B,YAAY,EAAC7B;MAAC,CAAC,GAACF,CAAC;MAACgC,IAAI,CAACC,GAAG,CAAC,CAAC/B,CAAC,IAAE,IAAI,GAACA,CAAC,GAAC,CAAC,KAAGD,CAAC,IAAE,IAAI,GAACA,CAAC,GAAC,CAAC,CAAC,CAAC,KAAG,CAAC,IAAEA,CAAC,KAAG,CAAC,IAAED,CAAC,CAACkC,iBAAiB,CAAClC,CAAC,CAACpI,KAAK,CAACb,MAAM,EAACiJ,CAAC,CAACpI,KAAK,CAACb,MAAM,CAAC;IAAA,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC0G,CAAC,EAAC1H,CAAC,CAAC4B,aAAa,EAACqB,CAAC,CAAC,CAAC,EAACnG,EAAE,CAAC,CAAC,CAAC+L,CAAC,CAAC,EAAC,CAACa,CAAC,CAAC,KAAG;IAAC,IAAGb,CAAC,KAAG,CAAC,IAAEa,CAAC,KAAG,CAAC,EAAC;MAAC,IAAGhH,CAAC,CAACxB,OAAO,EAAC;MAAO,IAAI2I,CAAC,GAAC7J,CAAC,CAACkJ,QAAQ,CAAChI,OAAO;MAAC,IAAG,CAAC2I,CAAC,EAAC;MAAO,IAAIG,CAAC,GAACH,CAAC,CAAChI,KAAK;QAAC;UAACkK,cAAc,EAAC9B,CAAC;UAAC+B,YAAY,EAAC9B,CAAC;UAACkC,kBAAkB,EAACjC;QAAC,CAAC,GAACN,CAAC;MAACA,CAAC,CAAChI,KAAK,GAAC,EAAE,EAACgI,CAAC,CAAChI,KAAK,GAACmI,CAAC,EAACG,CAAC,KAAG,IAAI,GAACN,CAAC,CAACsC,iBAAiB,CAAClC,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,GAACN,CAAC,CAACsC,iBAAiB,CAAClC,CAAC,EAACC,CAAC,CAAC;IAAA;EAAC,CAAC,EAAC,CAAClK,CAAC,CAAC4B,aAAa,CAAC,CAAC;EAAC,IAAI+F,CAAC,GAAC3M,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC4M,CAAC,GAAClM,CAAC,CAAC,MAAI;MAACiM,CAAC,CAACzG,OAAO,GAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAAC2G,CAAC,GAACnM,CAAC,CAAC,MAAI;MAACiH,CAAC,CAAC0J,SAAS,CAAC,MAAI;QAAC1E,CAAC,CAACzG,OAAO,GAAC,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA,CAAC,CAAC;IAAC4G,CAAC,GAACpM,CAAC,CAACmN,CAAC,IAAE;MAAC,QAAOnG,CAAC,CAACxB,OAAO,GAAC,CAAC,CAAC,EAAC2H,CAAC,CAAC7C,GAAG;QAAE,KAAK7G,CAAC,CAACmN,KAAK;UAAC,IAAG5J,CAAC,CAACxB,OAAO,GAAC,CAAC,CAAC,EAAClB,CAAC,CAAC4B,aAAa,KAAG,CAAC,IAAE+F,CAAC,CAACzG,OAAO,EAAC;UAAO,IAAG2H,CAAC,CAAC0D,cAAc,CAAC,CAAC,EAAC1D,CAAC,CAAC2D,eAAe,CAAC,CAAC,EAACxM,CAAC,CAACY,iBAAiB,KAAG,IAAI,EAAC;YAACmB,CAAC,CAAC0H,aAAa,CAAC,CAAC;YAAC;UAAM;UAAC1H,CAAC,CAAC2I,kBAAkB,CAAC,CAAC,EAAC1K,CAAC,CAAC8I,IAAI,KAAG,CAAC,IAAE/G,CAAC,CAAC0H,aAAa,CAAC,CAAC;UAAC;QAAM,KAAKtK,CAAC,CAACsN,SAAS;UAAC,OAAO/J,CAAC,CAACxB,OAAO,GAAC,CAAC,CAAC,EAAC2H,CAAC,CAAC0D,cAAc,CAAC,CAAC,EAAC1D,CAAC,CAAC2D,eAAe,CAAC,CAAC,EAAChO,CAAC,CAACwB,CAAC,CAAC4B,aAAa,EAAC;YAAC,CAAC,CAAC,GAAE,MAAIG,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAAC0O,IAAI,CAAC;YAAC,CAAC,CAAC,GAAE,MAAI3K,CAAC,CAACyI,YAAY,CAAC;UAAC,CAAC,CAAC;QAAC,KAAKrL,CAAC,CAACwN,OAAO;UAAC,OAAOjK,CAAC,CAACxB,OAAO,GAAC,CAAC,CAAC,EAAC2H,CAAC,CAAC0D,cAAc,CAAC,CAAC,EAAC1D,CAAC,CAAC2D,eAAe,CAAC,CAAC,EAAChO,CAAC,CAACwB,CAAC,CAAC4B,aAAa,EAAC;YAAC,CAAC,CAAC,GAAE,MAAIG,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAAC4O,QAAQ,CAAC;YAAC,CAAC,CAAC,GAAE,MAAI;cAAC7K,CAAC,CAACyI,YAAY,CAAC,CAAC,EAAC7H,CAAC,CAAC0J,SAAS,CAAC,MAAI;gBAACrM,CAAC,CAAC6B,KAAK,IAAEE,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAAC6O,IAAI,CAAC;cAAA,CAAC,CAAC;YAAA;UAAC,CAAC,CAAC;QAAC,KAAK1N,CAAC,CAAC2N,IAAI;UAAC,IAAGjE,CAAC,CAACkE,QAAQ,EAAC;UAAM,OAAOrK,CAAC,CAACxB,OAAO,GAAC,CAAC,CAAC,EAAC2H,CAAC,CAAC0D,cAAc,CAAC,CAAC,EAAC1D,CAAC,CAAC2D,eAAe,CAAC,CAAC,EAACzK,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAACgP,KAAK,CAAC;QAAC,KAAK7N,CAAC,CAAC8N,MAAM;UAAC,OAAOvK,CAAC,CAACxB,OAAO,GAAC,CAAC,CAAC,EAAC2H,CAAC,CAAC0D,cAAc,CAAC,CAAC,EAAC1D,CAAC,CAAC2D,eAAe,CAAC,CAAC,EAACzK,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAACgP,KAAK,CAAC;QAAC,KAAK7N,CAAC,CAAC+N,GAAG;UAAC,IAAGrE,CAAC,CAACkE,QAAQ,EAAC;UAAM,OAAOrK,CAAC,CAACxB,OAAO,GAAC,CAAC,CAAC,EAAC2H,CAAC,CAAC0D,cAAc,CAAC,CAAC,EAAC1D,CAAC,CAAC2D,eAAe,CAAC,CAAC,EAACzK,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAAC6O,IAAI,CAAC;QAAC,KAAK1N,CAAC,CAACgO,QAAQ;UAAC,OAAOzK,CAAC,CAACxB,OAAO,GAAC,CAAC,CAAC,EAAC2H,CAAC,CAAC0D,cAAc,CAAC,CAAC,EAAC1D,CAAC,CAAC2D,eAAe,CAAC,CAAC,EAACzK,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAAC6O,IAAI,CAAC;QAAC,KAAK1N,CAAC,CAACiO,MAAM;UAAC,OAAO1K,CAAC,CAACxB,OAAO,GAAC,CAAC,CAAC,EAAClB,CAAC,CAAC4B,aAAa,KAAG,CAAC,GAAC,KAAK,CAAC,IAAEiH,CAAC,CAAC0D,cAAc,CAAC,CAAC,EAACvM,CAAC,CAACgC,UAAU,CAACd,OAAO,IAAE,CAAClB,CAAC,CAACiC,eAAe,CAACf,OAAO,CAACgB,MAAM,IAAE2G,CAAC,CAAC2D,eAAe,CAAC,CAAC,EAACxM,CAAC,CAACuH,QAAQ,IAAEvH,CAAC,CAAC8I,IAAI,KAAG,CAAC,IAAE9I,CAAC,CAAC6B,KAAK,KAAG,IAAI,IAAEgB,CAAC,CAAC,CAAC,EAACd,CAAC,CAAC0H,aAAa,CAAC,CAAC,CAAC;QAAC,KAAKtK,CAAC,CAACkO,GAAG;UAAC,IAAG3K,CAAC,CAACxB,OAAO,GAAC,CAAC,CAAC,EAAClB,CAAC,CAAC4B,aAAa,KAAG,CAAC,EAAC;UAAO5B,CAAC,CAAC8I,IAAI,KAAG,CAAC,IAAE9I,CAAC,CAACmD,iBAAiB,KAAG,CAAC,IAAEpB,CAAC,CAAC2I,kBAAkB,CAAC,CAAC,EAAC3I,CAAC,CAAC0H,aAAa,CAAC,CAAC;UAAC;MAAK;IAAC,CAAC,CAAC;IAAC1B,CAAC,GAACrM,CAAC,CAACmN,CAAC,IAAE;MAAC9H,CAAC,IAAE,IAAI,IAAEA,CAAC,CAAC8H,CAAC,CAAC,EAAC7I,CAAC,CAACuH,QAAQ,IAAEvH,CAAC,CAAC8I,IAAI,KAAG,CAAC,IAAED,CAAC,CAACyE,MAAM,CAACzL,KAAK,KAAG,EAAE,IAAEgB,CAAC,CAAC,CAAC,EAACd,CAAC,CAACyI,YAAY,CAAC,CAAC;IAAA,CAAC,CAAC;IAACxC,CAAC,GAACtM,CAAC,CAACmN,CAAC,IAAE;MAAC,IAAIgB,CAAC,EAACG,CAAC,EAACC,CAAC;MAAC,IAAIP,CAAC,GAAC,CAACG,CAAC,GAAChB,CAAC,CAAC0E,aAAa,KAAG,IAAI,GAAC1D,CAAC,GAACnM,EAAE,CAAC8P,IAAI,CAACtD,CAAC,IAAEA,CAAC,KAAGrB,CAAC,CAAC4E,aAAa,CAAC;MAAC,IAAG/K,CAAC,CAACxB,OAAO,GAAC,CAAC,CAAC,EAAC,EAAE,CAAC8I,CAAC,GAAChK,CAAC,CAACgC,UAAU,CAACd,OAAO,KAAG,IAAI,IAAE8I,CAAC,CAAC0D,QAAQ,CAAChE,CAAC,CAAC,CAAC,IAAE,EAAE,CAACO,CAAC,GAACjK,CAAC,CAACmJ,SAAS,CAACjI,OAAO,KAAG,IAAI,IAAE+I,CAAC,CAACyD,QAAQ,CAAChE,CAAC,CAAC,CAAC,IAAE1J,CAAC,CAAC4B,aAAa,KAAG,CAAC,EAAC,OAAOiH,CAAC,CAAC0D,cAAc,CAAC,CAAC,EAACvM,CAAC,CAAC8I,IAAI,KAAG,CAAC,KAAG9I,CAAC,CAACuH,QAAQ,IAAEvH,CAAC,CAAC6B,KAAK,KAAG,IAAI,GAACgB,CAAC,CAAC,CAAC,GAAC7C,CAAC,CAACmD,iBAAiB,KAAG,CAAC,IAAEpB,CAAC,CAAC2I,kBAAkB,CAAC,CAAC,CAAC,EAAC3I,CAAC,CAAC0H,aAAa,CAAC,CAAC;IAAA,CAAC,CAAC;IAACxB,CAAC,GAACvM,CAAC,CAACmN,CAAC,IAAE;MAAC,IAAIgB,CAAC,EAACG,CAAC,EAACC,CAAC;MAAC,IAAIP,CAAC,GAAC,CAACG,CAAC,GAAChB,CAAC,CAAC0E,aAAa,KAAG,IAAI,GAAC1D,CAAC,GAACnM,EAAE,CAAC8P,IAAI,CAACtD,CAAC,IAAEA,CAAC,KAAGrB,CAAC,CAAC4E,aAAa,CAAC;MAAC,CAACzD,CAAC,GAAChK,CAAC,CAACmJ,SAAS,CAACjI,OAAO,KAAG,IAAI,IAAE8I,CAAC,CAAC0D,QAAQ,CAAChE,CAAC,CAAC,IAAE,CAACO,CAAC,GAACjK,CAAC,CAACgC,UAAU,CAACd,OAAO,KAAG,IAAI,IAAE+I,CAAC,CAACyD,QAAQ,CAAChE,CAAC,CAAC,IAAE1J,CAAC,CAAC2B,QAAQ,IAAE3B,CAAC,CAACyH,SAAS,IAAEzH,CAAC,CAAC4B,aAAa,KAAG,CAAC,KAAGG,CAAC,CAACyI,YAAY,CAAC,CAAC,EAAC7H,CAAC,CAAC0J,SAAS,CAAC,MAAI;QAACtK,CAAC,CAAC0I,oBAAoB,CAAC,CAAC,CAAC;MAAA,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACtC,CAAC,GAAC/M,EAAE,CAAC,MAAI;MAAC,IAAG4E,CAAC,CAAC2D,OAAO,EAAC,OAAM,CAAC3D,CAAC,CAAC2D,OAAO,CAAC,CAACgK,IAAI,CAAC,GAAG,CAAC;IAAA,CAAC,EAAC,CAAC3N,CAAC,CAAC2D,OAAO,CAAC,CAAC;IAACyE,CAAC,GAACxN,CAAC,CAAC,OAAK;MAAC0L,IAAI,EAACtG,CAAC,CAAC4B,aAAa,KAAG,CAAC;MAACD,QAAQ,EAAC3B,CAAC,CAAC2B;IAAQ,CAAC,CAAC,EAAC,CAAC3B,CAAC,CAAC,CAAC;IAACqI,EAAE,GAAC;MAAC5C,GAAG,EAACrD,CAAC;MAACgB,EAAE,EAACxD,CAAC;MAACgO,IAAI,EAAC,UAAU;MAAC9G,IAAI,EAACzF,CAAC;MAAC,eAAe,EAAC,CAACiH,CAAC,GAACtI,CAAC,CAACgC,UAAU,CAACd,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACoH,CAAC,CAAClF,EAAE;MAAC,eAAe,EAACpD,CAAC,CAAC4B,aAAa,KAAG,CAAC;MAAC,uBAAuB,EAAC5B,CAAC,CAACY,iBAAiB,KAAG,IAAI,GAAC,KAAK,CAAC,GAACZ,CAAC,CAACmC,OAAO,GAAC,CAACoG,CAAC,GAACvI,CAAC,CAACa,OAAO,CAAC2M,IAAI,CAAC3E,CAAC,IAAE;QAAC,IAAIa,CAAC;QAAC,OAAM,EAAE,CAACA,CAAC,GAAC1J,CAAC,CAACmC,OAAO,KAAG,IAAI,IAAEuH,CAAC,CAAC/H,QAAQ,CAACkH,CAAC,CAAC5H,OAAO,CAACC,OAAO,CAACW,KAAK,CAAC,CAAC,IAAE7B,CAAC,CAACsJ,OAAO,CAACT,CAAC,CAAC5H,OAAO,CAACC,OAAO,CAACW,KAAK,EAAC7B,CAAC,CAACmC,OAAO,CAACtB,OAAO,CAACb,CAAC,CAACY,iBAAiB,CAAC,CAAC;MAAA,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC2H,CAAC,CAACnF,EAAE,GAAC,CAACuF,CAAC,GAAC3I,CAAC,CAACa,OAAO,CAACb,CAAC,CAACY,iBAAiB,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC+H,CAAC,CAACvF,EAAE;MAAC,iBAAiB,EAAC+E,CAAC;MAAC,mBAAmB,EAAC,MAAM;MAACjB,YAAY,EAAC,CAAC8B,EAAE,GAAC,CAACJ,CAAC,GAAClI,CAAC,CAACwG,YAAY,KAAG,IAAI,GAAC0B,CAAC,GAAC5I,CAAC,CAACkH,YAAY,KAAG,KAAK,CAAC,GAAC1F,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACxB,CAAC,CAACkH,YAAY,CAAC,GAAC,IAAI,KAAG,IAAI,GAAC8B,EAAE,GAAChJ,CAAC,CAACkH,YAAY;MAACvF,QAAQ,EAAC3B,CAAC,CAAC2B,QAAQ;MAACkM,kBAAkB,EAACjG,CAAC;MAACkG,gBAAgB,EAACjG,CAAC;MAACkG,SAAS,EAACjG,CAAC;MAACX,QAAQ,EAACY,CAAC;MAACiG,OAAO,EAAC/F,CAAC;MAACgG,MAAM,EAACjG;IAAC,CAAC;EAAC,OAAO/I,CAAC,CAAC;IAACmM,QAAQ,EAAC/C,EAAE;IAACgD,UAAU,EAAC/J,CAAC;IAACgK,IAAI,EAAClD,CAAC;IAACmD,UAAU,EAACC,EAAE;IAACnE,IAAI,EAAC;EAAgB,CAAC,CAAC;AAAA;AAAC,IAAI6G,EAAE,GAAC,QAAQ;AAAC,SAASC,EAAEA,CAACzN,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIkC,CAAC;EAAC,IAAIxD,CAAC,GAAC+E,CAAC,CAAC,iBAAiB,CAAC;IAACxE,CAAC,GAACoE,EAAE,CAAC,iBAAiB,CAAC;IAACjD,CAAC,GAACvE,CAAC,CAAC6C,CAAC,CAAC8J,SAAS,EAACxI,CAAC,CAAC;IAACa,CAAC,GAAC5F,CAAC,CAAC,CAAC;IAAC;MAACwH,EAAE,EAAC/B,CAAC,GAAC,8BAA8BG,CAAC,EAAE;MAAC,GAAGF;IAAC,CAAC,GAACZ,CAAC;IAACV,CAAC,GAACxE,EAAE,CAAC,CAAC;IAACuG,CAAC,GAACrG,CAAC,CAACgM,CAAC,IAAE;MAAC,QAAOA,CAAC,CAAC1B,GAAG;QAAE,KAAK7G,CAAC,CAACsN,SAAS;UAAC,OAAO/E,CAAC,CAAC6E,cAAc,CAAC,CAAC,EAAC7E,CAAC,CAAC8E,eAAe,CAAC,CAAC,EAACnN,CAAC,CAACuC,aAAa,KAAG,CAAC,IAAEhC,CAAC,CAAC4K,YAAY,CAAC,CAAC,EAACxK,CAAC,CAACqM,SAAS,CAAC,MAAI;YAAC,IAAI1E,CAAC;YAAC,OAAM,CAACA,CAAC,GAACtI,CAAC,CAAC6J,QAAQ,CAAChI,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACyG,CAAC,CAACtF,KAAK,CAAC;cAAC+L,aAAa,EAAC,CAAC;YAAC,CAAC,CAAC;UAAA,CAAC,CAAC;QAAC,KAAKjP,CAAC,CAACwN,OAAO;UAAC,OAAOjF,CAAC,CAAC6E,cAAc,CAAC,CAAC,EAAC7E,CAAC,CAAC8E,eAAe,CAAC,CAAC,EAACnN,CAAC,CAACuC,aAAa,KAAG,CAAC,KAAGhC,CAAC,CAAC4K,YAAY,CAAC,CAAC,EAACxK,CAAC,CAACqM,SAAS,CAAC,MAAI;YAAChN,CAAC,CAACwC,KAAK,IAAEjC,CAAC,CAACmK,UAAU,CAAC/L,CAAC,CAAC6O,IAAI,CAAC;UAAA,CAAC,CAAC,CAAC,EAAC7M,CAAC,CAACqM,SAAS,CAAC,MAAI;YAAC,IAAI1E,CAAC;YAAC,OAAM,CAACA,CAAC,GAACtI,CAAC,CAAC6J,QAAQ,CAAChI,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACyG,CAAC,CAACtF,KAAK,CAAC;cAAC+L,aAAa,EAAC,CAAC;YAAC,CAAC,CAAC;UAAA,CAAC,CAAC;QAAC,KAAKjP,CAAC,CAACiO,MAAM;UAAC,OAAO/N,CAAC,CAACuC,aAAa,KAAG,CAAC,GAAC,KAAK,CAAC,IAAE8F,CAAC,CAAC6E,cAAc,CAAC,CAAC,EAAClN,CAAC,CAAC2C,UAAU,CAACd,OAAO,IAAE,CAAC7B,CAAC,CAAC4C,eAAe,CAACf,OAAO,CAACgB,MAAM,IAAEwF,CAAC,CAAC8E,eAAe,CAAC,CAAC,EAAC5M,CAAC,CAAC6J,aAAa,CAAC,CAAC,EAACzJ,CAAC,CAACqM,SAAS,CAAC,MAAI;YAAC,IAAI1E,CAAC;YAAC,OAAM,CAACA,CAAC,GAACtI,CAAC,CAAC6J,QAAQ,CAAChI,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACyG,CAAC,CAACtF,KAAK,CAAC;cAAC+L,aAAa,EAAC,CAAC;YAAC,CAAC,CAAC;UAAA,CAAC,CAAC,CAAC;QAAC;UAAQ;MAAM;IAAC,CAAC,CAAC;IAAChM,CAAC,GAAC1G,CAAC,CAACgM,CAAC,IAAE;MAAC,IAAG9J,EAAE,CAAC8J,CAAC,CAAC+F,aAAa,CAAC,EAAC,OAAO/F,CAAC,CAAC6E,cAAc,CAAC,CAAC;MAAClN,CAAC,CAACuC,aAAa,KAAG,CAAC,GAAChC,CAAC,CAAC6J,aAAa,CAAC,CAAC,IAAE/B,CAAC,CAAC6E,cAAc,CAAC,CAAC,EAAC3M,CAAC,CAAC4K,YAAY,CAAC,CAAC,CAAC,EAACxK,CAAC,CAACqM,SAAS,CAAC,MAAI;QAAC,IAAI1E,CAAC;QAAC,OAAM,CAACA,CAAC,GAACtI,CAAC,CAAC6J,QAAQ,CAAChI,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACyG,CAAC,CAACtF,KAAK,CAAC;UAAC+L,aAAa,EAAC,CAAC;QAAC,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA,CAAC,CAAC;IAACnL,CAAC,GAAC7H,EAAE,CAAC,MAAI;MAAC,IAAGiE,CAAC,CAACsE,OAAO,EAAC,OAAM,CAACtE,CAAC,CAACsE,OAAO,EAACtC,CAAC,CAAC,CAACsM,IAAI,CAAC,GAAG,CAAC;IAAA,CAAC,EAAC,CAACtO,CAAC,CAACsE,OAAO,EAACtC,CAAC,CAAC,CAAC;IAACqB,CAAC,GAAC9H,CAAC,CAAC,OAAK;MAAC0L,IAAI,EAACjH,CAAC,CAACuC,aAAa,KAAG,CAAC;MAACD,QAAQ,EAACtC,CAAC,CAACsC,QAAQ;MAACE,KAAK,EAACxC,CAAC,CAACwC;IAAK,CAAC,CAAC,EAAC,CAACxC,CAAC,CAAC,CAAC;IAACsD,CAAC,GAAC;MAAC8C,GAAG,EAAC1E,CAAC;MAACqC,EAAE,EAAC/B,CAAC;MAACyF,IAAI,EAACxK,EAAE,CAACoE,CAAC,EAACrB,CAAC,CAAC8J,SAAS,CAAC;MAACkF,QAAQ,EAAC,CAAC,CAAC;MAAC,eAAe,EAAC,SAAS;MAAC,eAAe,EAAC,CAACxL,CAAC,GAACxD,CAAC,CAAC2C,UAAU,CAACd,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC2B,CAAC,CAACO,EAAE;MAAC,eAAe,EAAC/D,CAAC,CAACuC,aAAa,KAAG,CAAC;MAAC,iBAAiB,EAACqB,CAAC;MAACtB,QAAQ,EAACtC,CAAC,CAACsC,QAAQ;MAAC2M,OAAO,EAAClM,CAAC;MAAC2L,SAAS,EAAChM;IAAC,CAAC;EAAC,OAAO9C,CAAC,CAAC;IAACmM,QAAQ,EAACzI,CAAC;IAAC0I,UAAU,EAAC/J,CAAC;IAACgK,IAAI,EAAC5I,CAAC;IAAC6I,UAAU,EAAC2C,EAAE;IAAC7G,IAAI,EAAC;EAAiB,CAAC,CAAC;AAAA;AAAC,IAAIkH,EAAE,GAAC,OAAO;AAAC,SAASC,EAAEA,CAAC9N,CAAC,EAACC,CAAC,EAAC;EAAC,IAAItB,CAAC,GAACzD,CAAC,CAAC,CAAC;IAAC;MAACwH,EAAE,EAACxD,CAAC,GAAC,6BAA6BP,CAAC,EAAE;MAAC,GAAG0B;IAAC,CAAC,GAACL,CAAC;IAACc,CAAC,GAAC4C,CAAC,CAAC,gBAAgB,CAAC;IAAC/C,CAAC,GAAC2C,EAAE,CAAC,gBAAgB,CAAC;IAAC1C,CAAC,GAAC9E,CAAC,CAACgF,CAAC,CAACyH,QAAQ,EAACtI,CAAC,CAAC;EAAC7E,CAAC,CAAC,MAAIuF,CAAC,CAACkJ,aAAa,CAAC3K,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;EAAC,IAAII,CAAC,GAACtE,CAAC,CAAC,MAAI;MAAC,IAAIuH,CAAC;MAAC,OAAM,CAACA,CAAC,GAACzB,CAAC,CAAC0H,QAAQ,CAAChI,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC+B,CAAC,CAACZ,KAAK,CAAC;QAAC+L,aAAa,EAAC,CAAC;MAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACrM,CAAC,GAACnH,CAAC,CAAC,OAAK;MAAC0L,IAAI,EAAC9E,CAAC,CAACI,aAAa,KAAG,CAAC;MAACD,QAAQ,EAACH,CAAC,CAACG;IAAQ,CAAC,CAAC,EAAC,CAACH,CAAC,CAAC,CAAC;EAAC,OAAOvC,CAAC,CAAC;IAACmM,QAAQ,EAAC;MAAC3F,GAAG,EAACnE,CAAC;MAAC8B,EAAE,EAACxD,CAAC;MAAC0O,OAAO,EAACtO;IAAC,CAAC;IAACqL,UAAU,EAACtK,CAAC;IAACuK,IAAI,EAACvJ,CAAC;IAACwJ,UAAU,EAACgD,EAAE;IAAClH,IAAI,EAAC;EAAgB,CAAC,CAAC;AAAA;AAAC,IAAIoH,EAAE,GAAC,IAAI;EAACC,EAAE,GAAC7P,EAAE,CAAC8P,cAAc,GAAC9P,EAAE,CAAC+P,MAAM;AAAC,SAASC,EAAEA,CAACnO,CAAC,EAACC,CAAC,EAAC;EAAC,IAAItB,CAAC,GAACzD,CAAC,CAAC,CAAC;IAAC;MAACwH,EAAE,EAACxD,CAAC,GAAC,+BAA+BP,CAAC,EAAE;MAAC6I,IAAI,EAACnH,CAAC,GAAC,CAAC,CAAC;MAAC,GAAGS;IAAC,CAAC,GAACd,CAAC;IAACW,CAAC,GAAC+C,CAAC,CAAC,kBAAkB,CAAC;IAAC9C,CAAC,GAAC9E,CAAC,CAAC6E,CAAC,CAACW,UAAU,EAACrB,CAAC,CAAC;IAACX,CAAC,GAACxC,EAAE,CAAC,CAAC;IAACuE,CAAC,GAAC,CAAC,MAAI/B,CAAC,KAAG,IAAI,GAAC,CAACA,CAAC,GAAC1C,EAAE,CAACgC,IAAI,MAAIhC,EAAE,CAACgC,IAAI,GAAC+B,CAAC,CAACO,aAAa,KAAG,CAAC,EAAE,CAAC;EAAC9F,CAAC,CAAC,MAAI;IAAC,IAAI6G,CAAC;IAACtB,CAAC,CAACY,eAAe,CAACf,OAAO,CAACgB,MAAM,GAAC,CAACS,CAAC,GAACjC,CAAC,CAACwB,MAAM,KAAG,IAAI,GAACS,CAAC,GAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACtB,CAAC,CAACY,eAAe,EAACvB,CAAC,CAACwB,MAAM,CAAC,CAAC,EAACpG,CAAC,CAAC,MAAI;IAACuF,CAAC,CAACY,eAAe,CAACf,OAAO,CAACgH,IAAI,GAACnH,CAAC;EAAA,CAAC,EAAC,CAACM,CAAC,CAACY,eAAe,EAAClB,CAAC,CAAC,CAAC,EAACnE,EAAE,CAAC;IAACkS,SAAS,EAACzN,CAAC,CAACW,UAAU,CAACd,OAAO;IAAC6N,OAAO,EAAC1N,CAAC,CAACO,aAAa,KAAG,CAAC;IAACoN,MAAMA,CAACrM,CAAC,EAAC;MAAC,OAAOA,CAAC,CAACsM,YAAY,CAAC,MAAM,CAAC,KAAG,QAAQ,GAACC,UAAU,CAACC,aAAa,GAACxM,CAAC,CAACyM,YAAY,CAAC,MAAM,CAAC,GAACF,UAAU,CAACG,WAAW,GAACH,UAAU,CAACI,aAAa;IAAA,CAAC;IAACC,IAAIA,CAAC5M,CAAC,EAAC;MAACA,CAAC,CAAC6M,YAAY,CAAC,MAAM,EAAC,MAAM,CAAC;IAAA;EAAC,CAAC,CAAC;EAAC,IAAIpN,CAAC,GAAChH,EAAE,CAAC,MAAI;MAAC,IAAIuH,CAAC,EAACE,CAAC;MAAC,OAAM,CAACA,CAAC,GAACxB,CAAC,CAACsC,OAAO,KAAG,IAAI,GAACd,CAAC,GAAC,CAACF,CAAC,GAACtB,CAAC,CAAC8H,SAAS,CAACjI,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACyB,CAAC,CAACS,EAAE;IAAA,CAAC,EAAC,CAAC/B,CAAC,CAACsC,OAAO,EAACtC,CAAC,CAAC8H,SAAS,CAACjI,OAAO,CAAC,CAAC;IAAC+B,CAAC,GAACrI,CAAC,CAAC,OAAK;MAAC0L,IAAI,EAACjF,CAAC,CAACO,aAAa,KAAG,CAAC;MAACwE,MAAM,EAAC,KAAK;IAAC,CAAC,CAAC,EAAC,CAAC/E,CAAC,CAAC,CAAC;IAACqB,CAAC,GAAC;MAAC,iBAAiB,EAACN,CAAC;MAACwL,IAAI,EAAC,SAAS;MAAC,sBAAsB,EAACvM,CAAC,CAACyH,IAAI,KAAG,CAAC,GAAC,CAAC,CAAC,GAAC,KAAK,CAAC;MAAC1F,EAAE,EAACxD,CAAC;MAAC6F,GAAG,EAACnE;IAAC,CAAC;EAAC,OAAOD,CAAC,CAACc,OAAO,IAAEd,CAAC,CAACO,aAAa,KAAG,CAAC,IAAEgC,MAAM,CAACC,MAAM,CAACrC,CAAC,EAAC;IAAC0E,QAAQ,EAACpM,CAAC,CAACoL,aAAa,CAACf,EAAE,EAAC,IAAI,EAAC3C,CAAC,CAAC0E,QAAQ;EAAC,CAAC,CAAC,EAACjH,CAAC,CAAC;IAACmM,QAAQ,EAAC1I,CAAC;IAAC2I,UAAU,EAAC7J,CAAC;IAAC8J,IAAI,EAACrI,CAAC;IAACsI,UAAU,EAACkD,EAAE;IAAC1D,QAAQ,EAAC2D,EAAE;IAACe,OAAO,EAAC1N,CAAC;IAACsF,IAAI,EAAC;EAAkB,CAAC,CAAC;AAAA;AAAC,IAAIqI,EAAE,GAAC,IAAI;AAAC,SAASC,EAAEA,CAACjP,CAAC,EAACC,CAAC,EAAC;EAAC,IAAI2H,CAAC;EAAC,IAAIjJ,CAAC,GAACzD,CAAC,CAAC,CAAC;IAAC;MAACwH,EAAE,EAACxD,CAAC,GAAC,8BAA8BP,CAAC,EAAE;MAACsC,QAAQ,EAACZ,CAAC,GAAC,CAAC,CAAC;MAACc,KAAK,EAACL,CAAC;MAACL,KAAK,EAACE,CAAC,GAAC,IAAI;MAAC,GAAGC;IAAC,CAAC,GAACZ,CAAC;IAACV,CAAC,GAACoE,CAAC,CAAC,iBAAiB,CAAC;IAACrC,CAAC,GAACiC,EAAE,CAAC,iBAAiB,CAAC;IAAC5B,CAAC,GAACpC,CAAC,CAACmC,OAAO,GAACnC,CAAC,CAACY,iBAAiB,KAAGZ,CAAC,CAAC8B,cAAc,CAACN,CAAC,CAAC,GAACxB,CAAC,CAACY,iBAAiB,KAAG,IAAI,GAAC,CAAC,CAAC,GAAC,CAAC,CAAC0H,CAAC,GAACtI,CAAC,CAACa,OAAO,CAACb,CAAC,CAACY,iBAAiB,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC0H,CAAC,CAAClF,EAAE,MAAIxD,CAAC;IAACqD,CAAC,GAACjD,CAAC,CAACuD,UAAU,CAAC/B,CAAC,CAAC;IAACkB,CAAC,GAAC1H,CAAC,CAAC,IAAI,CAAC;IAAC2H,CAAC,GAAC3G,EAAE,CAAC;MAAC2F,QAAQ,EAACZ,CAAC;MAACc,KAAK,EAACL,CAAC;MAACD,MAAM,EAACmB,CAAC;MAACvB,KAAK,EAACE;IAAC,CAAC,CAAC;IAACwB,CAAC,GAACrI,EAAE,CAAC0J,EAAE,CAAC;IAACwD,CAAC,GAAClL,CAAC,CAACmE,CAAC,EAAC+B,CAAC,EAACG,CAAC,GAACA,CAAC,CAAC+M,cAAc,GAAC,IAAI,CAAC;IAACjI,CAAC,GAACjM,CAAC,CAAC,MAAIqG,CAAC,CAACoF,QAAQ,CAAC3F,CAAC,CAAC,CAAC;EAAC1F,CAAC,CAAC,MAAIiG,CAAC,CAACuI,cAAc,CAAC1K,CAAC,EAAC+C,CAAC,CAAC,EAAC,CAACA,CAAC,EAAC/C,CAAC,CAAC,CAAC;EAAC,IAAIgI,CAAC,GAAC5M,CAAC,CAAC,EAAEgF,CAAC,CAACmC,OAAO,IAAEnC,CAAC,CAACwD,UAAU,CAAC,CAAC;EAAC1H,CAAC,CAAC,MAAI;IAAC,IAAG,CAACkE,CAAC,CAACmC,OAAO,IAAE,CAACnC,CAAC,CAACwD,UAAU,EAAC;IAAO,IAAI+E,CAAC,GAACrK,EAAE,CAAC,CAAC;IAAC,OAAOqK,CAAC,CAACsD,qBAAqB,CAAC,MAAI;MAACjE,CAAC,CAAC1G,OAAO,GAAC,CAAC,CAAC;IAAA,CAAC,CAAC,EAACqH,CAAC,CAACsH,OAAO;EAAA,CAAC,EAAC,CAAC7P,CAAC,CAACmC,OAAO,EAACnC,CAAC,CAACwD,UAAU,CAAC,CAAC,EAAC1H,CAAC,CAAC,MAAI;IAAC,IAAG,CAAC8L,CAAC,CAAC1G,OAAO,IAAElB,CAAC,CAAC4B,aAAa,KAAG,CAAC,IAAE,CAACQ,CAAC,IAAEpC,CAAC,CAACmD,iBAAiB,KAAG,CAAC,EAAC;IAAO,IAAIoF,CAAC,GAACrK,EAAE,CAAC,CAAC;IAAC,OAAOqK,CAAC,CAACsD,qBAAqB,CAAC,MAAI;MAAC,IAAIlD,CAAC,EAACC,CAAC;MAAC,CAACA,CAAC,GAAC,CAACD,CAAC,GAACjG,CAAC,CAACxB,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACyH,CAAC,CAACmH,cAAc,KAAG,IAAI,IAAElH,CAAC,CAACzC,IAAI,CAACwC,CAAC,EAAC;QAACoH,KAAK,EAAC;MAAS,CAAC,CAAC;IAAA,CAAC,CAAC,EAACxH,CAAC,CAACsH,OAAO;EAAA,CAAC,EAAC,CAACnN,CAAC,EAACN,CAAC,EAACpC,CAAC,CAAC4B,aAAa,EAAC5B,CAAC,CAACmD,iBAAiB,EAACnD,CAAC,CAACY,iBAAiB,CAAC,CAAC;EAAC,IAAIiH,CAAC,GAACnM,CAAC,CAAC6M,CAAC,IAAE;MAAC,IAAII,CAAC;MAAC,IAAG5H,CAAC,IAAE,CAAC4H,CAAC,GAAC3I,CAAC,CAACmC,OAAO,KAAG,IAAI,IAAEwG,CAAC,CAAChH,QAAQ,CAACH,CAAC,CAAC,EAAC,OAAO+G,CAAC,CAACgE,cAAc,CAAC,CAAC;MAAC5E,CAAC,CAAC,CAAC,EAACjJ,EAAE,CAAC,CAAC,IAAEmN,qBAAqB,CAAC,MAAI;QAAC,IAAIjD,CAAC;QAAC,OAAM,CAACA,CAAC,GAAC5I,CAAC,CAACkJ,QAAQ,CAAChI,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC0H,CAAC,CAACvG,KAAK,CAAC;UAAC+L,aAAa,EAAC,CAAC;QAAC,CAAC,CAAC;MAAA,CAAC,CAAC,EAACpO,CAAC,CAAC8I,IAAI,KAAG,CAAC,IAAE+C,qBAAqB,CAAC,MAAI9J,CAAC,CAAC0H,aAAa,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAAC3B,CAAC,GAACpM,CAAC,CAAC,MAAI;MAAC,IAAIiN,CAAC;MAAC,IAAG5H,CAAC,IAAE,CAAC4H,CAAC,GAAC3I,CAAC,CAACmC,OAAO,KAAG,IAAI,IAAEwG,CAAC,CAAChH,QAAQ,CAACH,CAAC,CAAC,EAAC,OAAOO,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAAC4N,OAAO,CAAC;MAAC,IAAIrD,CAAC,GAACvI,CAAC,CAAC8B,cAAc,CAACN,CAAC,CAAC;MAACO,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAACsE,QAAQ,EAACiG,CAAC,CAAC;IAAA,CAAC,CAAC;IAACR,CAAC,GAACrL,EAAE,CAAC,CAAC;IAACsL,CAAC,GAACtM,CAAC,CAAC6M,CAAC,IAAER,CAAC,CAACiI,MAAM,CAACzH,CAAC,CAAC,CAAC;IAACN,CAAC,GAACvM,CAAC,CAAC6M,CAAC,IAAE;MAAC,IAAIK,CAAC;MAAC,IAAG,CAACb,CAAC,CAACkI,QAAQ,CAAC1H,CAAC,CAAC,IAAExH,CAAC,IAAE,CAAC6H,CAAC,GAAC5I,CAAC,CAACmC,OAAO,KAAG,IAAI,IAAEyG,CAAC,CAACjH,QAAQ,CAACH,CAAC,CAAC,IAAEY,CAAC,EAAC;MAAO,IAAIuG,CAAC,GAAC3I,CAAC,CAAC8B,cAAc,CAACN,CAAC,CAAC;MAACO,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAACsE,QAAQ,EAACqG,CAAC,EAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACR,CAAC,GAACzM,CAAC,CAAC6M,CAAC,IAAE;MAAC,IAAII,CAAC;MAACZ,CAAC,CAACkI,QAAQ,CAAC1H,CAAC,CAAC,KAAGxH,CAAC,IAAE,CAAC4H,CAAC,GAAC3I,CAAC,CAACmC,OAAO,KAAG,IAAI,IAAEwG,CAAC,CAAChH,QAAQ,CAACH,CAAC,CAAC,IAAEY,CAAC,KAAGpC,CAAC,CAACiC,eAAe,CAACf,OAAO,CAACgH,IAAI,IAAEnG,CAAC,CAACgI,UAAU,CAAC/L,CAAC,CAAC4N,OAAO,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACxD,CAAC,GAACxN,CAAC,CAAC,OAAK;MAACsV,MAAM,EAAC9N,CAAC;MAAC+N,QAAQ,EAAClN,CAAC;MAACtB,QAAQ,EAACZ;IAAC,CAAC,CAAC,EAAC,CAACqB,CAAC,EAACa,CAAC,EAAClC,CAAC,CAAC,CAAC;EAAC,OAAO9B,CAAC,CAAC;IAACmM,QAAQ,EAAC;MAAChI,EAAE,EAACxD,CAAC;MAAC6F,GAAG,EAACiC,CAAC;MAACkG,IAAI,EAAC,QAAQ;MAACS,QAAQ,EAACtN,CAAC,KAAG,CAAC,CAAC,GAAC,KAAK,CAAC,GAAC,CAAC,CAAC;MAAC,eAAe,EAACA,CAAC,KAAG,CAAC,CAAC,GAAC,CAAC,CAAC,GAAC,KAAK,CAAC;MAAC,eAAe,EAACkC,CAAC;MAACtB,QAAQ,EAAC,KAAK,CAAC;MAAC2M,OAAO,EAACzG,CAAC;MAACmG,OAAO,EAAClG,CAAC;MAACsI,cAAc,EAACpI,CAAC;MAACqI,YAAY,EAACrI,CAAC;MAACsI,aAAa,EAACrI,CAAC;MAACsI,WAAW,EAACtI,CAAC;MAACuI,cAAc,EAACrI,CAAC;MAACsI,YAAY,EAACtI;IAAC,CAAC;IAACkD,UAAU,EAAC/J,CAAC;IAACgK,IAAI,EAAClD,CAAC;IAACmD,UAAU,EAACmE,EAAE;IAACrI,IAAI,EAAC;EAAiB,CAAC,CAAC;AAAA;AAAC,IAAIqJ,EAAE,GAAC3R,CAAC,CAACiI,EAAE,CAAC;EAAC2J,EAAE,GAAC5R,CAAC,CAACoP,EAAE,CAAC;EAACyC,EAAE,GAAC7R,CAAC,CAAC0M,EAAE,CAAC;EAACoF,EAAE,GAAC9R,CAAC,CAACyP,EAAE,CAAC;EAACsC,EAAE,GAAC/R,CAAC,CAAC8P,EAAE,CAAC;EAACkC,EAAE,GAAChS,CAAC,CAAC4Q,EAAE,CAAC;EAACqB,EAAE,GAACpN,MAAM,CAACC,MAAM,CAAC6M,EAAE,EAAC;IAACO,KAAK,EAACL,EAAE;IAACM,MAAM,EAACP,EAAE;IAACQ,KAAK,EAACN,EAAE;IAACO,OAAO,EAACN,EAAE;IAACO,MAAM,EAACN;EAAE,CAAC,CAAC;AAAC,SAAOC,EAAE,IAAIM,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}