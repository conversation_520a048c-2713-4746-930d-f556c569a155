{"ast": null, "code": "import { clamp } from '../../../utils/clamp.mjs';\nconst number = {\n  test: v => typeof v === \"number\",\n  parse: parseFloat,\n  transform: v => v\n};\nconst alpha = {\n  ...number,\n  transform: v => clamp(0, 1, v)\n};\nconst scale = {\n  ...number,\n  default: 1\n};\nexport { alpha, number, scale };", "map": {"version": 3, "names": ["clamp", "number", "test", "v", "parse", "parseFloat", "transform", "alpha", "scale", "default"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/framer-motion/dist/es/value/types/numbers/index.mjs"], "sourcesContent": ["import { clamp } from '../../../utils/clamp.mjs';\n\nconst number = {\n    test: (v) => typeof v === \"number\",\n    parse: parseFloat,\n    transform: (v) => v,\n};\nconst alpha = {\n    ...number,\n    transform: (v) => clamp(0, 1, v),\n};\nconst scale = {\n    ...number,\n    default: 1,\n};\n\nexport { alpha, number, scale };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,0BAA0B;AAEhD,MAAMC,MAAM,GAAG;EACXC,IAAI,EAAGC,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ;EAClCC,KAAK,EAAEC,UAAU;EACjBC,SAAS,EAAGH,CAAC,IAAKA;AACtB,CAAC;AACD,MAAMI,KAAK,GAAG;EACV,GAAGN,MAAM;EACTK,SAAS,EAAGH,CAAC,IAAKH,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEG,CAAC;AACnC,CAAC;AACD,MAAMK,KAAK,GAAG;EACV,GAAGP,MAAM;EACTQ,OAAO,EAAE;AACb,CAAC;AAED,SAASF,KAAK,EAAEN,MAAM,EAAEO,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}