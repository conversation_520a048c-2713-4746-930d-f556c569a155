{"ast": null, "code": "import { useRef as t } from \"react\";\nimport { useWindowEvent as a } from './use-window-event.js';\nvar s = (r => (r[r.Forwards = 0] = \"Forwards\", r[r.Backwards = 1] = \"Backwards\", r))(s || {});\nfunction n() {\n  let e = t(0);\n  return a(\"keydown\", o => {\n    o.key === \"Tab\" && (e.current = o.shiftKey ? 1 : 0);\n  }, !0), e;\n}\nexport { s as Direction, n as useTabDirection };", "map": {"version": 3, "names": ["useRef", "t", "useWindowEvent", "a", "s", "r", "Forwards", "Backwards", "n", "e", "o", "key", "current", "shift<PERSON>ey", "Direction", "useTabDirection"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/@headlessui/react/dist/hooks/use-tab-direction.js"], "sourcesContent": ["import{useRef as t}from\"react\";import{useWindowEvent as a}from'./use-window-event.js';var s=(r=>(r[r.Forwards=0]=\"Forwards\",r[r.Backwards=1]=\"Backwards\",r))(s||{});function n(){let e=t(0);return a(\"keydown\",o=>{o.key===\"Tab\"&&(e.current=o.shiftKey?1:0)},!0),e}export{s as Direction,n as useTabDirection};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,IAAIC,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,QAAQ,GAAC,CAAC,CAAC,GAAC,UAAU,EAACD,CAAC,CAACA,CAAC,CAACE,SAAS,GAAC,CAAC,CAAC,GAAC,WAAW,EAACF,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC,GAACR,CAAC,CAAC,CAAC,CAAC;EAAC,OAAOE,CAAC,CAAC,SAAS,EAACO,CAAC,IAAE;IAACA,CAAC,CAACC,GAAG,KAAG,KAAK,KAAGF,CAAC,CAACG,OAAO,GAACF,CAAC,CAACG,QAAQ,GAAC,CAAC,GAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC,CAAC,CAAC,EAACJ,CAAC;AAAA;AAAC,SAAOL,CAAC,IAAIU,SAAS,EAACN,CAAC,IAAIO,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}