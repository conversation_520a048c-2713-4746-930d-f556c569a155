import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Monitor,
  Wifi,
  HardDrive,
  Camera,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Maximize2,
  Settings,
  Phone
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const [isMuted, setIsMuted] = useState(false);
  const [isMicOn, setIsMicOn] = useState(false);

  const cameras = [
    { id: 1, name: 'Camera Hostel', status: 'online', location: 'Hostel Area' },
    { id: 2, name: 'Camera Room', status: 'online', location: 'Room 101' },
    { id: 3, name: 'Camera Garden', status: 'online', location: 'Garden View' },
  ];

  return (
    <div className="flex h-screen bg-gray-950">
      {/* Main Content */}
      <div className="flex-1 p-8">
        {/* Hero Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          {/* Left Side - Text Content */}
          <div className="flex flex-col justify-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-5xl font-bold text-white mb-6 leading-tight">
                Highest Level of
                <br />
                <span className="text-green-400">Protection</span>
              </h1>
              <p className="text-gray-400 text-lg mb-8 leading-relaxed">
                We protected of 3 million people with our
                <br />
                fort police security system.
              </p>
              <div className="flex items-center space-x-8">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                  <span className="text-gray-400 text-sm">Management security</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                  <span className="text-gray-400 text-sm">Management security</span>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Right Side - Camera Illustration */}
          <div className="flex items-center justify-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative"
            >
              {/* Green Camera Illustration */}
              <div className="relative">
                <div className="w-64 h-32 bg-gradient-to-r from-green-400 to-green-500 rounded-full transform rotate-12 shadow-2xl">
                  <div className="absolute inset-4 bg-gray-900 rounded-full flex items-center justify-center">
                    <div className="w-8 h-8 bg-red-500 rounded-full animate-pulse"></div>
                  </div>
                </div>
                {/* DNA Helix Icon */}
                <div className="absolute -right-8 top-8">
                  <div className="w-16 h-16 text-green-400">
                    <svg viewBox="0 0 24 24" fill="currentColor" className="w-full h-full">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-gray-900 rounded-xl p-6 border border-gray-800"
          >
            <div className="flex items-center space-x-3 mb-4">
              <Monitor className="w-6 h-6 text-gray-400" />
              <h3 className="text-white font-semibold">Other Device</h3>
            </div>
            <p className="text-gray-400 text-sm">
              We protected of 3 million people with our fort police security system.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-gray-900 rounded-xl p-6 border border-gray-800"
          >
            <div className="flex items-center space-x-3 mb-4">
              <Wifi className="w-6 h-6 text-gray-400" />
              <h3 className="text-white font-semibold">Network Monitoring</h3>
            </div>
            <p className="text-gray-400 text-sm">
              We protected of 3 million people with our fort police security system.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-gray-900 rounded-xl p-6 border border-gray-800"
          >
            <div className="flex items-center space-x-3 mb-4">
              <HardDrive className="w-6 h-6 text-gray-400" />
              <h3 className="text-white font-semibold">Backup</h3>
            </div>
            <p className="text-gray-400 text-sm">
              We protected of 3 million people with our fort police security system.
            </p>
          </motion.div>
        </div>

        {/* Camera Feed Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-gray-900 rounded-xl overflow-hidden border border-gray-800"
        >
          {/* Camera Feed Header */}
          <div className="bg-gray-800 px-6 py-4 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-white font-medium">Managed by:</span>
              <span className="text-gray-400">10.1.1.30</span>
            </div>
            <div className="flex items-center space-x-4 text-gray-400 text-sm">
              <span>Server Connected</span>
              <span>Last than a minute ago</span>
              <span>Version: 11.9.0.351</span>
            </div>
          </div>

          {/* Video Feed */}
          <div className="relative aspect-video bg-gray-800">
            <img
              src="/captured_images/img_20250514_185115_951.jpg"
              alt="Live Camera Feed"
              className="w-full h-full object-cover"
            />

            {/* Video Controls */}
            <div className="absolute bottom-4 left-4 flex items-center space-x-3">
              <button
                onClick={() => setIsMicOn(!isMicOn)}
                className={`p-2 rounded-full ${isMicOn ? 'bg-green-500' : 'bg-gray-700'} transition-colors`}
              >
                {isMicOn ? <Mic className="w-4 h-4 text-white" /> : <MicOff className="w-4 h-4 text-white" />}
              </button>
              <button
                onClick={() => setIsMuted(!isMuted)}
                className={`p-2 rounded-full ${!isMuted ? 'bg-green-500' : 'bg-gray-700'} transition-colors`}
              >
                {!isMuted ? <Volume2 className="w-4 h-4 text-white" /> : <VolumeX className="w-4 h-4 text-white" />}
              </button>
              <button className="p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors">
                <Camera className="w-4 h-4 text-white" />
              </button>
              <button className="p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors">
                <Maximize2 className="w-4 h-4 text-white" />
              </button>
            </div>

            {/* Bottom Action Buttons */}
            <div className="absolute bottom-4 right-4 flex items-center space-x-3">
              <button className="p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors">
                <Settings className="w-4 h-4 text-white" />
              </button>
              <button className="p-2 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors">
                <Phone className="w-4 h-4 text-white" />
              </button>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Right Sidebar - Camera List */}
      <div className="w-80 bg-gray-900 border-l border-gray-800 p-6">
        <h3 className="text-white font-semibold mb-6">Camera Status</h3>
        <div className="space-y-4">
          {cameras.map((camera, index) => (
            <motion.div
              key={camera.id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7 + index * 0.1 }}
              className="bg-gray-800 rounded-lg p-4 border border-gray-700"
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-white font-medium">{camera.name}</span>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                  <span className="text-green-400 text-sm">Online</span>
                </div>
              </div>
              <p className="text-gray-400 text-sm">{camera.location}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;