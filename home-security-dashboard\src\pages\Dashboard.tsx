import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Monitor,
  Wifi,
  HardDrive,
  Camera,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  Maximize2
} from 'lucide-react';

const Dashboard: React.FC = () => {
  const [isMuted, setIsMuted] = useState(false);
  const [isMicOn, setIsMicOn] = useState(false);

  const cameras = [
    { id: 1, name: 'Camera Hostel', status: 'online' },
    { id: 2, name: 'Camera Room', status: 'online' },
    { id: 3, name: 'Camera Garden', status: 'online' },
  ];

  return (
    <div className="h-screen bg-black p-6">
      {/* Feature Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-600 rounded-xl p-6 border border-gray-500"
        >
          <div className="flex items-center space-x-3 mb-4">
            <Monitor className="w-6 h-6 text-gray-300" />
            <h3 className="text-white font-semibold">Other Device</h3>
          </div>
          <p className="text-gray-300 text-sm">
            We protected of 3 million people with our fort police security system.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-600 rounded-xl p-6 border border-gray-500"
        >
          <div className="flex items-center space-x-3 mb-4">
            <Wifi className="w-6 h-6 text-gray-300" />
            <h3 className="text-white font-semibold">Network Monitoring</h3>
          </div>
          <p className="text-gray-300 text-sm">
            We protected of 3 million people with our fort police security system.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-600 rounded-xl p-6 border border-gray-500"
        >
          <div className="flex items-center space-x-3 mb-4">
            <HardDrive className="w-6 h-6 text-gray-300" />
            <h3 className="text-white font-semibold">Backup</h3>
          </div>
          <p className="text-gray-300 text-sm">
            We protected of 3 million people with our fort police security system.
          </p>
        </motion.div>
      </div>

      {/* Main Content Area */}
      <div className="flex gap-6 h-[calc(100vh-200px)]">
        {/* Camera Feed */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="flex-1 bg-gray-600 rounded-xl overflow-hidden border border-gray-500"
        >
          <div className="relative h-full">
            <img
              src="/captured_images/img_20250514_185115_951.jpg"
              alt="Live Camera Feed"
              className="w-full h-full object-cover"
            />

            {/* Video Controls */}
            <div className="absolute bottom-4 left-4 flex items-center space-x-3">
              <button
                onClick={() => setIsMicOn(!isMicOn)}
                className={`p-3 rounded-full ${isMicOn ? 'bg-green-500' : 'bg-gray-700'} transition-colors`}
              >
                {isMicOn ? <Mic className="w-5 h-5 text-white" /> : <MicOff className="w-5 h-5 text-white" />}
              </button>
              <button
                onClick={() => setIsMuted(!isMuted)}
                className={`p-3 rounded-full ${!isMuted ? 'bg-green-500' : 'bg-gray-700'} transition-colors`}
              >
                {!isMuted ? <Volume2 className="w-5 h-5 text-white" /> : <VolumeX className="w-5 h-5 text-white" />}
              </button>
              <button className="p-3 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors">
                <Camera className="w-5 h-5 text-white" />
              </button>
              <button className="p-3 rounded-full bg-gray-700 hover:bg-gray-600 transition-colors">
                <Maximize2 className="w-5 h-5 text-white" />
              </button>
            </div>

            {/* Recording Indicator */}
            <div className="absolute top-4 left-4">
              <div className="flex items-center space-x-2 bg-black bg-opacity-50 rounded-full px-3 py-2">
                <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
                <span className="text-white text-sm">REC</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Right Sidebar - Camera List */}
        <div className="w-80">
          <div className="space-y-4">
            {cameras.map((camera, index) => (
              <motion.div
                key={camera.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 + index * 0.1 }}
                className="bg-gray-600 rounded-xl p-6 border border-gray-500"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Camera className="w-5 h-5 text-gray-300" />
                    <span className="text-white font-medium">{camera.name}</span>
                  </div>
                  <div className="w-3 h-3 rounded-full bg-green-400"></div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;