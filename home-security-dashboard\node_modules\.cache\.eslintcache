[{"E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\index.tsx": "1", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\App.tsx": "2", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\pages\\Dashboard.tsx": "3", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\layout\\Layout.tsx": "4", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\pages\\Alerts.tsx": "5", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\pages\\Settings.tsx": "6", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\utils\\api.ts": "7", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\dashboard\\AlertCard.tsx": "8", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\layout\\Sidebar.tsx": "9", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\layout\\Header.tsx": "10", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\dashboard\\StatusPanel.tsx": "11", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\dashboard\\CameraFeed.tsx": "12", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\dashboard\\Timeline.tsx": "13", "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\shared\\Card.tsx": "14"}, {"size": 268, "mtime": 1748589252917, "results": "15", "hashOfConfig": "16"}, {"size": 608, "mtime": 1748591142841, "results": "17", "hashOfConfig": "16"}, {"size": 5698, "mtime": 1748595512321, "results": "18", "hashOfConfig": "16"}, {"size": 404, "mtime": 1748595315758, "results": "19", "hashOfConfig": "16"}, {"size": 754, "mtime": 1748589260685, "results": "20", "hashOfConfig": "16"}, {"size": 2268, "mtime": 1748589263897, "results": "21", "hashOfConfig": "16"}, {"size": 4238, "mtime": 1748592297360, "results": "22", "hashOfConfig": "16"}, {"size": 1792, "mtime": 1748592290922, "results": "23", "hashOfConfig": "16"}, {"size": 3435, "mtime": 1748595302105, "results": "24", "hashOfConfig": "16"}, {"size": 2540, "mtime": 1748591336375, "results": "25", "hashOfConfig": "16"}, {"size": 1820, "mtime": 1748590462744, "results": "26", "hashOfConfig": "16"}, {"size": 4493, "mtime": 1748592307422, "results": "27", "hashOfConfig": "16"}, {"size": 4299, "mtime": 1748590613166, "results": "28", "hashOfConfig": "16"}, {"size": 1112, "mtime": 1748592276724, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16dnitv", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\index.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\App.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\pages\\Dashboard.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\layout\\Layout.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\pages\\Alerts.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\pages\\Settings.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\utils\\api.ts", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\dashboard\\AlertCard.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\layout\\Sidebar.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\layout\\Header.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\dashboard\\StatusPanel.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\dashboard\\CameraFeed.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\dashboard\\Timeline.tsx", [], [], "E:\\code\\Resonance-KLE\\home-security-dashboard\\src\\components\\shared\\Card.tsx", [], []]