import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  Monitor,
  Camera,
  RotateCcw,
  FileText,
  Settings,
  Phone,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { motion } from 'framer-motion';

interface SidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
}

const navItems = [
  { path: '/', icon: Monitor, label: 'Monitoring' },
  { path: '/camera', icon: Camera, label: 'Security Camera' },
  { path: '/updates', icon: RotateCcw, label: 'Updates' },
  { path: '/license', icon: FileText, label: 'License' },
];

export const Sidebar: React.FC<SidebarProps> = ({ isOpen = true, onClose }) => {
  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 lg:hidden z-20"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <motion.aside
        initial={{ x: -300 }}
        animate={{ x: isOpen ? 0 : -300 }}
        transition={{ type: 'spring', damping: 20 }}
        className={`fixed left-0 top-0 bottom-0 w-64 bg-black border-r border-gray-600 p-6 lg:static z-30
          ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}`}
      >
        {/* Close button - mobile only */}
        <button
          onClick={onClose}
          className="lg:hidden absolute right-4 top-4 p-2 hover:bg-gray-800 rounded-lg transition-colors"
        >
          <ChevronLeft className="w-5 h-5 text-gray-400" />
        </button>

        {/* Logo */}
        <div className="mb-12 mt-4">
          <div className="flex items-center space-x-3 mb-2">
            <div className="w-6 h-6 rounded bg-green-400 flex items-center justify-center">
              <div className="w-3 h-3 bg-gray-900 rounded-sm"></div>
            </div>
            <span className="text-xl font-bold text-white">Oniex</span>
          </div>
          <div className="text-sm text-gray-400 ml-9">Endpoint Security</div>
        </div>

        {/* Navigation */}
        <nav className="space-y-2">
          {navItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) => `
                flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors group
                ${isActive
                  ? 'bg-gray-600 text-white'
                  : 'text-gray-400 hover:bg-gray-600 hover:text-white'}
              `}
            >
              <item.icon className="w-5 h-5" />
              <span className="text-sm">{item.label}</span>
              <ChevronRight className="w-4 h-4 ml-auto opacity-0 group-hover:opacity-100 transition-opacity" />
            </NavLink>
          ))}
        </nav>

        {/* Bottom Actions */}
        <div className="absolute bottom-6 left-6 right-6 space-y-4">
          <button className="flex items-center space-x-3 px-4 py-3 text-gray-400 hover:bg-gray-600 hover:text-white rounded-lg transition-colors w-full">
            <Settings className="w-5 h-5" />
            <span className="text-sm">Settings</span>
          </button>
          <button className="flex items-center space-x-3 px-4 py-3 text-gray-400 hover:bg-gray-600 hover:text-white rounded-lg transition-colors w-full">
            <Phone className="w-5 h-5" />
            <span className="text-sm">Support</span>
          </button>
        </div>
      </motion.aside>
    </>
  );
};