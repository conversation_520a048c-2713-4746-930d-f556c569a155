{"ast": null, "code": "import { warning } from '../../utils/errors.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { instantAnimationState } from '../../utils/use-instant-transition-state.mjs';\nimport { createAcceleratedAnimation } from '../animators/waapi/create-accelerated-animation.mjs';\nimport { createInstantAnimation } from '../animators/instant.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isAnimatable } from '../utils/is-animatable.mjs';\nimport { getKeyframes } from '../utils/keyframes.mjs';\nimport { getValueTransition, isTransitionDefined } from '../utils/transitions.mjs';\nimport { animateValue } from '../animators/js/index.mjs';\nimport { MotionGlobalConfig } from '../../utils/GlobalConfig.mjs';\nconst animateMotionValue = (valueName, value, target, transition = {}) => {\n  return onComplete => {\n    const valueTransition = getValueTransition(transition, valueName) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let {\n      elapsed = 0\n    } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const keyframes = getKeyframes(value, valueName, target, valueTransition);\n    /**\n     * Check if we're able to animate between the start and end keyframes,\n     * and throw a warning if we're attempting to animate between one that's\n     * animatable and another that isn't.\n     */\n    const originKeyframe = keyframes[0];\n    const targetKeyframe = keyframes[keyframes.length - 1];\n    const isOriginAnimatable = isAnimatable(valueName, originKeyframe);\n    const isTargetAnimatable = isAnimatable(valueName, targetKeyframe);\n    warning(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${valueName} from \"${originKeyframe}\" to \"${targetKeyframe}\". ${originKeyframe} is not an animatable value - to enable this animation set ${originKeyframe} to a value animatable to ${targetKeyframe} via the \\`style\\` property.`);\n    let options = {\n      keyframes,\n      velocity: value.getVelocity(),\n      ease: \"easeOut\",\n      ...valueTransition,\n      delay: -elapsed,\n      onUpdate: v => {\n        value.set(v);\n        valueTransition.onUpdate && valueTransition.onUpdate(v);\n      },\n      onComplete: () => {\n        onComplete();\n        valueTransition.onComplete && valueTransition.onComplete();\n      }\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unqiue transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n      options = {\n        ...options,\n        ...getDefaultTransition(valueName, options)\n      };\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    if (options.duration) {\n      options.duration = secondsToMilliseconds(options.duration);\n    }\n    if (options.repeatDelay) {\n      options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n    }\n    if (!isOriginAnimatable || !isTargetAnimatable || instantAnimationState.current || valueTransition.type === false || MotionGlobalConfig.skipAnimations) {\n      /**\n       * If we can't animate this value, or the global instant animation flag is set,\n       * or this is simply defined as an instant transition, return an instant transition.\n       */\n      return createInstantAnimation(instantAnimationState.current ? {\n        ...options,\n        delay: 0\n      } : options);\n    }\n    /**\n     * Animate via WAAPI if possible.\n     */\n    if (\n    /**\n     * If this is a handoff animation, the optimised animation will be running via\n     * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n     * optimised animation.\n     */\n    !transition.isHandoff && value.owner && value.owner.current instanceof HTMLElement &&\n    /**\n     * If we're outputting values to onUpdate then we can't use WAAPI as there's\n     * no way to read the value from WAAPI every frame.\n     */\n    !value.owner.getProps().onUpdate) {\n      const acceleratedAnimation = createAcceleratedAnimation(value, valueName, options);\n      if (acceleratedAnimation) return acceleratedAnimation;\n    }\n    /**\n     * If we didn't create an accelerated animation, create a JS animation\n     */\n    return animateValue(options);\n  };\n};\nexport { animateMotionValue };", "map": {"version": 3, "names": ["warning", "secondsToMilliseconds", "instantAnimationState", "createAcceleratedAnimation", "createInstantAnimation", "getDefaultTransition", "isAnimatable", "getKeyframes", "getValueTransition", "isTransitionDefined", "animateValue", "MotionGlobalConfig", "animateMotionValue", "valueName", "value", "target", "transition", "onComplete", "valueTransition", "delay", "elapsed", "keyframes", "originKeyframe", "targetKeyframe", "length", "isOriginAnimatable", "isTargetAnimatable", "options", "velocity", "getVelocity", "ease", "onUpdate", "v", "set", "duration", "repeatDelay", "current", "type", "skipAnimations", "<PERSON><PERSON><PERSON><PERSON>", "owner", "HTMLElement", "getProps", "acceleratedAnimation"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs"], "sourcesContent": ["import { warning } from '../../utils/errors.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { instantAnimationState } from '../../utils/use-instant-transition-state.mjs';\nimport { createAcceleratedAnimation } from '../animators/waapi/create-accelerated-animation.mjs';\nimport { createInstantAnimation } from '../animators/instant.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isAnimatable } from '../utils/is-animatable.mjs';\nimport { getKeyframes } from '../utils/keyframes.mjs';\nimport { getValueTransition, isTransitionDefined } from '../utils/transitions.mjs';\nimport { animateValue } from '../animators/js/index.mjs';\nimport { MotionGlobalConfig } from '../../utils/GlobalConfig.mjs';\n\nconst animateMotionValue = (valueName, value, target, transition = {}) => {\n    return (onComplete) => {\n        const valueTransition = getValueTransition(transition, valueName) || {};\n        /**\n         * Most transition values are currently completely overwritten by value-specific\n         * transitions. In the future it'd be nicer to blend these transitions. But for now\n         * delay actually does inherit from the root transition if not value-specific.\n         */\n        const delay = valueTransition.delay || transition.delay || 0;\n        /**\n         * Elapsed isn't a public transition option but can be passed through from\n         * optimized appear effects in milliseconds.\n         */\n        let { elapsed = 0 } = transition;\n        elapsed = elapsed - secondsToMilliseconds(delay);\n        const keyframes = getKeyframes(value, valueName, target, valueTransition);\n        /**\n         * Check if we're able to animate between the start and end keyframes,\n         * and throw a warning if we're attempting to animate between one that's\n         * animatable and another that isn't.\n         */\n        const originKeyframe = keyframes[0];\n        const targetKeyframe = keyframes[keyframes.length - 1];\n        const isOriginAnimatable = isAnimatable(valueName, originKeyframe);\n        const isTargetAnimatable = isAnimatable(valueName, targetKeyframe);\n        warning(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${valueName} from \"${originKeyframe}\" to \"${targetKeyframe}\". ${originKeyframe} is not an animatable value - to enable this animation set ${originKeyframe} to a value animatable to ${targetKeyframe} via the \\`style\\` property.`);\n        let options = {\n            keyframes,\n            velocity: value.getVelocity(),\n            ease: \"easeOut\",\n            ...valueTransition,\n            delay: -elapsed,\n            onUpdate: (v) => {\n                value.set(v);\n                valueTransition.onUpdate && valueTransition.onUpdate(v);\n            },\n            onComplete: () => {\n                onComplete();\n                valueTransition.onComplete && valueTransition.onComplete();\n            },\n        };\n        /**\n         * If there's no transition defined for this value, we can generate\n         * unqiue transition settings for this value.\n         */\n        if (!isTransitionDefined(valueTransition)) {\n            options = {\n                ...options,\n                ...getDefaultTransition(valueName, options),\n            };\n        }\n        /**\n         * Both WAAPI and our internal animation functions use durations\n         * as defined by milliseconds, while our external API defines them\n         * as seconds.\n         */\n        if (options.duration) {\n            options.duration = secondsToMilliseconds(options.duration);\n        }\n        if (options.repeatDelay) {\n            options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n        }\n        if (!isOriginAnimatable ||\n            !isTargetAnimatable ||\n            instantAnimationState.current ||\n            valueTransition.type === false ||\n            MotionGlobalConfig.skipAnimations) {\n            /**\n             * If we can't animate this value, or the global instant animation flag is set,\n             * or this is simply defined as an instant transition, return an instant transition.\n             */\n            return createInstantAnimation(instantAnimationState.current\n                ? { ...options, delay: 0 }\n                : options);\n        }\n        /**\n         * Animate via WAAPI if possible.\n         */\n        if (\n        /**\n         * If this is a handoff animation, the optimised animation will be running via\n         * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n         * optimised animation.\n         */\n        !transition.isHandoff &&\n            value.owner &&\n            value.owner.current instanceof HTMLElement &&\n            /**\n             * If we're outputting values to onUpdate then we can't use WAAPI as there's\n             * no way to read the value from WAAPI every frame.\n             */\n            !value.owner.getProps().onUpdate) {\n            const acceleratedAnimation = createAcceleratedAnimation(value, valueName, options);\n            if (acceleratedAnimation)\n                return acceleratedAnimation;\n        }\n        /**\n         * If we didn't create an accelerated animation, create a JS animation\n         */\n        return animateValue(options);\n    };\n};\n\nexport { animateMotionValue };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,qBAAqB,QAAQ,8CAA8C;AACpF,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,sBAAsB,QAAQ,0BAA0B;AACjE,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,0BAA0B;AAClF,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AAEjE,MAAMC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,GAAG,CAAC,CAAC,KAAK;EACtE,OAAQC,UAAU,IAAK;IACnB,MAAMC,eAAe,GAAGV,kBAAkB,CAACQ,UAAU,EAAEH,SAAS,CAAC,IAAI,CAAC,CAAC;IACvE;AACR;AACA;AACA;AACA;IACQ,MAAMM,KAAK,GAAGD,eAAe,CAACC,KAAK,IAAIH,UAAU,CAACG,KAAK,IAAI,CAAC;IAC5D;AACR;AACA;AACA;IACQ,IAAI;MAAEC,OAAO,GAAG;IAAE,CAAC,GAAGJ,UAAU;IAChCI,OAAO,GAAGA,OAAO,GAAGnB,qBAAqB,CAACkB,KAAK,CAAC;IAChD,MAAME,SAAS,GAAGd,YAAY,CAACO,KAAK,EAAED,SAAS,EAAEE,MAAM,EAAEG,eAAe,CAAC;IACzE;AACR;AACA;AACA;AACA;IACQ,MAAMI,cAAc,GAAGD,SAAS,CAAC,CAAC,CAAC;IACnC,MAAME,cAAc,GAAGF,SAAS,CAACA,SAAS,CAACG,MAAM,GAAG,CAAC,CAAC;IACtD,MAAMC,kBAAkB,GAAGnB,YAAY,CAACO,SAAS,EAAES,cAAc,CAAC;IAClE,MAAMI,kBAAkB,GAAGpB,YAAY,CAACO,SAAS,EAAEU,cAAc,CAAC;IAClEvB,OAAO,CAACyB,kBAAkB,KAAKC,kBAAkB,EAAE,6BAA6Bb,SAAS,UAAUS,cAAc,SAASC,cAAc,MAAMD,cAAc,8DAA8DA,cAAc,6BAA6BC,cAAc,8BAA8B,CAAC;IAClT,IAAII,OAAO,GAAG;MACVN,SAAS;MACTO,QAAQ,EAAEd,KAAK,CAACe,WAAW,CAAC,CAAC;MAC7BC,IAAI,EAAE,SAAS;MACf,GAAGZ,eAAe;MAClBC,KAAK,EAAE,CAACC,OAAO;MACfW,QAAQ,EAAGC,CAAC,IAAK;QACblB,KAAK,CAACmB,GAAG,CAACD,CAAC,CAAC;QACZd,eAAe,CAACa,QAAQ,IAAIb,eAAe,CAACa,QAAQ,CAACC,CAAC,CAAC;MAC3D,CAAC;MACDf,UAAU,EAAEA,CAAA,KAAM;QACdA,UAAU,CAAC,CAAC;QACZC,eAAe,CAACD,UAAU,IAAIC,eAAe,CAACD,UAAU,CAAC,CAAC;MAC9D;IACJ,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACR,mBAAmB,CAACS,eAAe,CAAC,EAAE;MACvCS,OAAO,GAAG;QACN,GAAGA,OAAO;QACV,GAAGtB,oBAAoB,CAACQ,SAAS,EAAEc,OAAO;MAC9C,CAAC;IACL;IACA;AACR;AACA;AACA;AACA;IACQ,IAAIA,OAAO,CAACO,QAAQ,EAAE;MAClBP,OAAO,CAACO,QAAQ,GAAGjC,qBAAqB,CAAC0B,OAAO,CAACO,QAAQ,CAAC;IAC9D;IACA,IAAIP,OAAO,CAACQ,WAAW,EAAE;MACrBR,OAAO,CAACQ,WAAW,GAAGlC,qBAAqB,CAAC0B,OAAO,CAACQ,WAAW,CAAC;IACpE;IACA,IAAI,CAACV,kBAAkB,IACnB,CAACC,kBAAkB,IACnBxB,qBAAqB,CAACkC,OAAO,IAC7BlB,eAAe,CAACmB,IAAI,KAAK,KAAK,IAC9B1B,kBAAkB,CAAC2B,cAAc,EAAE;MACnC;AACZ;AACA;AACA;MACY,OAAOlC,sBAAsB,CAACF,qBAAqB,CAACkC,OAAO,GACrD;QAAE,GAAGT,OAAO;QAAER,KAAK,EAAE;MAAE,CAAC,GACxBQ,OAAO,CAAC;IAClB;IACA;AACR;AACA;IACQ;IACA;AACR;AACA;AACA;AACA;IACQ,CAACX,UAAU,CAACuB,SAAS,IACjBzB,KAAK,CAAC0B,KAAK,IACX1B,KAAK,CAAC0B,KAAK,CAACJ,OAAO,YAAYK,WAAW;IAC1C;AACZ;AACA;AACA;IACY,CAAC3B,KAAK,CAAC0B,KAAK,CAACE,QAAQ,CAAC,CAAC,CAACX,QAAQ,EAAE;MAClC,MAAMY,oBAAoB,GAAGxC,0BAA0B,CAACW,KAAK,EAAED,SAAS,EAAEc,OAAO,CAAC;MAClF,IAAIgB,oBAAoB,EACpB,OAAOA,oBAAoB;IACnC;IACA;AACR;AACA;IACQ,OAAOjC,YAAY,CAACiB,OAAO,CAAC;EAChC,CAAC;AACL,CAAC;AAED,SAASf,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}