{"ast": null, "code": "function isSVGElement(element) {\n  return element instanceof SVGElement && element.tagName !== \"svg\";\n}\nexport { isSVGElement };", "map": {"version": 3, "names": ["isSVGElement", "element", "SVGElement", "tagName"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/framer-motion/dist/es/render/dom/utils/is-svg-element.mjs"], "sourcesContent": ["function isSVGElement(element) {\n    return element instanceof SVGElement && element.tagName !== \"svg\";\n}\n\nexport { isSVGElement };\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,OAAO,EAAE;EAC3B,OAAOA,OAAO,YAAYC,UAAU,IAAID,OAAO,CAACE,OAAO,KAAK,KAAK;AACrE;AAEA,SAASH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}