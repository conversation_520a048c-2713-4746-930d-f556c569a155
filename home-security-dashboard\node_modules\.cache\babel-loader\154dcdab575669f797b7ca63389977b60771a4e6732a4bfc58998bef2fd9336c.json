{"ast": null, "code": "import { moveItem } from '../../../utils/array.mjs';\nimport { mix } from '../../../utils/mix.mjs';\nfunction checkReorder(order, value, offset, velocity) {\n  if (!velocity) return order;\n  const index = order.findIndex(item => item.value === value);\n  if (index === -1) return order;\n  const nextOffset = velocity > 0 ? 1 : -1;\n  const nextItem = order[index + nextOffset];\n  if (!nextItem) return order;\n  const item = order[index];\n  const nextLayout = nextItem.layout;\n  const nextItemCenter = mix(nextLayout.min, nextLayout.max, 0.5);\n  if (nextOffset === 1 && item.layout.max + offset > nextItemCenter || nextOffset === -1 && item.layout.min + offset < nextItemCenter) {\n    return moveItem(order, index, index + nextOffset);\n  }\n  return order;\n}\nexport { checkReorder };", "map": {"version": 3, "names": ["moveItem", "mix", "check<PERSON>eor<PERSON>", "order", "value", "offset", "velocity", "index", "findIndex", "item", "nextOffset", "nextItem", "nextLayout", "layout", "nextItemCenter", "min", "max"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/framer-motion/dist/es/components/Reorder/utils/check-reorder.mjs"], "sourcesContent": ["import { moveItem } from '../../../utils/array.mjs';\nimport { mix } from '../../../utils/mix.mjs';\n\nfunction checkReorder(order, value, offset, velocity) {\n    if (!velocity)\n        return order;\n    const index = order.findIndex((item) => item.value === value);\n    if (index === -1)\n        return order;\n    const nextOffset = velocity > 0 ? 1 : -1;\n    const nextItem = order[index + nextOffset];\n    if (!nextItem)\n        return order;\n    const item = order[index];\n    const nextLayout = nextItem.layout;\n    const nextItemCenter = mix(nextLayout.min, nextLayout.max, 0.5);\n    if ((nextOffset === 1 && item.layout.max + offset > nextItemCenter) ||\n        (nextOffset === -1 && item.layout.min + offset < nextItemCenter)) {\n        return moveItem(order, index, index + nextOffset);\n    }\n    return order;\n}\n\nexport { checkReorder };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,GAAG,QAAQ,wBAAwB;AAE5C,SAASC,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAClD,IAAI,CAACA,QAAQ,EACT,OAAOH,KAAK;EAChB,MAAMI,KAAK,GAAGJ,KAAK,CAACK,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACL,KAAK,KAAKA,KAAK,CAAC;EAC7D,IAAIG,KAAK,KAAK,CAAC,CAAC,EACZ,OAAOJ,KAAK;EAChB,MAAMO,UAAU,GAAGJ,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACxC,MAAMK,QAAQ,GAAGR,KAAK,CAACI,KAAK,GAAGG,UAAU,CAAC;EAC1C,IAAI,CAACC,QAAQ,EACT,OAAOR,KAAK;EAChB,MAAMM,IAAI,GAAGN,KAAK,CAACI,KAAK,CAAC;EACzB,MAAMK,UAAU,GAAGD,QAAQ,CAACE,MAAM;EAClC,MAAMC,cAAc,GAAGb,GAAG,CAACW,UAAU,CAACG,GAAG,EAAEH,UAAU,CAACI,GAAG,EAAE,GAAG,CAAC;EAC/D,IAAKN,UAAU,KAAK,CAAC,IAAID,IAAI,CAACI,MAAM,CAACG,GAAG,GAAGX,MAAM,GAAGS,cAAc,IAC7DJ,UAAU,KAAK,CAAC,CAAC,IAAID,IAAI,CAACI,MAAM,CAACE,GAAG,GAAGV,MAAM,GAAGS,cAAe,EAAE;IAClE,OAAOd,QAAQ,CAACG,KAAK,EAAEI,KAAK,EAAEA,KAAK,GAAGG,UAAU,CAAC;EACrD;EACA,OAAOP,KAAK;AAChB;AAEA,SAASD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}