{"ast": null, "code": "import { setDay } from \"../../../setDay.js\";\nimport { Parser } from \"../Parser.js\";\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\n// Stand-alone local day of week\nexport class StandAloneLocalDayParser extends Parser {\n  priority = 90;\n  parse(dateString, token, match, options) {\n    const valueCallback = value => {\n      // We want here floor instead of trunc, so we get -7 for value 0 instead of 0\n      const wholeWeekDays = Math.floor((value - 1) / 7) * 7;\n      return (value + options.weekStartsOn + 6) % 7 + wholeWeekDays;\n    };\n    switch (token) {\n      // 3\n      case \"c\":\n      case \"cc\":\n        // 03\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n      // 3rd\n      case \"co\":\n        return mapValue(match.ordinalNumber(dateString, {\n          unit: \"day\"\n        }), valueCallback);\n      // Tue\n      case \"ccc\":\n        return match.day(dateString, {\n          width: \"abbreviated\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"short\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n\n      // T\n      case \"ccccc\":\n        return match.day(dateString, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n      // Tu\n      case \"cccccc\":\n        return match.day(dateString, {\n          width: \"short\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n\n      // Tuesday\n      case \"cccc\":\n      default:\n        return match.day(dateString, {\n          width: \"wide\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"abbreviated\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"short\",\n          context: \"standalone\"\n        }) || match.day(dateString, {\n          width: \"narrow\",\n          context: \"standalone\"\n        });\n    }\n  }\n  validate(_date, value) {\n    return value >= 0 && value <= 6;\n  }\n  set(date, _flags, value, options) {\n    date = setDay(date, value, options);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n  incompatibleTokens = [\"y\", \"R\", \"u\", \"q\", \"Q\", \"M\", \"L\", \"I\", \"d\", \"D\", \"E\", \"i\", \"e\", \"t\", \"T\"];\n}", "map": {"version": 3, "names": ["setDay", "<PERSON><PERSON><PERSON>", "mapValue", "parseNDigits", "StandAloneLocalDayParser", "priority", "parse", "dateString", "token", "match", "options", "valueCallback", "value", "wholeWeekDays", "Math", "floor", "weekStartsOn", "length", "ordinalNumber", "unit", "day", "width", "context", "validate", "_date", "set", "date", "_flags", "setHours", "incompatibleTokens"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/parse/_lib/parsers/StandAloneLocalDayParser.js"], "sourcesContent": ["import { setDay } from \"../../../setDay.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\n// Stand-alone local day of week\nexport class StandAloneLocalDayParser extends Parser {\n  priority = 90;\n\n  parse(dateString, token, match, options) {\n    const valueCallback = (value) => {\n      // We want here floor instead of trunc, so we get -7 for value 0 instead of 0\n      const wholeWeekDays = Math.floor((value - 1) / 7) * 7;\n      return ((value + options.weekStartsOn + 6) % 7) + wholeWeekDays;\n    };\n\n    switch (token) {\n      // 3\n      case \"c\":\n      case \"cc\": // 03\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n      // 3rd\n      case \"co\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"day\",\n          }),\n          valueCallback,\n        );\n      // Tue\n      case \"ccc\":\n        return (\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"standalone\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n\n      // T\n      case \"ccccc\":\n        return match.day(dateString, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // Tu\n      case \"cccccc\":\n        return (\n          match.day(dateString, { width: \"short\", context: \"standalone\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n\n      // Tuesday\n      case \"cccc\":\n      default:\n        return (\n          match.day(dateString, { width: \"wide\", context: \"standalone\" }) ||\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"standalone\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 6;\n  }\n\n  set(date, _flags, value, options) {\n    date = setDay(date, value, options);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"R\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"E\",\n    \"i\",\n    \"e\",\n    \"t\",\n    \"T\",\n  ];\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,QAAQ,EAAEC,YAAY,QAAQ,aAAa;;AAEpD;AACA,OAAO,MAAMC,wBAAwB,SAASH,MAAM,CAAC;EACnDI,QAAQ,GAAG,EAAE;EAEbC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAE;IACvC,MAAMC,aAAa,GAAIC,KAAK,IAAK;MAC/B;MACA,MAAMC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC;MACrD,OAAQ,CAACA,KAAK,GAAGF,OAAO,CAACM,YAAY,GAAG,CAAC,IAAI,CAAC,GAAIH,aAAa;IACjE,CAAC;IAED,QAAQL,KAAK;MACX;MACA,KAAK,GAAG;MACR,KAAK,IAAI;QAAE;QACT,OAAON,QAAQ,CAACC,YAAY,CAACK,KAAK,CAACS,MAAM,EAAEV,UAAU,CAAC,EAAEI,aAAa,CAAC;MACxE;MACA,KAAK,IAAI;QACP,OAAOT,QAAQ,CACbO,KAAK,CAACS,aAAa,CAACX,UAAU,EAAE;UAC9BY,IAAI,EAAE;QACR,CAAC,CAAC,EACFR,aACF,CAAC;MACH;MACA,KAAK,KAAK;QACR,OACEF,KAAK,CAACW,GAAG,CAACb,UAAU,EAAE;UACpBc,KAAK,EAAE,aAAa;UACpBC,OAAO,EAAE;QACX,CAAC,CAAC,IACFb,KAAK,CAACW,GAAG,CAACb,UAAU,EAAE;UAAEc,KAAK,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAa,CAAC,CAAC,IAChEb,KAAK,CAACW,GAAG,CAACb,UAAU,EAAE;UAAEc,KAAK,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAa,CAAC,CAAC;;MAGrE;MACA,KAAK,OAAO;QACV,OAAOb,KAAK,CAACW,GAAG,CAACb,UAAU,EAAE;UAC3Bc,KAAK,EAAE,QAAQ;UACfC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,KAAK,QAAQ;QACX,OACEb,KAAK,CAACW,GAAG,CAACb,UAAU,EAAE;UAAEc,KAAK,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAa,CAAC,CAAC,IAChEb,KAAK,CAACW,GAAG,CAACb,UAAU,EAAE;UAAEc,KAAK,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAa,CAAC,CAAC;;MAGrE;MACA,KAAK,MAAM;MACX;QACE,OACEb,KAAK,CAACW,GAAG,CAACb,UAAU,EAAE;UAAEc,KAAK,EAAE,MAAM;UAAEC,OAAO,EAAE;QAAa,CAAC,CAAC,IAC/Db,KAAK,CAACW,GAAG,CAACb,UAAU,EAAE;UACpBc,KAAK,EAAE,aAAa;UACpBC,OAAO,EAAE;QACX,CAAC,CAAC,IACFb,KAAK,CAACW,GAAG,CAACb,UAAU,EAAE;UAAEc,KAAK,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAa,CAAC,CAAC,IAChEb,KAAK,CAACW,GAAG,CAACb,UAAU,EAAE;UAAEc,KAAK,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAa,CAAC,CAAC;IAEvE;EACF;EAEAC,QAAQA,CAACC,KAAK,EAAEZ,KAAK,EAAE;IACrB,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,CAAC;EACjC;EAEAa,GAAGA,CAACC,IAAI,EAAEC,MAAM,EAAEf,KAAK,EAAEF,OAAO,EAAE;IAChCgB,IAAI,GAAG1B,MAAM,CAAC0B,IAAI,EAAEd,KAAK,EAAEF,OAAO,CAAC;IACnCgB,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzB,OAAOF,IAAI;EACb;EAEAG,kBAAkB,GAAG,CACnB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACJ;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}