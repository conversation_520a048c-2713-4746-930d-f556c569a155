{"ast": null, "code": "import { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { isCSSVariableName } from '../dom/utils/is-css-variable.mjs';\nimport { transformProps } from './utils/transform.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { renderHTML } from './utils/render.mjs';\nimport { getDefaultValueType } from '../dom/value-types/defaults.mjs';\nimport { measureViewportBox } from '../../projection/utils/measure.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nfunction getComputedStyle(element) {\n  return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends DOMVisualElement {\n  constructor() {\n    super(...arguments);\n    this.type = \"html\";\n  }\n  readValueFromInstance(instance, key) {\n    if (transformProps.has(key)) {\n      const defaultType = getDefaultValueType(key);\n      return defaultType ? defaultType.default || 0 : 0;\n    } else {\n      const computedStyle = getComputedStyle(instance);\n      const value = (isCSSVariableName(key) ? computedStyle.getPropertyValue(key) : computedStyle[key]) || 0;\n      return typeof value === \"string\" ? value.trim() : value;\n    }\n  }\n  measureInstanceViewportBox(instance, {\n    transformPagePoint\n  }) {\n    return measureViewportBox(instance, transformPagePoint);\n  }\n  build(renderState, latestValues, options, props) {\n    buildHTMLStyles(renderState, latestValues, options, props.transformTemplate);\n  }\n  scrapeMotionValuesFromProps(props, prevProps) {\n    return scrapeMotionValuesFromProps(props, prevProps);\n  }\n  handleChildMotionValue() {\n    if (this.childSubscription) {\n      this.childSubscription();\n      delete this.childSubscription;\n    }\n    const {\n      children\n    } = this.props;\n    if (isMotionValue(children)) {\n      this.childSubscription = children.on(\"change\", latest => {\n        if (this.current) this.current.textContent = `${latest}`;\n      });\n    }\n  }\n  renderInstance(instance, renderState, styleProp, projection) {\n    renderHTML(instance, renderState, styleProp, projection);\n  }\n}\nexport { HTMLVisualElement, getComputedStyle };", "map": {"version": 3, "names": ["buildHTMLStyles", "isCSSVariableName", "transformProps", "scrapeMotionValuesFromProps", "renderHTML", "getDefaultValueType", "measureViewportBox", "DOMVisualElement", "isMotionValue", "getComputedStyle", "element", "window", "HTMLVisualElement", "constructor", "arguments", "type", "readValueFromInstance", "instance", "key", "has", "defaultType", "default", "computedStyle", "value", "getPropertyValue", "trim", "measureInstanceViewportBox", "transformPagePoint", "build", "renderState", "latestValues", "options", "props", "transformTemplate", "prevProps", "handleChildMotionValue", "childSubscription", "children", "on", "latest", "current", "textContent", "renderInstance", "styleProp", "projection"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs"], "sourcesContent": ["import { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { isCSSVariableName } from '../dom/utils/is-css-variable.mjs';\nimport { transformProps } from './utils/transform.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nimport { renderHTML } from './utils/render.mjs';\nimport { getDefaultValueType } from '../dom/value-types/defaults.mjs';\nimport { measureViewportBox } from '../../projection/utils/measure.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\n\nfunction getComputedStyle(element) {\n    return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"html\";\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            const defaultType = getDefaultValueType(key);\n            return defaultType ? defaultType.default || 0 : 0;\n        }\n        else {\n            const computedStyle = getComputedStyle(instance);\n            const value = (isCSSVariableName(key)\n                ? computedStyle.getPropertyValue(key)\n                : computedStyle[key]) || 0;\n            return typeof value === \"string\" ? value.trim() : value;\n        }\n    }\n    measureInstanceViewportBox(instance, { transformPagePoint }) {\n        return measureViewportBox(instance, transformPagePoint);\n    }\n    build(renderState, latestValues, options, props) {\n        buildHTMLStyles(renderState, latestValues, options, props.transformTemplate);\n    }\n    scrapeMotionValuesFromProps(props, prevProps) {\n        return scrapeMotionValuesFromProps(props, prevProps);\n    }\n    handleChildMotionValue() {\n        if (this.childSubscription) {\n            this.childSubscription();\n            delete this.childSubscription;\n        }\n        const { children } = this.props;\n        if (isMotionValue(children)) {\n            this.childSubscription = children.on(\"change\", (latest) => {\n                if (this.current)\n                    this.current.textContent = `${latest}`;\n            });\n        }\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        renderHTML(instance, renderState, styleProp, projection);\n    }\n}\n\nexport { HTMLVisualElement, getComputedStyle };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,cAAc,QAAQ,uBAAuB;AACtD,SAASC,2BAA2B,QAAQ,kCAAkC;AAC9E,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,aAAa,QAAQ,uCAAuC;AAErE,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,OAAOC,MAAM,CAACF,gBAAgB,CAACC,OAAO,CAAC;AAC3C;AACA,MAAME,iBAAiB,SAASL,gBAAgB,CAAC;EAC7CM,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,IAAI,GAAG,MAAM;EACtB;EACAC,qBAAqBA,CAACC,QAAQ,EAAEC,GAAG,EAAE;IACjC,IAAIhB,cAAc,CAACiB,GAAG,CAACD,GAAG,CAAC,EAAE;MACzB,MAAME,WAAW,GAAGf,mBAAmB,CAACa,GAAG,CAAC;MAC5C,OAAOE,WAAW,GAAGA,WAAW,CAACC,OAAO,IAAI,CAAC,GAAG,CAAC;IACrD,CAAC,MACI;MACD,MAAMC,aAAa,GAAGb,gBAAgB,CAACQ,QAAQ,CAAC;MAChD,MAAMM,KAAK,GAAG,CAACtB,iBAAiB,CAACiB,GAAG,CAAC,GAC/BI,aAAa,CAACE,gBAAgB,CAACN,GAAG,CAAC,GACnCI,aAAa,CAACJ,GAAG,CAAC,KAAK,CAAC;MAC9B,OAAO,OAAOK,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACE,IAAI,CAAC,CAAC,GAAGF,KAAK;IAC3D;EACJ;EACAG,0BAA0BA,CAACT,QAAQ,EAAE;IAAEU;EAAmB,CAAC,EAAE;IACzD,OAAOrB,kBAAkB,CAACW,QAAQ,EAAEU,kBAAkB,CAAC;EAC3D;EACAC,KAAKA,CAACC,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAC7ChC,eAAe,CAAC6B,WAAW,EAAEC,YAAY,EAAEC,OAAO,EAAEC,KAAK,CAACC,iBAAiB,CAAC;EAChF;EACA9B,2BAA2BA,CAAC6B,KAAK,EAAEE,SAAS,EAAE;IAC1C,OAAO/B,2BAA2B,CAAC6B,KAAK,EAAEE,SAAS,CAAC;EACxD;EACAC,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;MACxB,OAAO,IAAI,CAACA,iBAAiB;IACjC;IACA,MAAM;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACL,KAAK;IAC/B,IAAIxB,aAAa,CAAC6B,QAAQ,CAAC,EAAE;MACzB,IAAI,CAACD,iBAAiB,GAAGC,QAAQ,CAACC,EAAE,CAAC,QAAQ,EAAGC,MAAM,IAAK;QACvD,IAAI,IAAI,CAACC,OAAO,EACZ,IAAI,CAACA,OAAO,CAACC,WAAW,GAAG,GAAGF,MAAM,EAAE;MAC9C,CAAC,CAAC;IACN;EACJ;EACAG,cAAcA,CAACzB,QAAQ,EAAEY,WAAW,EAAEc,SAAS,EAAEC,UAAU,EAAE;IACzDxC,UAAU,CAACa,QAAQ,EAAEY,WAAW,EAAEc,SAAS,EAAEC,UAAU,CAAC;EAC5D;AACJ;AAEA,SAAShC,iBAAiB,EAAEH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}