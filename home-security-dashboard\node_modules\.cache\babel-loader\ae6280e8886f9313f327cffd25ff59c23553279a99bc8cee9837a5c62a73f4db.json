{"ast": null, "code": "function t(n) {\n  function e() {\n    document.readyState !== \"loading\" && (n(), document.removeEventListener(\"DOMContentLoaded\", e));\n  }\n  typeof window != \"undefined\" && typeof document != \"undefined\" && (document.addEventListener(\"DOMContentLoaded\", e), e());\n}\nexport { t as onDocumentReady };", "map": {"version": 3, "names": ["t", "n", "e", "document", "readyState", "removeEventListener", "window", "addEventListener", "onDocumentReady"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/@headlessui/react/dist/utils/document-ready.js"], "sourcesContent": ["function t(n){function e(){document.readyState!==\"loading\"&&(n(),document.removeEventListener(\"DOMContentLoaded\",e))}typeof window!=\"undefined\"&&typeof document!=\"undefined\"&&(document.addEventListener(\"DOMContentLoaded\",e),e())}export{t as onDocumentReady};\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAAC;EAAC,SAASC,CAACA,CAAA,EAAE;IAACC,QAAQ,CAACC,UAAU,KAAG,SAAS,KAAGH,CAAC,CAAC,CAAC,EAACE,QAAQ,CAACE,mBAAmB,CAAC,kBAAkB,EAACH,CAAC,CAAC,CAAC;EAAA;EAAC,OAAOI,MAAM,IAAE,WAAW,IAAE,OAAOH,QAAQ,IAAE,WAAW,KAAGA,QAAQ,CAACI,gBAAgB,CAAC,kBAAkB,EAACL,CAAC,CAAC,EAACA,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIQ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}