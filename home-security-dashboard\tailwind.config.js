/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}', './public/index.html'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        background: {
          DEFAULT: '#0a0a0a',
          light: '#1a1a1a',
        },
        primary: {
          DEFAULT: '#00ff88',
          dark: '#00cc6a',
        },
        secondary: {
          DEFAULT: '#10B981',
          dark: '#059669',
        },
        green: {
          400: '#00ff88',
          500: '#00cc6a',
        }
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      }
    },
  },
  plugins: [],
};