import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Maximize2 } from 'lucide-react';
import { Dialog } from '@headlessui/react';

interface CameraFeedProps {
    streamUrl?: string;
    location?: string;
}

export const CameraFeed: React.FC<CameraFeedProps> = ({
    streamUrl = '/stream',
    location = 'Main Entrance'
}) => {
    const [isFullscreen, setIsFullscreen] = useState(false);

    return (
        <div className="relative">
            <div className="aspect-video bg-gray-800 rounded-lg overflow-hidden relative">
                <img
                    src={streamUrl}
                    alt="Live Camera Feed"
                    className="w-full h-full object-cover"
                />

                {/* Live Status Indicator */}
                <div className="absolute top-4 left-4 flex items-center space-x-2 bg-black bg-opacity-50 rounded-full px-3 py-1">
                    <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse" />
                    <span className="text-white text-sm">Live</span>
                </div>

                {/* Camera Info */}
                <div className="absolute bottom-4 left-4 right-4 flex justify-between items-center">
                    <div className="bg-black bg-opacity-50 rounded-lg px-3 py-1">
                        <span className="text-white text-sm">{location}</span>
                    </div>

                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setIsFullscreen(!isFullscreen)}
                        className="bg-black bg-opacity-50 rounded-lg p-2 text-white hover:bg-opacity-75 transition-all"
                    >
                        <Maximize2 className="w-5 h-5" />
                    </motion.button>
                </div>
            </div>

            {/* Motion Detection Indicators */}
            <div className="mt-4 grid grid-cols-2 gap-4">
                <div className="bg-gray-800 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                        <span className="text-gray-400">Motion Detected</span>
                        <span className="text-green-400">Yes</span>
                    </div>
                    <div className="mt-2 w-full h-1 bg-gray-700 rounded-full overflow-hidden">
                        <motion.div
                            className="h-full bg-green-500"
                            initial={{ width: "0%" }}
                            animate={{ width: "75%" }}
                            transition={{ duration: 1 }}
                        />
                    </div>
                </div>

                <div className="bg-gray-800 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                        <span className="text-gray-400">Signal Strength</span>
                        <span className="text-blue-400">Excellent</span>
                    </div>
                    <div className="mt-2 w-full h-1 bg-gray-700 rounded-full overflow-hidden">
                        <motion.div
                            className="h-full bg-blue-500"
                            initial={{ width: "0%" }}
                            animate={{ width: "90%" }}
                            transition={{ duration: 1 }}
                        />
                    </div>
                </div>
            </div>

            {/* Fullscreen Modal */}
            <Dialog
                open={isFullscreen}
                onClose={() => setIsFullscreen(false)}
                className="fixed inset-0 z-50 flex items-center justify-center"
            >
                <Dialog.Overlay className="fixed inset-0 bg-black opacity-75" />

                <div className="relative w-full max-w-5xl p-4">
                    <img
                        src={streamUrl}
                        alt="Live Camera Feed"
                        className="w-full h-full object-contain rounded-lg"
                    />
                    <button
                        onClick={() => setIsFullscreen(false)}
                        className="absolute top-6 right-6 text-white hover:text-gray-300"
                    >
                        <Maximize2 className="w-6 h-6" />
                    </button>
                </div>
            </Dialog>
        </div>
    );
};