{"ast": null, "code": "import { endOfDay } from \"./endOfDay.js\";\nimport { endOfMonth } from \"./endOfMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * @name isLastDayOfMonth\n * @category Month Helpers\n * @summary Is the given date the last day of a month?\n *\n * @description\n * Is the given date the last day of a month?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is the last day of a month\n *\n * @example\n * // Is 28 February 2014 the last day of a month?\n * const result = isLastDayOfMonth(new Date(2014, 1, 28))\n * //=> true\n */\nexport function isLastDayOfMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  return +endOfDay(_date, options) === +endOfMonth(_date, options);\n}\n\n// Fallback for modularized imports:\nexport default isLastDayOfMonth;", "map": {"version": 3, "names": ["endOfDay", "endOfMonth", "toDate", "isLastDayOfMonth", "date", "options", "_date", "in"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/isLastDayOfMonth.js"], "sourcesContent": ["import { endOfDay } from \"./endOfDay.js\";\nimport { endOfMonth } from \"./endOfMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * @name isLastDayOfMonth\n * @category Month Helpers\n * @summary Is the given date the last day of a month?\n *\n * @description\n * Is the given date the last day of a month?\n *\n * @param date - The date to check\n * @param options - An object with options\n *\n * @returns The date is the last day of a month\n *\n * @example\n * // Is 28 February 2014 the last day of a month?\n * const result = isLastDayOfMonth(new Date(2014, 1, 28))\n * //=> true\n */\nexport function isLastDayOfMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  return +endOfDay(_date, options) === +endOfMonth(_date, options);\n}\n\n// Fallback for modularized imports:\nexport default isLastDayOfMonth;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC9C,MAAMC,KAAK,GAAGJ,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,OAAO,CAACP,QAAQ,CAACM,KAAK,EAAED,OAAO,CAAC,KAAK,CAACJ,UAAU,CAACK,KAAK,EAAED,OAAO,CAAC;AAClE;;AAEA;AACA,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}