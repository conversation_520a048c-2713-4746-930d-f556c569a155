{"ast": null, "code": "import T, { createContext as P, Fragment as m, useContext as s, useEffect as d, useMemo as g, useRef as R, useState as E } from \"react\";\nimport { createPortal as C } from \"react-dom\";\nimport { useEvent as c } from '../../hooks/use-event.js';\nimport { useIsoMorphicEffect as y } from '../../hooks/use-iso-morphic-effect.js';\nimport { useOnUnmount as H } from '../../hooks/use-on-unmount.js';\nimport { useOwnerDocument as x } from '../../hooks/use-owner.js';\nimport { useServerHandoffComplete as b } from '../../hooks/use-server-handoff-complete.js';\nimport { optionalRef as h, useSyncRefs as L } from '../../hooks/use-sync-refs.js';\nimport { usePortalRoot as O } from '../../internal/portal-force-root.js';\nimport { env as A } from '../../utils/env.js';\nimport { forwardRefWithAs as G, render as M } from '../../utils/render.js';\nfunction F(p) {\n  let n = O(),\n    l = s(_),\n    e = x(p),\n    [a, o] = E(() => {\n      if (!n && l !== null || A.isServer) return null;\n      let t = e == null ? void 0 : e.getElementById(\"headlessui-portal-root\");\n      if (t) return t;\n      if (e === null) return null;\n      let r = e.createElement(\"div\");\n      return r.setAttribute(\"id\", \"headlessui-portal-root\"), e.body.appendChild(r);\n    });\n  return d(() => {\n    a !== null && (e != null && e.body.contains(a) || e == null || e.body.appendChild(a));\n  }, [a, e]), d(() => {\n    n || l !== null && o(l.current);\n  }, [l, o, n]), a;\n}\nlet U = m;\nfunction N(p, n) {\n  let l = p,\n    e = R(null),\n    a = L(h(u => {\n      e.current = u;\n    }), n),\n    o = x(e),\n    t = F(e),\n    [r] = E(() => {\n      var u;\n      return A.isServer ? null : (u = o == null ? void 0 : o.createElement(\"div\")) != null ? u : null;\n    }),\n    i = s(f),\n    v = b();\n  return y(() => {\n    !t || !r || t.contains(r) || (r.setAttribute(\"data-headlessui-portal\", \"\"), t.appendChild(r));\n  }, [t, r]), y(() => {\n    if (r && i) return i.register(r);\n  }, [i, r]), H(() => {\n    var u;\n    !t || !r || (r instanceof Node && t.contains(r) && t.removeChild(r), t.childNodes.length <= 0 && ((u = t.parentElement) == null || u.removeChild(t)));\n  }), v ? !t || !r ? null : C(M({\n    ourProps: {\n      ref: a\n    },\n    theirProps: l,\n    defaultTag: U,\n    name: \"Portal\"\n  }), r) : null;\n}\nlet S = m,\n  _ = P(null);\nfunction j(p, n) {\n  let {\n      target: l,\n      ...e\n    } = p,\n    o = {\n      ref: L(n)\n    };\n  return T.createElement(_.Provider, {\n    value: l\n  }, M({\n    ourProps: o,\n    theirProps: e,\n    defaultTag: S,\n    name: \"Popover.Group\"\n  }));\n}\nlet f = P(null);\nfunction ee() {\n  let p = s(f),\n    n = R([]),\n    l = c(o => (n.current.push(o), p && p.register(o), () => e(o))),\n    e = c(o => {\n      let t = n.current.indexOf(o);\n      t !== -1 && n.current.splice(t, 1), p && p.unregister(o);\n    }),\n    a = g(() => ({\n      register: l,\n      unregister: e,\n      portals: n\n    }), [l, e, n]);\n  return [n, g(() => function ({\n    children: t\n  }) {\n    return T.createElement(f.Provider, {\n      value: a\n    }, t);\n  }, [a])];\n}\nlet D = G(N),\n  I = G(j),\n  te = Object.assign(D, {\n    Group: I\n  });\nexport { te as Portal, ee as useNestedPortals };", "map": {"version": 3, "names": ["T", "createContext", "P", "Fragment", "m", "useContext", "s", "useEffect", "d", "useMemo", "g", "useRef", "R", "useState", "E", "createPortal", "C", "useEvent", "c", "useIsoMorphicEffect", "y", "useOnUnmount", "H", "useOwnerDocument", "x", "useServerHandoffComplete", "b", "optionalRef", "h", "useSyncRefs", "L", "usePortalRoot", "O", "env", "A", "forwardRefWithAs", "G", "render", "M", "F", "p", "n", "l", "_", "e", "a", "o", "isServer", "t", "getElementById", "r", "createElement", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "contains", "current", "U", "N", "u", "i", "f", "v", "register", "Node", "<PERSON><PERSON><PERSON><PERSON>", "childNodes", "length", "parentElement", "ourProps", "ref", "theirProps", "defaultTag", "name", "S", "j", "target", "Provider", "value", "ee", "push", "indexOf", "splice", "unregister", "portals", "children", "D", "I", "te", "Object", "assign", "Group", "Portal", "useNestedPortals"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/@headlessui/react/dist/components/portal/portal.js"], "sourcesContent": ["import T,{createContext as P,Fragment as m,useContext as s,useEffect as d,useMemo as g,useRef as R,useState as E}from\"react\";import{createPortal as C}from\"react-dom\";import{useEvent as c}from'../../hooks/use-event.js';import{useIsoMorphicEffect as y}from'../../hooks/use-iso-morphic-effect.js';import{useOnUnmount as H}from'../../hooks/use-on-unmount.js';import{useOwnerDocument as x}from'../../hooks/use-owner.js';import{useServerHandoffComplete as b}from'../../hooks/use-server-handoff-complete.js';import{optionalRef as h,useSyncRefs as L}from'../../hooks/use-sync-refs.js';import{usePortalRoot as O}from'../../internal/portal-force-root.js';import{env as A}from'../../utils/env.js';import{forwardRefWithAs as G,render as M}from'../../utils/render.js';function F(p){let n=O(),l=s(_),e=x(p),[a,o]=E(()=>{if(!n&&l!==null||A.isServer)return null;let t=e==null?void 0:e.getElementById(\"headlessui-portal-root\");if(t)return t;if(e===null)return null;let r=e.createElement(\"div\");return r.setAttribute(\"id\",\"headlessui-portal-root\"),e.body.appendChild(r)});return d(()=>{a!==null&&(e!=null&&e.body.contains(a)||e==null||e.body.appendChild(a))},[a,e]),d(()=>{n||l!==null&&o(l.current)},[l,o,n]),a}let U=m;function N(p,n){let l=p,e=R(null),a=L(h(u=>{e.current=u}),n),o=x(e),t=F(e),[r]=E(()=>{var u;return A.isServer?null:(u=o==null?void 0:o.createElement(\"div\"))!=null?u:null}),i=s(f),v=b();return y(()=>{!t||!r||t.contains(r)||(r.setAttribute(\"data-headlessui-portal\",\"\"),t.appendChild(r))},[t,r]),y(()=>{if(r&&i)return i.register(r)},[i,r]),H(()=>{var u;!t||!r||(r instanceof Node&&t.contains(r)&&t.removeChild(r),t.childNodes.length<=0&&((u=t.parentElement)==null||u.removeChild(t)))}),v?!t||!r?null:C(M({ourProps:{ref:a},theirProps:l,defaultTag:U,name:\"Portal\"}),r):null}let S=m,_=P(null);function j(p,n){let{target:l,...e}=p,o={ref:L(n)};return T.createElement(_.Provider,{value:l},M({ourProps:o,theirProps:e,defaultTag:S,name:\"Popover.Group\"}))}let f=P(null);function ee(){let p=s(f),n=R([]),l=c(o=>(n.current.push(o),p&&p.register(o),()=>e(o))),e=c(o=>{let t=n.current.indexOf(o);t!==-1&&n.current.splice(t,1),p&&p.unregister(o)}),a=g(()=>({register:l,unregister:e,portals:n}),[l,e,n]);return[n,g(()=>function({children:t}){return T.createElement(f.Provider,{value:a},t)},[a])]}let D=G(N),I=G(j),te=Object.assign(D,{Group:I});export{te as Portal,ee as useNestedPortals};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,SAAS,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,WAAW;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,+BAA+B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,wBAAwB,IAAIC,CAAC,QAAK,4CAA4C;AAAC,SAAOC,WAAW,IAAIC,CAAC,EAACC,WAAW,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,aAAa,IAAIC,CAAC,QAAK,qCAAqC;AAAC,SAAOC,GAAG,IAAIC,CAAC,QAAK,oBAAoB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACT,CAAC,CAAC,CAAC;IAACU,CAAC,GAACpC,CAAC,CAACqC,CAAC,CAAC;IAACC,CAAC,GAACpB,CAAC,CAACgB,CAAC,CAAC;IAAC,CAACK,CAAC,EAACC,CAAC,CAAC,GAAChC,CAAC,CAAC,MAAI;MAAC,IAAG,CAAC2B,CAAC,IAAEC,CAAC,KAAG,IAAI,IAAER,CAAC,CAACa,QAAQ,EAAC,OAAO,IAAI;MAAC,IAAIC,CAAC,GAACJ,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACK,cAAc,CAAC,wBAAwB,CAAC;MAAC,IAAGD,CAAC,EAAC,OAAOA,CAAC;MAAC,IAAGJ,CAAC,KAAG,IAAI,EAAC,OAAO,IAAI;MAAC,IAAIM,CAAC,GAACN,CAAC,CAACO,aAAa,CAAC,KAAK,CAAC;MAAC,OAAOD,CAAC,CAACE,YAAY,CAAC,IAAI,EAAC,wBAAwB,CAAC,EAACR,CAAC,CAACS,IAAI,CAACC,WAAW,CAACJ,CAAC,CAAC;IAAA,CAAC,CAAC;EAAC,OAAO1C,CAAC,CAAC,MAAI;IAACqC,CAAC,KAAG,IAAI,KAAGD,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACS,IAAI,CAACE,QAAQ,CAACV,CAAC,CAAC,IAAED,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACS,IAAI,CAACC,WAAW,CAACT,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACA,CAAC,EAACD,CAAC,CAAC,CAAC,EAACpC,CAAC,CAAC,MAAI;IAACiC,CAAC,IAAEC,CAAC,KAAG,IAAI,IAAEI,CAAC,CAACJ,CAAC,CAACc,OAAO,CAAC;EAAA,CAAC,EAAC,CAACd,CAAC,EAACI,CAAC,EAACL,CAAC,CAAC,CAAC,EAACI,CAAC;AAAA;AAAC,IAAIY,CAAC,GAACrD,CAAC;AAAC,SAASsD,CAACA,CAAClB,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACF,CAAC;IAACI,CAAC,GAAChC,CAAC,CAAC,IAAI,CAAC;IAACiC,CAAC,GAACf,CAAC,CAACF,CAAC,CAAC+B,CAAC,IAAE;MAACf,CAAC,CAACY,OAAO,GAACG,CAAC;IAAA,CAAC,CAAC,EAAClB,CAAC,CAAC;IAACK,CAAC,GAACtB,CAAC,CAACoB,CAAC,CAAC;IAACI,CAAC,GAACT,CAAC,CAACK,CAAC,CAAC;IAAC,CAACM,CAAC,CAAC,GAACpC,CAAC,CAAC,MAAI;MAAC,IAAI6C,CAAC;MAAC,OAAOzB,CAAC,CAACa,QAAQ,GAAC,IAAI,GAAC,CAACY,CAAC,GAACb,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACK,aAAa,CAAC,KAAK,CAAC,KAAG,IAAI,GAACQ,CAAC,GAAC,IAAI;IAAA,CAAC,CAAC;IAACC,CAAC,GAACtD,CAAC,CAACuD,CAAC,CAAC;IAACC,CAAC,GAACpC,CAAC,CAAC,CAAC;EAAC,OAAON,CAAC,CAAC,MAAI;IAAC,CAAC4B,CAAC,IAAE,CAACE,CAAC,IAAEF,CAAC,CAACO,QAAQ,CAACL,CAAC,CAAC,KAAGA,CAAC,CAACE,YAAY,CAAC,wBAAwB,EAAC,EAAE,CAAC,EAACJ,CAAC,CAACM,WAAW,CAACJ,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACF,CAAC,EAACE,CAAC,CAAC,CAAC,EAAC9B,CAAC,CAAC,MAAI;IAAC,IAAG8B,CAAC,IAAEU,CAAC,EAAC,OAAOA,CAAC,CAACG,QAAQ,CAACb,CAAC,CAAC;EAAA,CAAC,EAAC,CAACU,CAAC,EAACV,CAAC,CAAC,CAAC,EAAC5B,CAAC,CAAC,MAAI;IAAC,IAAIqC,CAAC;IAAC,CAACX,CAAC,IAAE,CAACE,CAAC,KAAGA,CAAC,YAAYc,IAAI,IAAEhB,CAAC,CAACO,QAAQ,CAACL,CAAC,CAAC,IAAEF,CAAC,CAACiB,WAAW,CAACf,CAAC,CAAC,EAACF,CAAC,CAACkB,UAAU,CAACC,MAAM,IAAE,CAAC,KAAG,CAACR,CAAC,GAACX,CAAC,CAACoB,aAAa,KAAG,IAAI,IAAET,CAAC,CAACM,WAAW,CAACjB,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAACc,CAAC,GAAC,CAACd,CAAC,IAAE,CAACE,CAAC,GAAC,IAAI,GAAClC,CAAC,CAACsB,CAAC,CAAC;IAAC+B,QAAQ,EAAC;MAACC,GAAG,EAACzB;IAAC,CAAC;IAAC0B,UAAU,EAAC7B,CAAC;IAAC8B,UAAU,EAACf,CAAC;IAACgB,IAAI,EAAC;EAAQ,CAAC,CAAC,EAACvB,CAAC,CAAC,GAAC,IAAI;AAAA;AAAC,IAAIwB,CAAC,GAACtE,CAAC;EAACuC,CAAC,GAACzC,CAAC,CAAC,IAAI,CAAC;AAAC,SAASyE,CAACA,CAACnC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAG;MAACmC,MAAM,EAAClC,CAAC;MAAC,GAAGE;IAAC,CAAC,GAACJ,CAAC;IAACM,CAAC,GAAC;MAACwB,GAAG,EAACxC,CAAC,CAACW,CAAC;IAAC,CAAC;EAAC,OAAOzC,CAAC,CAACmD,aAAa,CAACR,CAAC,CAACkC,QAAQ,EAAC;IAACC,KAAK,EAACpC;EAAC,CAAC,EAACJ,CAAC,CAAC;IAAC+B,QAAQ,EAACvB,CAAC;IAACyB,UAAU,EAAC3B,CAAC;IAAC4B,UAAU,EAACE,CAAC;IAACD,IAAI,EAAC;EAAe,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIZ,CAAC,GAAC3D,CAAC,CAAC,IAAI,CAAC;AAAC,SAAS6E,EAAEA,CAAA,EAAE;EAAC,IAAIvC,CAAC,GAAClC,CAAC,CAACuD,CAAC,CAAC;IAACpB,CAAC,GAAC7B,CAAC,CAAC,EAAE,CAAC;IAAC8B,CAAC,GAACxB,CAAC,CAAC4B,CAAC,KAAGL,CAAC,CAACe,OAAO,CAACwB,IAAI,CAAClC,CAAC,CAAC,EAACN,CAAC,IAAEA,CAAC,CAACuB,QAAQ,CAACjB,CAAC,CAAC,EAAC,MAAIF,CAAC,CAACE,CAAC,CAAC,CAAC,CAAC;IAACF,CAAC,GAAC1B,CAAC,CAAC4B,CAAC,IAAE;MAAC,IAAIE,CAAC,GAACP,CAAC,CAACe,OAAO,CAACyB,OAAO,CAACnC,CAAC,CAAC;MAACE,CAAC,KAAG,CAAC,CAAC,IAAEP,CAAC,CAACe,OAAO,CAAC0B,MAAM,CAAClC,CAAC,EAAC,CAAC,CAAC,EAACR,CAAC,IAAEA,CAAC,CAAC2C,UAAU,CAACrC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACD,CAAC,GAACnC,CAAC,CAAC,OAAK;MAACqD,QAAQ,EAACrB,CAAC;MAACyC,UAAU,EAACvC,CAAC;MAACwC,OAAO,EAAC3C;IAAC,CAAC,CAAC,EAAC,CAACC,CAAC,EAACE,CAAC,EAACH,CAAC,CAAC,CAAC;EAAC,OAAM,CAACA,CAAC,EAAC/B,CAAC,CAAC,MAAI,UAAS;IAAC2E,QAAQ,EAACrC;EAAC,CAAC,EAAC;IAAC,OAAOhD,CAAC,CAACmD,aAAa,CAACU,CAAC,CAACgB,QAAQ,EAAC;MAACC,KAAK,EAACjC;IAAC,CAAC,EAACG,CAAC,CAAC;EAAA,CAAC,EAAC,CAACH,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIyC,CAAC,GAAClD,CAAC,CAACsB,CAAC,CAAC;EAAC6B,CAAC,GAACnD,CAAC,CAACuC,CAAC,CAAC;EAACa,EAAE,GAACC,MAAM,CAACC,MAAM,CAACJ,CAAC,EAAC;IAACK,KAAK,EAACJ;EAAC,CAAC,CAAC;AAAC,SAAOC,EAAE,IAAII,MAAM,EAACb,EAAE,IAAIc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}