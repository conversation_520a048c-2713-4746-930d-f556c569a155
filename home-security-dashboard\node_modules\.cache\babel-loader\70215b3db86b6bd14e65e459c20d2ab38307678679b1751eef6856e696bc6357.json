{"ast": null, "code": "import { mix } from '../../utils/mix.mjs';\nimport { complex } from '../../value/types/complex/index.mjs';\nconst correctBoxShadow = {\n  correct: (latest, {\n    treeScale,\n    projectionDelta\n  }) => {\n    const original = latest;\n    const shadow = complex.parse(latest);\n    // TODO: Doesn't support multiple shadows\n    if (shadow.length > 5) return original;\n    const template = complex.createTransformer(latest);\n    const offset = typeof shadow[0] !== \"number\" ? 1 : 0;\n    // Calculate the overall context scale\n    const xScale = projectionDelta.x.scale * treeScale.x;\n    const yScale = projectionDelta.y.scale * treeScale.y;\n    shadow[0 + offset] /= xScale;\n    shadow[1 + offset] /= yScale;\n    /**\n     * Ideally we'd correct x and y scales individually, but because blur and\n     * spread apply to both we have to take a scale average and apply that instead.\n     * We could potentially improve the outcome of this by incorporating the ratio between\n     * the two scales.\n     */\n    const averageScale = mix(xScale, yScale, 0.5);\n    // Blur\n    if (typeof shadow[2 + offset] === \"number\") shadow[2 + offset] /= averageScale;\n    // Spread\n    if (typeof shadow[3 + offset] === \"number\") shadow[3 + offset] /= averageScale;\n    return template(shadow);\n  }\n};\nexport { correctBoxShadow };", "map": {"version": 3, "names": ["mix", "complex", "correctBoxShadow", "correct", "latest", "treeScale", "projectionDel<PERSON>", "original", "shadow", "parse", "length", "template", "createTransformer", "offset", "xScale", "x", "scale", "yScale", "y", "averageScale"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs"], "sourcesContent": ["import { mix } from '../../utils/mix.mjs';\nimport { complex } from '../../value/types/complex/index.mjs';\n\nconst correctBoxShadow = {\n    correct: (latest, { treeScale, projectionDelta }) => {\n        const original = latest;\n        const shadow = complex.parse(latest);\n        // TODO: Doesn't support multiple shadows\n        if (shadow.length > 5)\n            return original;\n        const template = complex.createTransformer(latest);\n        const offset = typeof shadow[0] !== \"number\" ? 1 : 0;\n        // Calculate the overall context scale\n        const xScale = projectionDelta.x.scale * treeScale.x;\n        const yScale = projectionDelta.y.scale * treeScale.y;\n        shadow[0 + offset] /= xScale;\n        shadow[1 + offset] /= yScale;\n        /**\n         * Ideally we'd correct x and y scales individually, but because blur and\n         * spread apply to both we have to take a scale average and apply that instead.\n         * We could potentially improve the outcome of this by incorporating the ratio between\n         * the two scales.\n         */\n        const averageScale = mix(xScale, yScale, 0.5);\n        // Blur\n        if (typeof shadow[2 + offset] === \"number\")\n            shadow[2 + offset] /= averageScale;\n        // Spread\n        if (typeof shadow[3 + offset] === \"number\")\n            shadow[3 + offset] /= averageScale;\n        return template(shadow);\n    },\n};\n\nexport { correctBoxShadow };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,qBAAqB;AACzC,SAASC,OAAO,QAAQ,qCAAqC;AAE7D,MAAMC,gBAAgB,GAAG;EACrBC,OAAO,EAAEA,CAACC,MAAM,EAAE;IAAEC,SAAS;IAAEC;EAAgB,CAAC,KAAK;IACjD,MAAMC,QAAQ,GAAGH,MAAM;IACvB,MAAMI,MAAM,GAAGP,OAAO,CAACQ,KAAK,CAACL,MAAM,CAAC;IACpC;IACA,IAAII,MAAM,CAACE,MAAM,GAAG,CAAC,EACjB,OAAOH,QAAQ;IACnB,MAAMI,QAAQ,GAAGV,OAAO,CAACW,iBAAiB,CAACR,MAAM,CAAC;IAClD,MAAMS,MAAM,GAAG,OAAOL,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC;IACpD;IACA,MAAMM,MAAM,GAAGR,eAAe,CAACS,CAAC,CAACC,KAAK,GAAGX,SAAS,CAACU,CAAC;IACpD,MAAME,MAAM,GAAGX,eAAe,CAACY,CAAC,CAACF,KAAK,GAAGX,SAAS,CAACa,CAAC;IACpDV,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,IAAIC,MAAM;IAC5BN,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,IAAII,MAAM;IAC5B;AACR;AACA;AACA;AACA;AACA;IACQ,MAAME,YAAY,GAAGnB,GAAG,CAACc,MAAM,EAAEG,MAAM,EAAE,GAAG,CAAC;IAC7C;IACA,IAAI,OAAOT,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,KAAK,QAAQ,EACtCL,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,IAAIM,YAAY;IACtC;IACA,IAAI,OAAOX,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,KAAK,QAAQ,EACtCL,MAAM,CAAC,CAAC,GAAGK,MAAM,CAAC,IAAIM,YAAY;IACtC,OAAOR,QAAQ,CAACH,MAAM,CAAC;EAC3B;AACJ,CAAC;AAED,SAASN,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}