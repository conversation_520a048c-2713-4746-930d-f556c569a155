{"ast": null, "code": "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { max } from \"./max.js\";\nimport { min } from \"./min.js\";\n\n/**\n * The {@link clamp} function options.\n */\n\n/**\n * The {@link clamp} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name clamp\n * @category Interval Helpers\n * @summary Return a date bounded by the start and the end of the given interval.\n *\n * @description\n * Clamps a date to the lower bound with the start of the interval and the upper\n * bound with the end of the interval.\n *\n * - When the date is less than the start of the interval, the start is returned.\n * - When the date is greater than the end of the interval, the end is returned.\n * - Otherwise the date is returned.\n *\n * @typeParam DateType - Date argument type.\n * @typeParam IntervalType - Interval argument type.\n * @typeParam Options - Options type.\n *\n * @param date - The date to be bounded\n * @param interval - The interval to bound to\n * @param options - An object with options\n *\n * @returns The date bounded by the start and the end of the interval\n *\n * @example\n * // What is Mar 21, 2021 bounded to an interval starting at Mar 22, 2021 and ending at Apr 01, 2021\n * const result = clamp(new Date(2021, 2, 21), {\n *   start: new Date(2021, 2, 22),\n *   end: new Date(2021, 3, 1),\n * })\n * //=> Mon Mar 22 2021 00:00:00\n */\nexport function clamp(date, interval, options) {\n  const [date_, start, end] = normalizeDates(options?.in, date, interval.start, interval.end);\n  return min([max([date_, start], options), end], options);\n}\n\n// Fallback for modularized imports:\nexport default clamp;", "map": {"version": 3, "names": ["normalizeDates", "max", "min", "clamp", "date", "interval", "options", "date_", "start", "end", "in"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/clamp.js"], "sourcesContent": ["import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { max } from \"./max.js\";\nimport { min } from \"./min.js\";\n\n/**\n * The {@link clamp} function options.\n */\n\n/**\n * The {@link clamp} function result type. It resolves the proper data type.\n * It uses the first argument date object type, starting from the date argument,\n * then the start interval date, and finally the end interval date. If\n * a context function is passed, it uses the context function return type.\n */\n\n/**\n * @name clamp\n * @category Interval Helpers\n * @summary Return a date bounded by the start and the end of the given interval.\n *\n * @description\n * Clamps a date to the lower bound with the start of the interval and the upper\n * bound with the end of the interval.\n *\n * - When the date is less than the start of the interval, the start is returned.\n * - When the date is greater than the end of the interval, the end is returned.\n * - Otherwise the date is returned.\n *\n * @typeParam DateType - Date argument type.\n * @typeParam IntervalType - Interval argument type.\n * @typeParam Options - Options type.\n *\n * @param date - The date to be bounded\n * @param interval - The interval to bound to\n * @param options - An object with options\n *\n * @returns The date bounded by the start and the end of the interval\n *\n * @example\n * // What is Mar 21, 2021 bounded to an interval starting at Mar 22, 2021 and ending at Apr 01, 2021\n * const result = clamp(new Date(2021, 2, 21), {\n *   start: new Date(2021, 2, 22),\n *   end: new Date(2021, 3, 1),\n * })\n * //=> Mon Mar 22 2021 00:00:00\n */\nexport function clamp(date, interval, options) {\n  const [date_, start, end] = normalizeDates(\n    options?.in,\n    date,\n    interval.start,\n    interval.end,\n  );\n\n  return min([max([date_, start], options), end], options);\n}\n\n// Fallback for modularized imports:\nexport default clamp;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,0BAA0B;AACzD,SAASC,GAAG,QAAQ,UAAU;AAC9B,SAASC,GAAG,QAAQ,UAAU;;AAE9B;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC7C,MAAM,CAACC,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC,GAAGT,cAAc,CACxCM,OAAO,EAAEI,EAAE,EACXN,IAAI,EACJC,QAAQ,CAACG,KAAK,EACdH,QAAQ,CAACI,GACX,CAAC;EAED,OAAOP,GAAG,CAAC,CAACD,GAAG,CAAC,CAACM,KAAK,EAAEC,KAAK,CAAC,EAAEF,OAAO,CAAC,EAAEG,GAAG,CAAC,EAAEH,OAAO,CAAC;AAC1D;;AAEA;AACA,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}