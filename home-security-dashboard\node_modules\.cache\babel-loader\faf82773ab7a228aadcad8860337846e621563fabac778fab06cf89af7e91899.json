{"ast": null, "code": "import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link roundToNearestHours} function options.\n */\n\n/**\n * @name roundToNearestHours\n * @category Hour Helpers\n * @summary Rounds the given date to the nearest hour\n *\n * @description\n * Rounds the given date to the nearest hour (or number of hours).\n * Rounds up when the given date is exactly between the nearest round hours.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to round\n * @param options - An object with options.\n *\n * @returns The new date rounded to the closest hour\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56))\n * //=> Thu Jul 10 2014 13:00:00\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest half hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { nearestTo: 6 })\n * //=> Thu Jul 10 2014 12:00:00\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest half hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { nearestTo: 8 })\n * //=> Thu Jul 10 2014 16:00:00\n *\n * @example\n * // Floor (rounds down) 10 July 2014 12:34:56 to nearest hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 1, 23, 45), { roundingMethod: 'ceil' })\n * //=> Thu Jul 10 2014 02:00:00\n *\n * @example\n * // Ceil (rounds up) 10 July 2014 12:34:56 to nearest quarter hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { roundingMethod: 'floor', nearestTo: 8 })\n * //=> Thu Jul 10 2014 08:00:00\n */\nexport function roundToNearestHours(date, options) {\n  const nearestTo = options?.nearestTo ?? 1;\n  if (nearestTo < 1 || nearestTo > 12) return constructFrom(options?.in || date, NaN);\n  const date_ = toDate(date, options?.in);\n  const fractionalMinutes = date_.getMinutes() / 60;\n  const fractionalSeconds = date_.getSeconds() / 60 / 60;\n  const fractionalMilliseconds = date_.getMilliseconds() / 1000 / 60 / 60;\n  const hours = date_.getHours() + fractionalMinutes + fractionalSeconds + fractionalMilliseconds;\n  const method = options?.roundingMethod ?? \"round\";\n  const roundingMethod = getRoundingMethod(method);\n  const roundedHours = roundingMethod(hours / nearestTo) * nearestTo;\n  date_.setHours(roundedHours, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default roundToNearestHours;", "map": {"version": 3, "names": ["getRoundingMethod", "constructFrom", "toDate", "roundToNearestHours", "date", "options", "nearestTo", "in", "NaN", "date_", "fractionalMinutes", "getMinutes", "fractionalSeconds", "getSeconds", "fractionalMilliseconds", "getMilliseconds", "hours", "getHours", "method", "roundingMethod", "roundedHours", "setHours"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/roundToNearestHours.js"], "sourcesContent": ["import { getRoundingMethod } from \"./_lib/getRoundingMethod.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link roundToNearestHours} function options.\n */\n\n/**\n * @name roundToNearestHours\n * @category Hour Helpers\n * @summary Rounds the given date to the nearest hour\n *\n * @description\n * Rounds the given date to the nearest hour (or number of hours).\n * Rounds up when the given date is exactly between the nearest round hours.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to round\n * @param options - An object with options.\n *\n * @returns The new date rounded to the closest hour\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56))\n * //=> Thu Jul 10 2014 13:00:00\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest half hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { nearestTo: 6 })\n * //=> Thu Jul 10 2014 12:00:00\n *\n * @example\n * // Round 10 July 2014 12:34:56 to nearest half hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { nearestTo: 8 })\n * //=> Thu Jul 10 2014 16:00:00\n *\n * @example\n * // Floor (rounds down) 10 July 2014 12:34:56 to nearest hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 1, 23, 45), { roundingMethod: 'ceil' })\n * //=> Thu Jul 10 2014 02:00:00\n *\n * @example\n * // Ceil (rounds up) 10 July 2014 12:34:56 to nearest quarter hour:\n * const result = roundToNearestHours(new Date(2014, 6, 10, 12, 34, 56), { roundingMethod: 'floor', nearestTo: 8 })\n * //=> Thu Jul 10 2014 08:00:00\n */\nexport function roundToNearestHours(date, options) {\n  const nearestTo = options?.nearestTo ?? 1;\n\n  if (nearestTo < 1 || nearestTo > 12)\n    return constructFrom(options?.in || date, NaN);\n\n  const date_ = toDate(date, options?.in);\n  const fractionalMinutes = date_.getMinutes() / 60;\n  const fractionalSeconds = date_.getSeconds() / 60 / 60;\n  const fractionalMilliseconds = date_.getMilliseconds() / 1000 / 60 / 60;\n  const hours =\n    date_.getHours() +\n    fractionalMinutes +\n    fractionalSeconds +\n    fractionalMilliseconds;\n\n  const method = options?.roundingMethod ?? \"round\";\n  const roundingMethod = getRoundingMethod(method);\n\n  const roundedHours = roundingMethod(hours / nearestTo) * nearestTo;\n\n  date_.setHours(roundedHours, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default roundToNearestHours;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACjD,MAAMC,SAAS,GAAGD,OAAO,EAAEC,SAAS,IAAI,CAAC;EAEzC,IAAIA,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,EAAE,EACjC,OAAOL,aAAa,CAACI,OAAO,EAAEE,EAAE,IAAIH,IAAI,EAAEI,GAAG,CAAC;EAEhD,MAAMC,KAAK,GAAGP,MAAM,CAACE,IAAI,EAAEC,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMG,iBAAiB,GAAGD,KAAK,CAACE,UAAU,CAAC,CAAC,GAAG,EAAE;EACjD,MAAMC,iBAAiB,GAAGH,KAAK,CAACI,UAAU,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;EACtD,MAAMC,sBAAsB,GAAGL,KAAK,CAACM,eAAe,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;EACvE,MAAMC,KAAK,GACTP,KAAK,CAACQ,QAAQ,CAAC,CAAC,GAChBP,iBAAiB,GACjBE,iBAAiB,GACjBE,sBAAsB;EAExB,MAAMI,MAAM,GAAGb,OAAO,EAAEc,cAAc,IAAI,OAAO;EACjD,MAAMA,cAAc,GAAGnB,iBAAiB,CAACkB,MAAM,CAAC;EAEhD,MAAME,YAAY,GAAGD,cAAc,CAACH,KAAK,GAAGV,SAAS,CAAC,GAAGA,SAAS;EAElEG,KAAK,CAACY,QAAQ,CAACD,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACrC,OAAOX,KAAK;AACd;;AAEA;AACA,eAAeN,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}