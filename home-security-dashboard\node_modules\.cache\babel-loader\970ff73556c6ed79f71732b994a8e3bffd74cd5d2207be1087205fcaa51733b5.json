{"ast": null, "code": "import C, { createContext as Q, createRef as de, useContext as Z, useEffect as ee, useMemo as h, useReducer as ge, useRef as J, useState as ce } from \"react\";\nimport { useNestedPortals as Se } from '../../components/portal/portal.js';\nimport { useEvent as R } from '../../hooks/use-event.js';\nimport { useEventListener as Re } from '../../hooks/use-event-listener.js';\nimport { useId as K } from '../../hooks/use-id.js';\nimport { useIsoMorphicEffect as Ae } from '../../hooks/use-iso-morphic-effect.js';\nimport { useLatestValue as ve } from '../../hooks/use-latest-value.js';\nimport { useOutsideClick as Oe } from '../../hooks/use-outside-click.js';\nimport { useOwnerDocument as ne } from '../../hooks/use-owner.js';\nimport { useResolveButtonType as Ce } from '../../hooks/use-resolve-button-type.js';\nimport { useMainTreeNode as Me, useRootContainers as xe } from '../../hooks/use-root-containers.js';\nimport { optionalRef as Fe, useSyncRefs as j } from '../../hooks/use-sync-refs.js';\nimport { Direction as H, useTabDirection as Te } from '../../hooks/use-tab-direction.js';\nimport { Features as le, Hidden as ae } from '../../internal/hidden.js';\nimport { OpenClosedProvider as Ie, State as V, useOpenClosed as me } from '../../internal/open-closed.js';\nimport { isDisabledReactIssue7711 as ye } from '../../utils/bugs.js';\nimport { Focus as G, FocusableMode as _e, focusIn as N, FocusResult as pe, getFocusableElements as se, isFocusableElement as Le } from '../../utils/focus-management.js';\nimport { match as k } from '../../utils/match.js';\nimport '../../utils/micro-task.js';\nimport { getOwnerDocument as Be } from '../../utils/owner.js';\nimport { Features as te, forwardRefWithAs as X, render as Y, useMergeRefsFn as De } from '../../utils/render.js';\nimport { Keys as w } from '../keyboard.js';\nvar he = (u => (u[u.Open = 0] = \"Open\", u[u.Closed = 1] = \"Closed\", u))(he || {}),\n  He = (e => (e[e.TogglePopover = 0] = \"TogglePopover\", e[e.ClosePopover = 1] = \"ClosePopover\", e[e.SetButton = 2] = \"SetButton\", e[e.SetButtonId = 3] = \"SetButtonId\", e[e.SetPanel = 4] = \"SetPanel\", e[e.SetPanelId = 5] = \"SetPanelId\", e))(He || {});\nlet Ge = {\n    [0]: t => {\n      let o = {\n        ...t,\n        popoverState: k(t.popoverState, {\n          [0]: 1,\n          [1]: 0\n        })\n      };\n      return o.popoverState === 0 && (o.__demoMode = !1), o;\n    },\n    [1](t) {\n      return t.popoverState === 1 ? t : {\n        ...t,\n        popoverState: 1\n      };\n    },\n    [2](t, o) {\n      return t.button === o.button ? t : {\n        ...t,\n        button: o.button\n      };\n    },\n    [3](t, o) {\n      return t.buttonId === o.buttonId ? t : {\n        ...t,\n        buttonId: o.buttonId\n      };\n    },\n    [4](t, o) {\n      return t.panel === o.panel ? t : {\n        ...t,\n        panel: o.panel\n      };\n    },\n    [5](t, o) {\n      return t.panelId === o.panelId ? t : {\n        ...t,\n        panelId: o.panelId\n      };\n    }\n  },\n  ue = Q(null);\nue.displayName = \"PopoverContext\";\nfunction oe(t) {\n  let o = Z(ue);\n  if (o === null) {\n    let u = new Error(`<${t} /> is missing a parent <Popover /> component.`);\n    throw Error.captureStackTrace && Error.captureStackTrace(u, oe), u;\n  }\n  return o;\n}\nlet ie = Q(null);\nie.displayName = \"PopoverAPIContext\";\nfunction fe(t) {\n  let o = Z(ie);\n  if (o === null) {\n    let u = new Error(`<${t} /> is missing a parent <Popover /> component.`);\n    throw Error.captureStackTrace && Error.captureStackTrace(u, fe), u;\n  }\n  return o;\n}\nlet Pe = Q(null);\nPe.displayName = \"PopoverGroupContext\";\nfunction Ee() {\n  return Z(Pe);\n}\nlet re = Q(null);\nre.displayName = \"PopoverPanelContext\";\nfunction Ne() {\n  return Z(re);\n}\nfunction ke(t, o) {\n  return k(o.type, Ge, t, o);\n}\nlet we = \"div\";\nfunction Ue(t, o) {\n  var B;\n  let {\n      __demoMode: u = !1,\n      ...M\n    } = t,\n    x = J(null),\n    n = j(o, Fe(l => {\n      x.current = l;\n    })),\n    e = J([]),\n    c = ge(ke, {\n      __demoMode: u,\n      popoverState: u ? 0 : 1,\n      buttons: e,\n      button: null,\n      buttonId: null,\n      panel: null,\n      panelId: null,\n      beforePanelSentinel: de(),\n      afterPanelSentinel: de()\n    }),\n    [{\n      popoverState: f,\n      button: s,\n      buttonId: I,\n      panel: a,\n      panelId: v,\n      beforePanelSentinel: y,\n      afterPanelSentinel: A\n    }, P] = c,\n    p = ne((B = x.current) != null ? B : s),\n    E = h(() => {\n      if (!s || !a) return !1;\n      for (let W of document.querySelectorAll(\"body > *\")) if (Number(W == null ? void 0 : W.contains(s)) ^ Number(W == null ? void 0 : W.contains(a))) return !0;\n      let l = se(),\n        S = l.indexOf(s),\n        q = (S + l.length - 1) % l.length,\n        U = (S + 1) % l.length,\n        z = l[q],\n        be = l[U];\n      return !a.contains(z) && !a.contains(be);\n    }, [s, a]),\n    F = ve(I),\n    D = ve(v),\n    _ = h(() => ({\n      buttonId: F,\n      panelId: D,\n      close: () => P({\n        type: 1\n      })\n    }), [F, D, P]),\n    O = Ee(),\n    L = O == null ? void 0 : O.registerPopover,\n    $ = R(() => {\n      var l;\n      return (l = O == null ? void 0 : O.isFocusWithinPopoverGroup()) != null ? l : (p == null ? void 0 : p.activeElement) && ((s == null ? void 0 : s.contains(p.activeElement)) || (a == null ? void 0 : a.contains(p.activeElement)));\n    });\n  ee(() => L == null ? void 0 : L(_), [L, _]);\n  let [i, b] = Se(),\n    T = xe({\n      mainTreeNodeRef: O == null ? void 0 : O.mainTreeNodeRef,\n      portals: i,\n      defaultContainers: [s, a]\n    });\n  Re(p == null ? void 0 : p.defaultView, \"focus\", l => {\n    var S, q, U, z;\n    l.target !== window && l.target instanceof HTMLElement && f === 0 && ($() || s && a && (T.contains(l.target) || (q = (S = y.current) == null ? void 0 : S.contains) != null && q.call(S, l.target) || (z = (U = A.current) == null ? void 0 : U.contains) != null && z.call(U, l.target) || P({\n      type: 1\n    })));\n  }, !0), Oe(T.resolveContainers, (l, S) => {\n    P({\n      type: 1\n    }), Le(S, _e.Loose) || (l.preventDefault(), s == null || s.focus());\n  }, f === 0);\n  let d = R(l => {\n      P({\n        type: 1\n      });\n      let S = (() => l ? l instanceof HTMLElement ? l : \"current\" in l && l.current instanceof HTMLElement ? l.current : s : s)();\n      S == null || S.focus();\n    }),\n    r = h(() => ({\n      close: d,\n      isPortalled: E\n    }), [d, E]),\n    m = h(() => ({\n      open: f === 0,\n      close: d\n    }), [f, d]),\n    g = {\n      ref: n\n    };\n  return C.createElement(re.Provider, {\n    value: null\n  }, C.createElement(ue.Provider, {\n    value: c\n  }, C.createElement(ie.Provider, {\n    value: r\n  }, C.createElement(Ie, {\n    value: k(f, {\n      [0]: V.Open,\n      [1]: V.Closed\n    })\n  }, C.createElement(b, null, Y({\n    ourProps: g,\n    theirProps: M,\n    slot: m,\n    defaultTag: we,\n    name: \"Popover\"\n  }), C.createElement(T.MainTreeNode, null))))));\n}\nlet We = \"button\";\nfunction Ke(t, o) {\n  let u = K(),\n    {\n      id: M = `headlessui-popover-button-${u}`,\n      ...x\n    } = t,\n    [n, e] = oe(\"Popover.Button\"),\n    {\n      isPortalled: c\n    } = fe(\"Popover.Button\"),\n    f = J(null),\n    s = `headlessui-focus-sentinel-${K()}`,\n    I = Ee(),\n    a = I == null ? void 0 : I.closeOthers,\n    y = Ne() !== null;\n  ee(() => {\n    if (!y) return e({\n      type: 3,\n      buttonId: M\n    }), () => {\n      e({\n        type: 3,\n        buttonId: null\n      });\n    };\n  }, [y, M, e]);\n  let [A] = ce(() => Symbol()),\n    P = j(f, o, y ? null : r => {\n      if (r) n.buttons.current.push(A);else {\n        let m = n.buttons.current.indexOf(A);\n        m !== -1 && n.buttons.current.splice(m, 1);\n      }\n      n.buttons.current.length > 1 && console.warn(\"You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported.\"), r && e({\n        type: 2,\n        button: r\n      });\n    }),\n    p = j(f, o),\n    E = ne(f),\n    F = R(r => {\n      var m, g, B;\n      if (y) {\n        if (n.popoverState === 1) return;\n        switch (r.key) {\n          case w.Space:\n          case w.Enter:\n            r.preventDefault(), (g = (m = r.target).click) == null || g.call(m), e({\n              type: 1\n            }), (B = n.button) == null || B.focus();\n            break;\n        }\n      } else switch (r.key) {\n        case w.Space:\n        case w.Enter:\n          r.preventDefault(), r.stopPropagation(), n.popoverState === 1 && (a == null || a(n.buttonId)), e({\n            type: 0\n          });\n          break;\n        case w.Escape:\n          if (n.popoverState !== 0) return a == null ? void 0 : a(n.buttonId);\n          if (!f.current || E != null && E.activeElement && !f.current.contains(E.activeElement)) return;\n          r.preventDefault(), r.stopPropagation(), e({\n            type: 1\n          });\n          break;\n      }\n    }),\n    D = R(r => {\n      y || r.key === w.Space && r.preventDefault();\n    }),\n    _ = R(r => {\n      var m, g;\n      ye(r.currentTarget) || t.disabled || (y ? (e({\n        type: 1\n      }), (m = n.button) == null || m.focus()) : (r.preventDefault(), r.stopPropagation(), n.popoverState === 1 && (a == null || a(n.buttonId)), e({\n        type: 0\n      }), (g = n.button) == null || g.focus()));\n    }),\n    O = R(r => {\n      r.preventDefault(), r.stopPropagation();\n    }),\n    L = n.popoverState === 0,\n    $ = h(() => ({\n      open: L\n    }), [L]),\n    i = Ce(t, f),\n    b = y ? {\n      ref: p,\n      type: i,\n      onKeyDown: F,\n      onClick: _\n    } : {\n      ref: P,\n      id: n.buttonId,\n      type: i,\n      \"aria-expanded\": n.popoverState === 0,\n      \"aria-controls\": n.panel ? n.panelId : void 0,\n      onKeyDown: F,\n      onKeyUp: D,\n      onClick: _,\n      onMouseDown: O\n    },\n    T = Te(),\n    d = R(() => {\n      let r = n.panel;\n      if (!r) return;\n      function m() {\n        k(T.current, {\n          [H.Forwards]: () => N(r, G.First),\n          [H.Backwards]: () => N(r, G.Last)\n        }) === pe.Error && N(se().filter(B => B.dataset.headlessuiFocusGuard !== \"true\"), k(T.current, {\n          [H.Forwards]: G.Next,\n          [H.Backwards]: G.Previous\n        }), {\n          relativeTo: n.button\n        });\n      }\n      m();\n    });\n  return C.createElement(C.Fragment, null, Y({\n    ourProps: b,\n    theirProps: x,\n    slot: $,\n    defaultTag: We,\n    name: \"Popover.Button\"\n  }), L && !y && c && C.createElement(ae, {\n    id: s,\n    features: le.Focusable,\n    \"data-headlessui-focus-guard\": !0,\n    as: \"button\",\n    type: \"button\",\n    onFocus: d\n  }));\n}\nlet je = \"div\",\n  Ve = te.RenderStrategy | te.Static;\nfunction $e(t, o) {\n  let u = K(),\n    {\n      id: M = `headlessui-popover-overlay-${u}`,\n      ...x\n    } = t,\n    [{\n      popoverState: n\n    }, e] = oe(\"Popover.Overlay\"),\n    c = j(o),\n    f = me(),\n    s = (() => f !== null ? (f & V.Open) === V.Open : n === 0)(),\n    I = R(y => {\n      if (ye(y.currentTarget)) return y.preventDefault();\n      e({\n        type: 1\n      });\n    }),\n    a = h(() => ({\n      open: n === 0\n    }), [n]);\n  return Y({\n    ourProps: {\n      ref: c,\n      id: M,\n      \"aria-hidden\": !0,\n      onClick: I\n    },\n    theirProps: x,\n    slot: a,\n    defaultTag: je,\n    features: Ve,\n    visible: s,\n    name: \"Popover.Overlay\"\n  });\n}\nlet Je = \"div\",\n  Xe = te.RenderStrategy | te.Static;\nfunction Ye(t, o) {\n  let u = K(),\n    {\n      id: M = `headlessui-popover-panel-${u}`,\n      focus: x = !1,\n      ...n\n    } = t,\n    [e, c] = oe(\"Popover.Panel\"),\n    {\n      close: f,\n      isPortalled: s\n    } = fe(\"Popover.Panel\"),\n    I = `headlessui-focus-sentinel-before-${K()}`,\n    a = `headlessui-focus-sentinel-after-${K()}`,\n    v = J(null),\n    y = j(v, o, i => {\n      c({\n        type: 4,\n        panel: i\n      });\n    }),\n    A = ne(v),\n    P = De();\n  Ae(() => (c({\n    type: 5,\n    panelId: M\n  }), () => {\n    c({\n      type: 5,\n      panelId: null\n    });\n  }), [M, c]);\n  let p = me(),\n    E = (() => p !== null ? (p & V.Open) === V.Open : e.popoverState === 0)(),\n    F = R(i => {\n      var b;\n      switch (i.key) {\n        case w.Escape:\n          if (e.popoverState !== 0 || !v.current || A != null && A.activeElement && !v.current.contains(A.activeElement)) return;\n          i.preventDefault(), i.stopPropagation(), c({\n            type: 1\n          }), (b = e.button) == null || b.focus();\n          break;\n      }\n    });\n  ee(() => {\n    var i;\n    t.static || e.popoverState === 1 && ((i = t.unmount) == null || i) && c({\n      type: 4,\n      panel: null\n    });\n  }, [e.popoverState, t.unmount, t.static, c]), ee(() => {\n    if (e.__demoMode || !x || e.popoverState !== 0 || !v.current) return;\n    let i = A == null ? void 0 : A.activeElement;\n    v.current.contains(i) || N(v.current, G.First);\n  }, [e.__demoMode, x, v, e.popoverState]);\n  let D = h(() => ({\n      open: e.popoverState === 0,\n      close: f\n    }), [e, f]),\n    _ = {\n      ref: y,\n      id: M,\n      onKeyDown: F,\n      onBlur: x && e.popoverState === 0 ? i => {\n        var T, d, r, m, g;\n        let b = i.relatedTarget;\n        b && v.current && ((T = v.current) != null && T.contains(b) || (c({\n          type: 1\n        }), ((r = (d = e.beforePanelSentinel.current) == null ? void 0 : d.contains) != null && r.call(d, b) || (g = (m = e.afterPanelSentinel.current) == null ? void 0 : m.contains) != null && g.call(m, b)) && b.focus({\n          preventScroll: !0\n        })));\n      } : void 0,\n      tabIndex: -1\n    },\n    O = Te(),\n    L = R(() => {\n      let i = v.current;\n      if (!i) return;\n      function b() {\n        k(O.current, {\n          [H.Forwards]: () => {\n            var d;\n            N(i, G.First) === pe.Error && ((d = e.afterPanelSentinel.current) == null || d.focus());\n          },\n          [H.Backwards]: () => {\n            var T;\n            (T = e.button) == null || T.focus({\n              preventScroll: !0\n            });\n          }\n        });\n      }\n      b();\n    }),\n    $ = R(() => {\n      let i = v.current;\n      if (!i) return;\n      function b() {\n        k(O.current, {\n          [H.Forwards]: () => {\n            var B;\n            if (!e.button) return;\n            let T = se(),\n              d = T.indexOf(e.button),\n              r = T.slice(0, d + 1),\n              g = [...T.slice(d + 1), ...r];\n            for (let l of g.slice()) if (l.dataset.headlessuiFocusGuard === \"true\" || (B = e.panel) != null && B.contains(l)) {\n              let S = g.indexOf(l);\n              S !== -1 && g.splice(S, 1);\n            }\n            N(g, G.First, {\n              sorted: !1\n            });\n          },\n          [H.Backwards]: () => {\n            var d;\n            N(i, G.Previous) === pe.Error && ((d = e.button) == null || d.focus());\n          }\n        });\n      }\n      b();\n    });\n  return C.createElement(re.Provider, {\n    value: M\n  }, E && s && C.createElement(ae, {\n    id: I,\n    ref: e.beforePanelSentinel,\n    features: le.Focusable,\n    \"data-headlessui-focus-guard\": !0,\n    as: \"button\",\n    type: \"button\",\n    onFocus: L\n  }), Y({\n    mergeRefs: P,\n    ourProps: _,\n    theirProps: n,\n    slot: D,\n    defaultTag: Je,\n    features: Xe,\n    visible: E,\n    name: \"Popover.Panel\"\n  }), E && s && C.createElement(ae, {\n    id: a,\n    ref: e.afterPanelSentinel,\n    features: le.Focusable,\n    \"data-headlessui-focus-guard\": !0,\n    as: \"button\",\n    type: \"button\",\n    onFocus: $\n  }));\n}\nlet qe = \"div\";\nfunction ze(t, o) {\n  let u = J(null),\n    M = j(u, o),\n    [x, n] = ce([]),\n    e = Me(),\n    c = R(P => {\n      n(p => {\n        let E = p.indexOf(P);\n        if (E !== -1) {\n          let F = p.slice();\n          return F.splice(E, 1), F;\n        }\n        return p;\n      });\n    }),\n    f = R(P => (n(p => [...p, P]), () => c(P))),\n    s = R(() => {\n      var E;\n      let P = Be(u);\n      if (!P) return !1;\n      let p = P.activeElement;\n      return (E = u.current) != null && E.contains(p) ? !0 : x.some(F => {\n        var D, _;\n        return ((D = P.getElementById(F.buttonId.current)) == null ? void 0 : D.contains(p)) || ((_ = P.getElementById(F.panelId.current)) == null ? void 0 : _.contains(p));\n      });\n    }),\n    I = R(P => {\n      for (let p of x) p.buttonId.current !== P && p.close();\n    }),\n    a = h(() => ({\n      registerPopover: f,\n      unregisterPopover: c,\n      isFocusWithinPopoverGroup: s,\n      closeOthers: I,\n      mainTreeNodeRef: e.mainTreeNodeRef\n    }), [f, c, s, I, e.mainTreeNodeRef]),\n    v = h(() => ({}), []),\n    y = t,\n    A = {\n      ref: M\n    };\n  return C.createElement(Pe.Provider, {\n    value: a\n  }, Y({\n    ourProps: A,\n    theirProps: y,\n    slot: v,\n    defaultTag: qe,\n    name: \"Popover.Group\"\n  }), C.createElement(e.MainTreeNode, null));\n}\nlet Qe = X(Ue),\n  Ze = X(Ke),\n  et = X($e),\n  tt = X(Ye),\n  ot = X(ze),\n  Ct = Object.assign(Qe, {\n    Button: Ze,\n    Overlay: et,\n    Panel: tt,\n    Group: ot\n  });\nexport { Ct as Popover };", "map": {"version": 3, "names": ["C", "createContext", "Q", "createRef", "de", "useContext", "Z", "useEffect", "ee", "useMemo", "h", "useReducer", "ge", "useRef", "J", "useState", "ce", "useNestedPortals", "Se", "useEvent", "R", "useEventListener", "Re", "useId", "K", "useIsoMorphicEffect", "Ae", "useLatestValue", "ve", "useOutsideClick", "Oe", "useOwnerDocument", "ne", "useResolveButtonType", "Ce", "useMainTreeNode", "Me", "useRootContainers", "xe", "optionalRef", "Fe", "useSyncRefs", "j", "Direction", "H", "useTabDirection", "Te", "Features", "le", "Hidden", "ae", "OpenClosedProvider", "Ie", "State", "V", "useOpenClosed", "me", "isDisabledReactIssue7711", "ye", "Focus", "G", "FocusableMode", "_e", "focusIn", "N", "FocusResult", "pe", "getFocusableElements", "se", "isFocusableElement", "Le", "match", "k", "getOwnerDocument", "Be", "te", "forwardRefWithAs", "X", "render", "Y", "useMergeRefsFn", "De", "Keys", "w", "he", "u", "Open", "Closed", "He", "e", "TogglePopover", "ClosePopover", "SetButton", "SetButtonId", "SetPanel", "SetPanelId", "Ge", "t", "o", "popoverState", "__demoMode", "button", "buttonId", "panel", "panelId", "ue", "displayName", "oe", "Error", "captureStackTrace", "ie", "fe", "Pe", "Ee", "re", "Ne", "ke", "type", "we", "Ue", "B", "M", "x", "n", "l", "current", "c", "buttons", "beforePanelSentinel", "afterPanelSentinel", "f", "s", "I", "a", "v", "y", "A", "P", "p", "E", "W", "document", "querySelectorAll", "Number", "contains", "S", "indexOf", "q", "length", "U", "z", "be", "F", "D", "_", "close", "O", "L", "registerPopover", "$", "isFocusWithinPopoverGroup", "activeElement", "i", "b", "T", "mainTreeNodeRef", "portals", "defaultContainers", "defaultView", "target", "window", "HTMLElement", "call", "resolveContainers", "Loose", "preventDefault", "focus", "d", "r", "isPortalled", "m", "open", "g", "ref", "createElement", "Provider", "value", "ourProps", "theirProps", "slot", "defaultTag", "name", "MainTreeNode", "We", "<PERSON>", "id", "closeOthers", "Symbol", "push", "splice", "console", "warn", "key", "Space", "Enter", "click", "stopPropagation", "Escape", "currentTarget", "disabled", "onKeyDown", "onClick", "onKeyUp", "onMouseDown", "Forwards", "First", "Backwards", "Last", "filter", "dataset", "headless<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Next", "Previous", "relativeTo", "Fragment", "features", "Focusable", "as", "onFocus", "je", "Ve", "RenderStrategy", "Static", "$e", "visible", "Je", "Xe", "Ye", "static", "unmount", "onBlur", "relatedTarget", "preventScroll", "tabIndex", "slice", "sorted", "mergeRefs", "qe", "ze", "some", "getElementById", "unregisterPopover", "Qe", "Ze", "et", "tt", "ot", "Ct", "Object", "assign", "<PERSON><PERSON>", "Overlay", "Panel", "Group", "Popover"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/@headlessui/react/dist/components/popover/popover.js"], "sourcesContent": ["import C,{createContext as Q,createRef as de,useContext as Z,useEffect as ee,useMemo as h,useReducer as ge,useRef as J,useState as ce}from\"react\";import{useNestedPortals as Se}from'../../components/portal/portal.js';import{useEvent as R}from'../../hooks/use-event.js';import{useEventListener as Re}from'../../hooks/use-event-listener.js';import{useId as K}from'../../hooks/use-id.js';import{useIsoMorphicEffect as Ae}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as ve}from'../../hooks/use-latest-value.js';import{useOutsideClick as Oe}from'../../hooks/use-outside-click.js';import{useOwnerDocument as ne}from'../../hooks/use-owner.js';import{useResolveButtonType as Ce}from'../../hooks/use-resolve-button-type.js';import{useMainTreeNode as Me,useRootContainers as xe}from'../../hooks/use-root-containers.js';import{optionalRef as Fe,useSyncRefs as j}from'../../hooks/use-sync-refs.js';import{Direction as H,useTabDirection as Te}from'../../hooks/use-tab-direction.js';import{Features as le,Hidden as ae}from'../../internal/hidden.js';import{OpenClosedProvider as Ie,State as V,useOpenClosed as me}from'../../internal/open-closed.js';import{isDisabledReactIssue7711 as ye}from'../../utils/bugs.js';import{Focus as G,FocusableMode as _e,focusIn as N,FocusResult as pe,getFocusableElements as se,isFocusableElement as Le}from'../../utils/focus-management.js';import{match as k}from'../../utils/match.js';import'../../utils/micro-task.js';import{getOwnerDocument as Be}from'../../utils/owner.js';import{Features as te,forwardRefWithAs as X,render as Y,useMergeRefsFn as De}from'../../utils/render.js';import{Keys as w}from'../keyboard.js';var he=(u=>(u[u.Open=0]=\"Open\",u[u.Closed=1]=\"Closed\",u))(he||{}),He=(e=>(e[e.TogglePopover=0]=\"TogglePopover\",e[e.ClosePopover=1]=\"ClosePopover\",e[e.SetButton=2]=\"SetButton\",e[e.SetButtonId=3]=\"SetButtonId\",e[e.SetPanel=4]=\"SetPanel\",e[e.SetPanelId=5]=\"SetPanelId\",e))(He||{});let Ge={[0]:t=>{let o={...t,popoverState:k(t.popoverState,{[0]:1,[1]:0})};return o.popoverState===0&&(o.__demoMode=!1),o},[1](t){return t.popoverState===1?t:{...t,popoverState:1}},[2](t,o){return t.button===o.button?t:{...t,button:o.button}},[3](t,o){return t.buttonId===o.buttonId?t:{...t,buttonId:o.buttonId}},[4](t,o){return t.panel===o.panel?t:{...t,panel:o.panel}},[5](t,o){return t.panelId===o.panelId?t:{...t,panelId:o.panelId}}},ue=Q(null);ue.displayName=\"PopoverContext\";function oe(t){let o=Z(ue);if(o===null){let u=new Error(`<${t} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(u,oe),u}return o}let ie=Q(null);ie.displayName=\"PopoverAPIContext\";function fe(t){let o=Z(ie);if(o===null){let u=new Error(`<${t} /> is missing a parent <Popover /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(u,fe),u}return o}let Pe=Q(null);Pe.displayName=\"PopoverGroupContext\";function Ee(){return Z(Pe)}let re=Q(null);re.displayName=\"PopoverPanelContext\";function Ne(){return Z(re)}function ke(t,o){return k(o.type,Ge,t,o)}let we=\"div\";function Ue(t,o){var B;let{__demoMode:u=!1,...M}=t,x=J(null),n=j(o,Fe(l=>{x.current=l})),e=J([]),c=ge(ke,{__demoMode:u,popoverState:u?0:1,buttons:e,button:null,buttonId:null,panel:null,panelId:null,beforePanelSentinel:de(),afterPanelSentinel:de()}),[{popoverState:f,button:s,buttonId:I,panel:a,panelId:v,beforePanelSentinel:y,afterPanelSentinel:A},P]=c,p=ne((B=x.current)!=null?B:s),E=h(()=>{if(!s||!a)return!1;for(let W of document.querySelectorAll(\"body > *\"))if(Number(W==null?void 0:W.contains(s))^Number(W==null?void 0:W.contains(a)))return!0;let l=se(),S=l.indexOf(s),q=(S+l.length-1)%l.length,U=(S+1)%l.length,z=l[q],be=l[U];return!a.contains(z)&&!a.contains(be)},[s,a]),F=ve(I),D=ve(v),_=h(()=>({buttonId:F,panelId:D,close:()=>P({type:1})}),[F,D,P]),O=Ee(),L=O==null?void 0:O.registerPopover,$=R(()=>{var l;return(l=O==null?void 0:O.isFocusWithinPopoverGroup())!=null?l:(p==null?void 0:p.activeElement)&&((s==null?void 0:s.contains(p.activeElement))||(a==null?void 0:a.contains(p.activeElement)))});ee(()=>L==null?void 0:L(_),[L,_]);let[i,b]=Se(),T=xe({mainTreeNodeRef:O==null?void 0:O.mainTreeNodeRef,portals:i,defaultContainers:[s,a]});Re(p==null?void 0:p.defaultView,\"focus\",l=>{var S,q,U,z;l.target!==window&&l.target instanceof HTMLElement&&f===0&&($()||s&&a&&(T.contains(l.target)||(q=(S=y.current)==null?void 0:S.contains)!=null&&q.call(S,l.target)||(z=(U=A.current)==null?void 0:U.contains)!=null&&z.call(U,l.target)||P({type:1})))},!0),Oe(T.resolveContainers,(l,S)=>{P({type:1}),Le(S,_e.Loose)||(l.preventDefault(),s==null||s.focus())},f===0);let d=R(l=>{P({type:1});let S=(()=>l?l instanceof HTMLElement?l:\"current\"in l&&l.current instanceof HTMLElement?l.current:s:s)();S==null||S.focus()}),r=h(()=>({close:d,isPortalled:E}),[d,E]),m=h(()=>({open:f===0,close:d}),[f,d]),g={ref:n};return C.createElement(re.Provider,{value:null},C.createElement(ue.Provider,{value:c},C.createElement(ie.Provider,{value:r},C.createElement(Ie,{value:k(f,{[0]:V.Open,[1]:V.Closed})},C.createElement(b,null,Y({ourProps:g,theirProps:M,slot:m,defaultTag:we,name:\"Popover\"}),C.createElement(T.MainTreeNode,null))))))}let We=\"button\";function Ke(t,o){let u=K(),{id:M=`headlessui-popover-button-${u}`,...x}=t,[n,e]=oe(\"Popover.Button\"),{isPortalled:c}=fe(\"Popover.Button\"),f=J(null),s=`headlessui-focus-sentinel-${K()}`,I=Ee(),a=I==null?void 0:I.closeOthers,y=Ne()!==null;ee(()=>{if(!y)return e({type:3,buttonId:M}),()=>{e({type:3,buttonId:null})}},[y,M,e]);let[A]=ce(()=>Symbol()),P=j(f,o,y?null:r=>{if(r)n.buttons.current.push(A);else{let m=n.buttons.current.indexOf(A);m!==-1&&n.buttons.current.splice(m,1)}n.buttons.current.length>1&&console.warn(\"You are already using a <Popover.Button /> but only 1 <Popover.Button /> is supported.\"),r&&e({type:2,button:r})}),p=j(f,o),E=ne(f),F=R(r=>{var m,g,B;if(y){if(n.popoverState===1)return;switch(r.key){case w.Space:case w.Enter:r.preventDefault(),(g=(m=r.target).click)==null||g.call(m),e({type:1}),(B=n.button)==null||B.focus();break}}else switch(r.key){case w.Space:case w.Enter:r.preventDefault(),r.stopPropagation(),n.popoverState===1&&(a==null||a(n.buttonId)),e({type:0});break;case w.Escape:if(n.popoverState!==0)return a==null?void 0:a(n.buttonId);if(!f.current||E!=null&&E.activeElement&&!f.current.contains(E.activeElement))return;r.preventDefault(),r.stopPropagation(),e({type:1});break}}),D=R(r=>{y||r.key===w.Space&&r.preventDefault()}),_=R(r=>{var m,g;ye(r.currentTarget)||t.disabled||(y?(e({type:1}),(m=n.button)==null||m.focus()):(r.preventDefault(),r.stopPropagation(),n.popoverState===1&&(a==null||a(n.buttonId)),e({type:0}),(g=n.button)==null||g.focus()))}),O=R(r=>{r.preventDefault(),r.stopPropagation()}),L=n.popoverState===0,$=h(()=>({open:L}),[L]),i=Ce(t,f),b=y?{ref:p,type:i,onKeyDown:F,onClick:_}:{ref:P,id:n.buttonId,type:i,\"aria-expanded\":n.popoverState===0,\"aria-controls\":n.panel?n.panelId:void 0,onKeyDown:F,onKeyUp:D,onClick:_,onMouseDown:O},T=Te(),d=R(()=>{let r=n.panel;if(!r)return;function m(){k(T.current,{[H.Forwards]:()=>N(r,G.First),[H.Backwards]:()=>N(r,G.Last)})===pe.Error&&N(se().filter(B=>B.dataset.headlessuiFocusGuard!==\"true\"),k(T.current,{[H.Forwards]:G.Next,[H.Backwards]:G.Previous}),{relativeTo:n.button})}m()});return C.createElement(C.Fragment,null,Y({ourProps:b,theirProps:x,slot:$,defaultTag:We,name:\"Popover.Button\"}),L&&!y&&c&&C.createElement(ae,{id:s,features:le.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:d}))}let je=\"div\",Ve=te.RenderStrategy|te.Static;function $e(t,o){let u=K(),{id:M=`headlessui-popover-overlay-${u}`,...x}=t,[{popoverState:n},e]=oe(\"Popover.Overlay\"),c=j(o),f=me(),s=(()=>f!==null?(f&V.Open)===V.Open:n===0)(),I=R(y=>{if(ye(y.currentTarget))return y.preventDefault();e({type:1})}),a=h(()=>({open:n===0}),[n]);return Y({ourProps:{ref:c,id:M,\"aria-hidden\":!0,onClick:I},theirProps:x,slot:a,defaultTag:je,features:Ve,visible:s,name:\"Popover.Overlay\"})}let Je=\"div\",Xe=te.RenderStrategy|te.Static;function Ye(t,o){let u=K(),{id:M=`headlessui-popover-panel-${u}`,focus:x=!1,...n}=t,[e,c]=oe(\"Popover.Panel\"),{close:f,isPortalled:s}=fe(\"Popover.Panel\"),I=`headlessui-focus-sentinel-before-${K()}`,a=`headlessui-focus-sentinel-after-${K()}`,v=J(null),y=j(v,o,i=>{c({type:4,panel:i})}),A=ne(v),P=De();Ae(()=>(c({type:5,panelId:M}),()=>{c({type:5,panelId:null})}),[M,c]);let p=me(),E=(()=>p!==null?(p&V.Open)===V.Open:e.popoverState===0)(),F=R(i=>{var b;switch(i.key){case w.Escape:if(e.popoverState!==0||!v.current||A!=null&&A.activeElement&&!v.current.contains(A.activeElement))return;i.preventDefault(),i.stopPropagation(),c({type:1}),(b=e.button)==null||b.focus();break}});ee(()=>{var i;t.static||e.popoverState===1&&((i=t.unmount)==null||i)&&c({type:4,panel:null})},[e.popoverState,t.unmount,t.static,c]),ee(()=>{if(e.__demoMode||!x||e.popoverState!==0||!v.current)return;let i=A==null?void 0:A.activeElement;v.current.contains(i)||N(v.current,G.First)},[e.__demoMode,x,v,e.popoverState]);let D=h(()=>({open:e.popoverState===0,close:f}),[e,f]),_={ref:y,id:M,onKeyDown:F,onBlur:x&&e.popoverState===0?i=>{var T,d,r,m,g;let b=i.relatedTarget;b&&v.current&&((T=v.current)!=null&&T.contains(b)||(c({type:1}),((r=(d=e.beforePanelSentinel.current)==null?void 0:d.contains)!=null&&r.call(d,b)||(g=(m=e.afterPanelSentinel.current)==null?void 0:m.contains)!=null&&g.call(m,b))&&b.focus({preventScroll:!0})))}:void 0,tabIndex:-1},O=Te(),L=R(()=>{let i=v.current;if(!i)return;function b(){k(O.current,{[H.Forwards]:()=>{var d;N(i,G.First)===pe.Error&&((d=e.afterPanelSentinel.current)==null||d.focus())},[H.Backwards]:()=>{var T;(T=e.button)==null||T.focus({preventScroll:!0})}})}b()}),$=R(()=>{let i=v.current;if(!i)return;function b(){k(O.current,{[H.Forwards]:()=>{var B;if(!e.button)return;let T=se(),d=T.indexOf(e.button),r=T.slice(0,d+1),g=[...T.slice(d+1),...r];for(let l of g.slice())if(l.dataset.headlessuiFocusGuard===\"true\"||(B=e.panel)!=null&&B.contains(l)){let S=g.indexOf(l);S!==-1&&g.splice(S,1)}N(g,G.First,{sorted:!1})},[H.Backwards]:()=>{var d;N(i,G.Previous)===pe.Error&&((d=e.button)==null||d.focus())}})}b()});return C.createElement(re.Provider,{value:M},E&&s&&C.createElement(ae,{id:I,ref:e.beforePanelSentinel,features:le.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:L}),Y({mergeRefs:P,ourProps:_,theirProps:n,slot:D,defaultTag:Je,features:Xe,visible:E,name:\"Popover.Panel\"}),E&&s&&C.createElement(ae,{id:a,ref:e.afterPanelSentinel,features:le.Focusable,\"data-headlessui-focus-guard\":!0,as:\"button\",type:\"button\",onFocus:$}))}let qe=\"div\";function ze(t,o){let u=J(null),M=j(u,o),[x,n]=ce([]),e=Me(),c=R(P=>{n(p=>{let E=p.indexOf(P);if(E!==-1){let F=p.slice();return F.splice(E,1),F}return p})}),f=R(P=>(n(p=>[...p,P]),()=>c(P))),s=R(()=>{var E;let P=Be(u);if(!P)return!1;let p=P.activeElement;return(E=u.current)!=null&&E.contains(p)?!0:x.some(F=>{var D,_;return((D=P.getElementById(F.buttonId.current))==null?void 0:D.contains(p))||((_=P.getElementById(F.panelId.current))==null?void 0:_.contains(p))})}),I=R(P=>{for(let p of x)p.buttonId.current!==P&&p.close()}),a=h(()=>({registerPopover:f,unregisterPopover:c,isFocusWithinPopoverGroup:s,closeOthers:I,mainTreeNodeRef:e.mainTreeNodeRef}),[f,c,s,I,e.mainTreeNodeRef]),v=h(()=>({}),[]),y=t,A={ref:M};return C.createElement(Pe.Provider,{value:a},Y({ourProps:A,theirProps:y,slot:v,defaultTag:qe,name:\"Popover.Group\"}),C.createElement(e.MainTreeNode,null))}let Qe=X(Ue),Ze=X(Ke),et=X($e),tt=X(Ye),ot=X(ze),Ct=Object.assign(Qe,{Button:Ze,Overlay:et,Panel:tt,Group:ot});export{Ct as Popover};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,SAAS,IAAIC,EAAE,EAACC,UAAU,IAAIC,CAAC,EAACC,SAAS,IAAIC,EAAE,EAACC,OAAO,IAAIC,CAAC,EAACC,UAAU,IAAIC,EAAE,EAACC,MAAM,IAAIC,CAAC,EAACC,QAAQ,IAAIC,EAAE,QAAK,OAAO;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,QAAK,mCAAmC;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,QAAK,mCAAmC;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,mBAAmB,IAAIC,EAAE,QAAK,uCAAuC;AAAC,SAAOC,cAAc,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,eAAe,IAAIC,EAAE,QAAK,kCAAkC;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,oBAAoB,IAAIC,EAAE,QAAK,wCAAwC;AAAC,SAAOC,eAAe,IAAIC,EAAE,EAACC,iBAAiB,IAAIC,EAAE,QAAK,oCAAoC;AAAC,SAAOC,WAAW,IAAIC,EAAE,EAACC,WAAW,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,SAAS,IAAIC,CAAC,EAACC,eAAe,IAAIC,EAAE,QAAK,kCAAkC;AAAC,SAAOC,QAAQ,IAAIC,EAAE,EAACC,MAAM,IAAIC,EAAE,QAAK,0BAA0B;AAAC,SAAOC,kBAAkB,IAAIC,EAAE,EAACC,KAAK,IAAIC,CAAC,EAACC,aAAa,IAAIC,EAAE,QAAK,+BAA+B;AAAC,SAAOC,wBAAwB,IAAIC,EAAE,QAAK,qBAAqB;AAAC,SAAOC,KAAK,IAAIC,CAAC,EAACC,aAAa,IAAIC,EAAE,EAACC,OAAO,IAAIC,CAAC,EAACC,WAAW,IAAIC,EAAE,EAACC,oBAAoB,IAAIC,EAAE,EAACC,kBAAkB,IAAIC,EAAE,QAAK,iCAAiC;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,sBAAsB;AAAC,OAAM,2BAA2B;AAAC,SAAOC,gBAAgB,IAAIC,EAAE,QAAK,sBAAsB;AAAC,SAAO3B,QAAQ,IAAI4B,EAAE,EAACC,gBAAgB,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,EAACC,cAAc,IAAIC,EAAE,QAAK,uBAAuB;AAAC,SAAOC,IAAI,IAAIC,CAAC,QAAK,gBAAgB;AAAC,IAAIC,EAAE,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,IAAI,GAAC,CAAC,CAAC,GAAC,MAAM,EAACD,CAAC,CAACA,CAAC,CAACE,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACF,CAAC,CAAC,EAAED,EAAE,IAAE,CAAC,CAAC,CAAC;EAACI,EAAE,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,aAAa,GAAC,CAAC,CAAC,GAAC,eAAe,EAACD,CAAC,CAACA,CAAC,CAACE,YAAY,GAAC,CAAC,CAAC,GAAC,cAAc,EAACF,CAAC,CAACA,CAAC,CAACG,SAAS,GAAC,CAAC,CAAC,GAAC,WAAW,EAACH,CAAC,CAACA,CAAC,CAACI,WAAW,GAAC,CAAC,CAAC,GAAC,aAAa,EAACJ,CAAC,CAACA,CAAC,CAACK,QAAQ,GAAC,CAAC,CAAC,GAAC,UAAU,EAACL,CAAC,CAACA,CAAC,CAACM,UAAU,GAAC,CAAC,CAAC,GAAC,YAAY,EAACN,CAAC,CAAC,EAAED,EAAE,IAAE,CAAC,CAAC,CAAC;AAAC,IAAIQ,EAAE,GAAC;IAAC,CAAC,CAAC,GAAEC,CAAC,IAAE;MAAC,IAAIC,CAAC,GAAC;QAAC,GAAGD,CAAC;QAACE,YAAY,EAAC3B,CAAC,CAACyB,CAAC,CAACE,YAAY,EAAC;UAAC,CAAC,CAAC,GAAE,CAAC;UAAC,CAAC,CAAC,GAAE;QAAC,CAAC;MAAC,CAAC;MAAC,OAAOD,CAAC,CAACC,YAAY,KAAG,CAAC,KAAGD,CAAC,CAACE,UAAU,GAAC,CAAC,CAAC,CAAC,EAACF,CAAC;IAAA,CAAC;IAAC,CAAC,CAAC,EAAED,CAAC,EAAC;MAAC,OAAOA,CAAC,CAACE,YAAY,KAAG,CAAC,GAACF,CAAC,GAAC;QAAC,GAAGA,CAAC;QAACE,YAAY,EAAC;MAAC,CAAC;IAAA,CAAC;IAAC,CAAC,CAAC,EAAEF,CAAC,EAACC,CAAC,EAAC;MAAC,OAAOD,CAAC,CAACI,MAAM,KAAGH,CAAC,CAACG,MAAM,GAACJ,CAAC,GAAC;QAAC,GAAGA,CAAC;QAACI,MAAM,EAACH,CAAC,CAACG;MAAM,CAAC;IAAA,CAAC;IAAC,CAAC,CAAC,EAAEJ,CAAC,EAACC,CAAC,EAAC;MAAC,OAAOD,CAAC,CAACK,QAAQ,KAAGJ,CAAC,CAACI,QAAQ,GAACL,CAAC,GAAC;QAAC,GAAGA,CAAC;QAACK,QAAQ,EAACJ,CAAC,CAACI;MAAQ,CAAC;IAAA,CAAC;IAAC,CAAC,CAAC,EAAEL,CAAC,EAACC,CAAC,EAAC;MAAC,OAAOD,CAAC,CAACM,KAAK,KAAGL,CAAC,CAACK,KAAK,GAACN,CAAC,GAAC;QAAC,GAAGA,CAAC;QAACM,KAAK,EAACL,CAAC,CAACK;MAAK,CAAC;IAAA,CAAC;IAAC,CAAC,CAAC,EAAEN,CAAC,EAACC,CAAC,EAAC;MAAC,OAAOD,CAAC,CAACO,OAAO,KAAGN,CAAC,CAACM,OAAO,GAACP,CAAC,GAAC;QAAC,GAAGA,CAAC;QAACO,OAAO,EAACN,CAAC,CAACM;MAAO,CAAC;IAAA;EAAC,CAAC;EAACC,EAAE,GAACvG,CAAC,CAAC,IAAI,CAAC;AAACuG,EAAE,CAACC,WAAW,GAAC,gBAAgB;AAAC,SAASC,EAAEA,CAACV,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAC5F,CAAC,CAACmG,EAAE,CAAC;EAAC,IAAGP,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIb,CAAC,GAAC,IAAIuB,KAAK,CAAC,IAAIX,CAAC,gDAAgD,CAAC;IAAC,MAAMW,KAAK,CAACC,iBAAiB,IAAED,KAAK,CAACC,iBAAiB,CAACxB,CAAC,EAACsB,EAAE,CAAC,EAACtB,CAAC;EAAA;EAAC,OAAOa,CAAC;AAAA;AAAC,IAAIY,EAAE,GAAC5G,CAAC,CAAC,IAAI,CAAC;AAAC4G,EAAE,CAACJ,WAAW,GAAC,mBAAmB;AAAC,SAASK,EAAEA,CAACd,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAC5F,CAAC,CAACwG,EAAE,CAAC;EAAC,IAAGZ,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIb,CAAC,GAAC,IAAIuB,KAAK,CAAC,IAAIX,CAAC,gDAAgD,CAAC;IAAC,MAAMW,KAAK,CAACC,iBAAiB,IAAED,KAAK,CAACC,iBAAiB,CAACxB,CAAC,EAAC0B,EAAE,CAAC,EAAC1B,CAAC;EAAA;EAAC,OAAOa,CAAC;AAAA;AAAC,IAAIc,EAAE,GAAC9G,CAAC,CAAC,IAAI,CAAC;AAAC8G,EAAE,CAACN,WAAW,GAAC,qBAAqB;AAAC,SAASO,EAAEA,CAAA,EAAE;EAAC,OAAO3G,CAAC,CAAC0G,EAAE,CAAC;AAAA;AAAC,IAAIE,EAAE,GAAChH,CAAC,CAAC,IAAI,CAAC;AAACgH,EAAE,CAACR,WAAW,GAAC,qBAAqB;AAAC,SAASS,EAAEA,CAAA,EAAE;EAAC,OAAO7G,CAAC,CAAC4G,EAAE,CAAC;AAAA;AAAC,SAASE,EAAEA,CAACnB,CAAC,EAACC,CAAC,EAAC;EAAC,OAAO1B,CAAC,CAAC0B,CAAC,CAACmB,IAAI,EAACrB,EAAE,EAACC,CAAC,EAACC,CAAC,CAAC;AAAA;AAAC,IAAIoB,EAAE,GAAC,KAAK;AAAC,SAASC,EAAEA,CAACtB,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIsB,CAAC;EAAC,IAAG;MAACpB,UAAU,EAACf,CAAC,GAAC,CAAC,CAAC;MAAC,GAAGoC;IAAC,CAAC,GAACxB,CAAC;IAACyB,CAAC,GAAC5G,CAAC,CAAC,IAAI,CAAC;IAAC6G,CAAC,GAACjF,CAAC,CAACwD,CAAC,EAAC1D,EAAE,CAACoF,CAAC,IAAE;MAACF,CAAC,CAACG,OAAO,GAACD,CAAC;IAAA,CAAC,CAAC,CAAC;IAACnC,CAAC,GAAC3E,CAAC,CAAC,EAAE,CAAC;IAACgH,CAAC,GAAClH,EAAE,CAACwG,EAAE,EAAC;MAAChB,UAAU,EAACf,CAAC;MAACc,YAAY,EAACd,CAAC,GAAC,CAAC,GAAC,CAAC;MAAC0C,OAAO,EAACtC,CAAC;MAACY,MAAM,EAAC,IAAI;MAACC,QAAQ,EAAC,IAAI;MAACC,KAAK,EAAC,IAAI;MAACC,OAAO,EAAC,IAAI;MAACwB,mBAAmB,EAAC5H,EAAE,CAAC,CAAC;MAAC6H,kBAAkB,EAAC7H,EAAE,CAAC;IAAC,CAAC,CAAC;IAAC,CAAC;MAAC+F,YAAY,EAAC+B,CAAC;MAAC7B,MAAM,EAAC8B,CAAC;MAAC7B,QAAQ,EAAC8B,CAAC;MAAC7B,KAAK,EAAC8B,CAAC;MAAC7B,OAAO,EAAC8B,CAAC;MAACN,mBAAmB,EAACO,CAAC;MAACN,kBAAkB,EAACO;IAAC,CAAC,EAACC,CAAC,CAAC,GAACX,CAAC;IAACY,CAAC,GAAC1G,EAAE,CAAC,CAACwF,CAAC,GAACE,CAAC,CAACG,OAAO,KAAG,IAAI,GAACL,CAAC,GAACW,CAAC,CAAC;IAACQ,CAAC,GAACjI,CAAC,CAAC,MAAI;MAAC,IAAG,CAACyH,CAAC,IAAE,CAACE,CAAC,EAAC,OAAM,CAAC,CAAC;MAAC,KAAI,IAAIO,CAAC,IAAIC,QAAQ,CAACC,gBAAgB,CAAC,UAAU,CAAC,EAAC,IAAGC,MAAM,CAACH,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACI,QAAQ,CAACb,CAAC,CAAC,CAAC,GAACY,MAAM,CAACH,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACI,QAAQ,CAACX,CAAC,CAAC,CAAC,EAAC,OAAM,CAAC,CAAC;MAAC,IAAIT,CAAC,GAACxD,EAAE,CAAC,CAAC;QAAC6E,CAAC,GAACrB,CAAC,CAACsB,OAAO,CAACf,CAAC,CAAC;QAACgB,CAAC,GAAC,CAACF,CAAC,GAACrB,CAAC,CAACwB,MAAM,GAAC,CAAC,IAAExB,CAAC,CAACwB,MAAM;QAACC,CAAC,GAAC,CAACJ,CAAC,GAAC,CAAC,IAAErB,CAAC,CAACwB,MAAM;QAACE,CAAC,GAAC1B,CAAC,CAACuB,CAAC,CAAC;QAACI,EAAE,GAAC3B,CAAC,CAACyB,CAAC,CAAC;MAAC,OAAM,CAAChB,CAAC,CAACW,QAAQ,CAACM,CAAC,CAAC,IAAE,CAACjB,CAAC,CAACW,QAAQ,CAACO,EAAE,CAAC;IAAA,CAAC,EAAC,CAACpB,CAAC,EAACE,CAAC,CAAC,CAAC;IAACmB,CAAC,GAAC5H,EAAE,CAACwG,CAAC,CAAC;IAACqB,CAAC,GAAC7H,EAAE,CAAC0G,CAAC,CAAC;IAACoB,CAAC,GAAChJ,CAAC,CAAC,OAAK;MAAC4F,QAAQ,EAACkD,CAAC;MAAChD,OAAO,EAACiD,CAAC;MAACE,KAAK,EAACA,CAAA,KAAIlB,CAAC,CAAC;QAACpB,IAAI,EAAC;MAAC,CAAC;IAAC,CAAC,CAAC,EAAC,CAACmC,CAAC,EAACC,CAAC,EAAChB,CAAC,CAAC,CAAC;IAACmB,CAAC,GAAC3C,EAAE,CAAC,CAAC;IAAC4C,CAAC,GAACD,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACE,eAAe;IAACC,CAAC,GAAC3I,CAAC,CAAC,MAAI;MAAC,IAAIwG,CAAC;MAAC,OAAM,CAACA,CAAC,GAACgC,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACI,yBAAyB,CAAC,CAAC,KAAG,IAAI,GAACpC,CAAC,GAAC,CAACc,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACuB,aAAa,MAAI,CAAC9B,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACa,QAAQ,CAACN,CAAC,CAACuB,aAAa,CAAC,MAAI5B,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACW,QAAQ,CAACN,CAAC,CAACuB,aAAa,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;EAACzJ,EAAE,CAAC,MAAIqJ,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACH,CAAC,CAAC,EAAC,CAACG,CAAC,EAACH,CAAC,CAAC,CAAC;EAAC,IAAG,CAACQ,CAAC,EAACC,CAAC,CAAC,GAACjJ,EAAE,CAAC,CAAC;IAACkJ,CAAC,GAAC9H,EAAE,CAAC;MAAC+H,eAAe,EAACT,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACS,eAAe;MAACC,OAAO,EAACJ,CAAC;MAACK,iBAAiB,EAAC,CAACpC,CAAC,EAACE,CAAC;IAAC,CAAC,CAAC;EAAC/G,EAAE,CAACoH,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAC8B,WAAW,EAAC,OAAO,EAAC5C,CAAC,IAAE;IAAC,IAAIqB,CAAC,EAACE,CAAC,EAACE,CAAC,EAACC,CAAC;IAAC1B,CAAC,CAAC6C,MAAM,KAAGC,MAAM,IAAE9C,CAAC,CAAC6C,MAAM,YAAYE,WAAW,IAAEzC,CAAC,KAAG,CAAC,KAAG6B,CAAC,CAAC,CAAC,IAAE5B,CAAC,IAAEE,CAAC,KAAG+B,CAAC,CAACpB,QAAQ,CAACpB,CAAC,CAAC6C,MAAM,CAAC,IAAE,CAACtB,CAAC,GAAC,CAACF,CAAC,GAACV,CAAC,CAACV,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACoB,CAAC,CAACD,QAAQ,KAAG,IAAI,IAAEG,CAAC,CAACyB,IAAI,CAAC3B,CAAC,EAACrB,CAAC,CAAC6C,MAAM,CAAC,IAAE,CAACnB,CAAC,GAAC,CAACD,CAAC,GAACb,CAAC,CAACX,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACwB,CAAC,CAACL,QAAQ,KAAG,IAAI,IAAEM,CAAC,CAACsB,IAAI,CAACvB,CAAC,EAACzB,CAAC,CAAC6C,MAAM,CAAC,IAAEhC,CAAC,CAAC;MAACpB,IAAI,EAAC;IAAC,CAAC,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAAC,CAAC,CAAC,EAACvF,EAAE,CAACsI,CAAC,CAACS,iBAAiB,EAAC,CAACjD,CAAC,EAACqB,CAAC,KAAG;IAACR,CAAC,CAAC;MAACpB,IAAI,EAAC;IAAC,CAAC,CAAC,EAAC/C,EAAE,CAAC2E,CAAC,EAACnF,EAAE,CAACgH,KAAK,CAAC,KAAGlD,CAAC,CAACmD,cAAc,CAAC,CAAC,EAAC5C,CAAC,IAAE,IAAI,IAAEA,CAAC,CAAC6C,KAAK,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC9C,CAAC,KAAG,CAAC,CAAC;EAAC,IAAI+C,CAAC,GAAC7J,CAAC,CAACwG,CAAC,IAAE;MAACa,CAAC,CAAC;QAACpB,IAAI,EAAC;MAAC,CAAC,CAAC;MAAC,IAAI4B,CAAC,GAAC,CAAC,MAAIrB,CAAC,GAACA,CAAC,YAAY+C,WAAW,GAAC/C,CAAC,GAAC,SAAS,IAAGA,CAAC,IAAEA,CAAC,CAACC,OAAO,YAAY8C,WAAW,GAAC/C,CAAC,CAACC,OAAO,GAACM,CAAC,GAACA,CAAC,EAAE,CAAC;MAACc,CAAC,IAAE,IAAI,IAAEA,CAAC,CAAC+B,KAAK,CAAC,CAAC;IAAA,CAAC,CAAC;IAACE,CAAC,GAACxK,CAAC,CAAC,OAAK;MAACiJ,KAAK,EAACsB,CAAC;MAACE,WAAW,EAACxC;IAAC,CAAC,CAAC,EAAC,CAACsC,CAAC,EAACtC,CAAC,CAAC,CAAC;IAACyC,CAAC,GAAC1K,CAAC,CAAC,OAAK;MAAC2K,IAAI,EAACnD,CAAC,KAAG,CAAC;MAACyB,KAAK,EAACsB;IAAC,CAAC,CAAC,EAAC,CAAC/C,CAAC,EAAC+C,CAAC,CAAC,CAAC;IAACK,CAAC,GAAC;MAACC,GAAG,EAAC5D;IAAC,CAAC;EAAC,OAAO3H,CAAC,CAACwL,aAAa,CAACtE,EAAE,CAACuE,QAAQ,EAAC;IAACC,KAAK,EAAC;EAAI,CAAC,EAAC1L,CAAC,CAACwL,aAAa,CAAC/E,EAAE,CAACgF,QAAQ,EAAC;IAACC,KAAK,EAAC5D;EAAC,CAAC,EAAC9H,CAAC,CAACwL,aAAa,CAAC1E,EAAE,CAAC2E,QAAQ,EAAC;IAACC,KAAK,EAACR;EAAC,CAAC,EAAClL,CAAC,CAACwL,aAAa,CAACpI,EAAE,EAAC;IAACsI,KAAK,EAAClH,CAAC,CAAC0D,CAAC,EAAC;MAAC,CAAC,CAAC,GAAE5E,CAAC,CAACgC,IAAI;MAAC,CAAC,CAAC,GAAEhC,CAAC,CAACiC;IAAM,CAAC;EAAC,CAAC,EAACvF,CAAC,CAACwL,aAAa,CAACrB,CAAC,EAAC,IAAI,EAACpF,CAAC,CAAC;IAAC4G,QAAQ,EAACL,CAAC;IAACM,UAAU,EAACnE,CAAC;IAACoE,IAAI,EAACT,CAAC;IAACU,UAAU,EAACxE,EAAE;IAACyE,IAAI,EAAC;EAAS,CAAC,CAAC,EAAC/L,CAAC,CAACwL,aAAa,CAACpB,CAAC,CAAC4B,YAAY,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIC,EAAE,GAAC,QAAQ;AAAC,SAASC,EAAEA,CAACjG,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIb,CAAC,GAAC7D,CAAC,CAAC,CAAC;IAAC;MAAC2K,EAAE,EAAC1E,CAAC,GAAC,6BAA6BpC,CAAC,EAAE;MAAC,GAAGqC;IAAC,CAAC,GAACzB,CAAC;IAAC,CAAC0B,CAAC,EAAClC,CAAC,CAAC,GAACkB,EAAE,CAAC,gBAAgB,CAAC;IAAC;MAACwE,WAAW,EAACrD;IAAC,CAAC,GAACf,EAAE,CAAC,gBAAgB,CAAC;IAACmB,CAAC,GAACpH,CAAC,CAAC,IAAI,CAAC;IAACqH,CAAC,GAAC,6BAA6B3G,CAAC,CAAC,CAAC,EAAE;IAAC4G,CAAC,GAACnB,EAAE,CAAC,CAAC;IAACoB,CAAC,GAACD,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACgE,WAAW;IAAC7D,CAAC,GAACpB,EAAE,CAAC,CAAC,KAAG,IAAI;EAAC3G,EAAE,CAAC,MAAI;IAAC,IAAG,CAAC+H,CAAC,EAAC,OAAO9C,CAAC,CAAC;MAAC4B,IAAI,EAAC,CAAC;MAACf,QAAQ,EAACmB;IAAC,CAAC,CAAC,EAAC,MAAI;MAAChC,CAAC,CAAC;QAAC4B,IAAI,EAAC,CAAC;QAACf,QAAQ,EAAC;MAAI,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC,EAAC,CAACiC,CAAC,EAACd,CAAC,EAAChC,CAAC,CAAC,CAAC;EAAC,IAAG,CAAC+C,CAAC,CAAC,GAACxH,EAAE,CAAC,MAAIqL,MAAM,CAAC,CAAC,CAAC;IAAC5D,CAAC,GAAC/F,CAAC,CAACwF,CAAC,EAAChC,CAAC,EAACqC,CAAC,GAAC,IAAI,GAAC2C,CAAC,IAAE;MAAC,IAAGA,CAAC,EAACvD,CAAC,CAACI,OAAO,CAACF,OAAO,CAACyE,IAAI,CAAC9D,CAAC,CAAC,CAAC,KAAI;QAAC,IAAI4C,CAAC,GAACzD,CAAC,CAACI,OAAO,CAACF,OAAO,CAACqB,OAAO,CAACV,CAAC,CAAC;QAAC4C,CAAC,KAAG,CAAC,CAAC,IAAEzD,CAAC,CAACI,OAAO,CAACF,OAAO,CAAC0E,MAAM,CAACnB,CAAC,EAAC,CAAC,CAAC;MAAA;MAACzD,CAAC,CAACI,OAAO,CAACF,OAAO,CAACuB,MAAM,GAAC,CAAC,IAAEoD,OAAO,CAACC,IAAI,CAAC,wFAAwF,CAAC,EAACvB,CAAC,IAAEzF,CAAC,CAAC;QAAC4B,IAAI,EAAC,CAAC;QAAChB,MAAM,EAAC6E;MAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACxC,CAAC,GAAChG,CAAC,CAACwF,CAAC,EAAChC,CAAC,CAAC;IAACyC,CAAC,GAAC3G,EAAE,CAACkG,CAAC,CAAC;IAACsB,CAAC,GAACpI,CAAC,CAAC8J,CAAC,IAAE;MAAC,IAAIE,CAAC,EAACE,CAAC,EAAC9D,CAAC;MAAC,IAAGe,CAAC,EAAC;QAAC,IAAGZ,CAAC,CAACxB,YAAY,KAAG,CAAC,EAAC;QAAO,QAAO+E,CAAC,CAACwB,GAAG;UAAE,KAAKvH,CAAC,CAACwH,KAAK;UAAC,KAAKxH,CAAC,CAACyH,KAAK;YAAC1B,CAAC,CAACH,cAAc,CAAC,CAAC,EAAC,CAACO,CAAC,GAAC,CAACF,CAAC,GAACF,CAAC,CAACT,MAAM,EAAEoC,KAAK,KAAG,IAAI,IAAEvB,CAAC,CAACV,IAAI,CAACQ,CAAC,CAAC,EAAC3F,CAAC,CAAC;cAAC4B,IAAI,EAAC;YAAC,CAAC,CAAC,EAAC,CAACG,CAAC,GAACG,CAAC,CAACtB,MAAM,KAAG,IAAI,IAAEmB,CAAC,CAACwD,KAAK,CAAC,CAAC;YAAC;QAAK;MAAC,CAAC,MAAK,QAAOE,CAAC,CAACwB,GAAG;QAAE,KAAKvH,CAAC,CAACwH,KAAK;QAAC,KAAKxH,CAAC,CAACyH,KAAK;UAAC1B,CAAC,CAACH,cAAc,CAAC,CAAC,EAACG,CAAC,CAAC4B,eAAe,CAAC,CAAC,EAACnF,CAAC,CAACxB,YAAY,KAAG,CAAC,KAAGkC,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACV,CAAC,CAACrB,QAAQ,CAAC,CAAC,EAACb,CAAC,CAAC;YAAC4B,IAAI,EAAC;UAAC,CAAC,CAAC;UAAC;QAAM,KAAKlC,CAAC,CAAC4H,MAAM;UAAC,IAAGpF,CAAC,CAACxB,YAAY,KAAG,CAAC,EAAC,OAAOkC,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACV,CAAC,CAACrB,QAAQ,CAAC;UAAC,IAAG,CAAC4B,CAAC,CAACL,OAAO,IAAEc,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACsB,aAAa,IAAE,CAAC/B,CAAC,CAACL,OAAO,CAACmB,QAAQ,CAACL,CAAC,CAACsB,aAAa,CAAC,EAAC;UAAOiB,CAAC,CAACH,cAAc,CAAC,CAAC,EAACG,CAAC,CAAC4B,eAAe,CAAC,CAAC,EAACrH,CAAC,CAAC;YAAC4B,IAAI,EAAC;UAAC,CAAC,CAAC;UAAC;MAAK;IAAC,CAAC,CAAC;IAACoC,CAAC,GAACrI,CAAC,CAAC8J,CAAC,IAAE;MAAC3C,CAAC,IAAE2C,CAAC,CAACwB,GAAG,KAAGvH,CAAC,CAACwH,KAAK,IAAEzB,CAAC,CAACH,cAAc,CAAC,CAAC;IAAA,CAAC,CAAC;IAACrB,CAAC,GAACtI,CAAC,CAAC8J,CAAC,IAAE;MAAC,IAAIE,CAAC,EAACE,CAAC;MAAC5H,EAAE,CAACwH,CAAC,CAAC8B,aAAa,CAAC,IAAE/G,CAAC,CAACgH,QAAQ,KAAG1E,CAAC,IAAE9C,CAAC,CAAC;QAAC4B,IAAI,EAAC;MAAC,CAAC,CAAC,EAAC,CAAC+D,CAAC,GAACzD,CAAC,CAACtB,MAAM,KAAG,IAAI,IAAE+E,CAAC,CAACJ,KAAK,CAAC,CAAC,KAAGE,CAAC,CAACH,cAAc,CAAC,CAAC,EAACG,CAAC,CAAC4B,eAAe,CAAC,CAAC,EAACnF,CAAC,CAACxB,YAAY,KAAG,CAAC,KAAGkC,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACV,CAAC,CAACrB,QAAQ,CAAC,CAAC,EAACb,CAAC,CAAC;QAAC4B,IAAI,EAAC;MAAC,CAAC,CAAC,EAAC,CAACiE,CAAC,GAAC3D,CAAC,CAACtB,MAAM,KAAG,IAAI,IAAEiF,CAAC,CAACN,KAAK,CAAC,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACpB,CAAC,GAACxI,CAAC,CAAC8J,CAAC,IAAE;MAACA,CAAC,CAACH,cAAc,CAAC,CAAC,EAACG,CAAC,CAAC4B,eAAe,CAAC,CAAC;IAAA,CAAC,CAAC;IAACjD,CAAC,GAAClC,CAAC,CAACxB,YAAY,KAAG,CAAC;IAAC4D,CAAC,GAACrJ,CAAC,CAAC,OAAK;MAAC2K,IAAI,EAACxB;IAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;IAACK,CAAC,GAAChI,EAAE,CAAC+D,CAAC,EAACiC,CAAC,CAAC;IAACiC,CAAC,GAAC5B,CAAC,GAAC;MAACgD,GAAG,EAAC7C,CAAC;MAACrB,IAAI,EAAC6C,CAAC;MAACgD,SAAS,EAAC1D,CAAC;MAAC2D,OAAO,EAACzD;IAAC,CAAC,GAAC;MAAC6B,GAAG,EAAC9C,CAAC;MAAC0D,EAAE,EAACxE,CAAC,CAACrB,QAAQ;MAACe,IAAI,EAAC6C,CAAC;MAAC,eAAe,EAACvC,CAAC,CAACxB,YAAY,KAAG,CAAC;MAAC,eAAe,EAACwB,CAAC,CAACpB,KAAK,GAACoB,CAAC,CAACnB,OAAO,GAAC,KAAK,CAAC;MAAC0G,SAAS,EAAC1D,CAAC;MAAC4D,OAAO,EAAC3D,CAAC;MAAC0D,OAAO,EAACzD,CAAC;MAAC2D,WAAW,EAACzD;IAAC,CAAC;IAACQ,CAAC,GAACtH,EAAE,CAAC,CAAC;IAACmI,CAAC,GAAC7J,CAAC,CAAC,MAAI;MAAC,IAAI8J,CAAC,GAACvD,CAAC,CAACpB,KAAK;MAAC,IAAG,CAAC2E,CAAC,EAAC;MAAO,SAASE,CAACA,CAAA,EAAE;QAAC5G,CAAC,CAAC4F,CAAC,CAACvC,OAAO,EAAC;UAAC,CAACjF,CAAC,CAAC0K,QAAQ,GAAE,MAAItJ,CAAC,CAACkH,CAAC,EAACtH,CAAC,CAAC2J,KAAK,CAAC;UAAC,CAAC3K,CAAC,CAAC4K,SAAS,GAAE,MAAIxJ,CAAC,CAACkH,CAAC,EAACtH,CAAC,CAAC6J,IAAI;QAAC,CAAC,CAAC,KAAGvJ,EAAE,CAAC0C,KAAK,IAAE5C,CAAC,CAACI,EAAE,CAAC,CAAC,CAACsJ,MAAM,CAAClG,CAAC,IAAEA,CAAC,CAACmG,OAAO,CAACC,oBAAoB,KAAG,MAAM,CAAC,EAACpJ,CAAC,CAAC4F,CAAC,CAACvC,OAAO,EAAC;UAAC,CAACjF,CAAC,CAAC0K,QAAQ,GAAE1J,CAAC,CAACiK,IAAI;UAAC,CAACjL,CAAC,CAAC4K,SAAS,GAAE5J,CAAC,CAACkK;QAAQ,CAAC,CAAC,EAAC;UAACC,UAAU,EAACpG,CAAC,CAACtB;QAAM,CAAC,CAAC;MAAA;MAAC+E,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;EAAC,OAAOpL,CAAC,CAACwL,aAAa,CAACxL,CAAC,CAACgO,QAAQ,EAAC,IAAI,EAACjJ,CAAC,CAAC;IAAC4G,QAAQ,EAACxB,CAAC;IAACyB,UAAU,EAAClE,CAAC;IAACmE,IAAI,EAAC9B,CAAC;IAAC+B,UAAU,EAACG,EAAE;IAACF,IAAI,EAAC;EAAgB,CAAC,CAAC,EAAClC,CAAC,IAAE,CAACtB,CAAC,IAAET,CAAC,IAAE9H,CAAC,CAACwL,aAAa,CAACtI,EAAE,EAAC;IAACiJ,EAAE,EAAChE,CAAC;IAAC8F,QAAQ,EAACjL,EAAE,CAACkL,SAAS;IAAC,6BAA6B,EAAC,CAAC,CAAC;IAACC,EAAE,EAAC,QAAQ;IAAC9G,IAAI,EAAC,QAAQ;IAAC+G,OAAO,EAACnD;EAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIoD,EAAE,GAAC,KAAK;EAACC,EAAE,GAAC3J,EAAE,CAAC4J,cAAc,GAAC5J,EAAE,CAAC6J,MAAM;AAAC,SAASC,EAAEA,CAACxI,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIb,CAAC,GAAC7D,CAAC,CAAC,CAAC;IAAC;MAAC2K,EAAE,EAAC1E,CAAC,GAAC,8BAA8BpC,CAAC,EAAE;MAAC,GAAGqC;IAAC,CAAC,GAACzB,CAAC;IAAC,CAAC;MAACE,YAAY,EAACwB;IAAC,CAAC,EAAClC,CAAC,CAAC,GAACkB,EAAE,CAAC,iBAAiB,CAAC;IAACmB,CAAC,GAACpF,CAAC,CAACwD,CAAC,CAAC;IAACgC,CAAC,GAAC1E,EAAE,CAAC,CAAC;IAAC2E,CAAC,GAAC,CAAC,MAAID,CAAC,KAAG,IAAI,GAAC,CAACA,CAAC,GAAC5E,CAAC,CAACgC,IAAI,MAAIhC,CAAC,CAACgC,IAAI,GAACqC,CAAC,KAAG,CAAC,EAAE,CAAC;IAACS,CAAC,GAAChH,CAAC,CAACmH,CAAC,IAAE;MAAC,IAAG7E,EAAE,CAAC6E,CAAC,CAACyE,aAAa,CAAC,EAAC,OAAOzE,CAAC,CAACwC,cAAc,CAAC,CAAC;MAACtF,CAAC,CAAC;QAAC4B,IAAI,EAAC;MAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACgB,CAAC,GAAC3H,CAAC,CAAC,OAAK;MAAC2K,IAAI,EAAC1D,CAAC,KAAG;IAAC,CAAC,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;EAAC,OAAO5C,CAAC,CAAC;IAAC4G,QAAQ,EAAC;MAACJ,GAAG,EAACzD,CAAC;MAACqE,EAAE,EAAC1E,CAAC;MAAC,aAAa,EAAC,CAAC,CAAC;MAAC0F,OAAO,EAAC/E;IAAC,CAAC;IAACwD,UAAU,EAAClE,CAAC;IAACmE,IAAI,EAACxD,CAAC;IAACyD,UAAU,EAACuC,EAAE;IAACJ,QAAQ,EAACK,EAAE;IAACI,OAAO,EAACvG,CAAC;IAAC4D,IAAI,EAAC;EAAiB,CAAC,CAAC;AAAA;AAAC,IAAI4C,EAAE,GAAC,KAAK;EAACC,EAAE,GAACjK,EAAE,CAAC4J,cAAc,GAAC5J,EAAE,CAAC6J,MAAM;AAAC,SAASK,EAAEA,CAAC5I,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIb,CAAC,GAAC7D,CAAC,CAAC,CAAC;IAAC;MAAC2K,EAAE,EAAC1E,CAAC,GAAC,4BAA4BpC,CAAC,EAAE;MAAC2F,KAAK,EAACtD,CAAC,GAAC,CAAC,CAAC;MAAC,GAAGC;IAAC,CAAC,GAAC1B,CAAC;IAAC,CAACR,CAAC,EAACqC,CAAC,CAAC,GAACnB,EAAE,CAAC,eAAe,CAAC;IAAC;MAACgD,KAAK,EAACzB,CAAC;MAACiD,WAAW,EAAChD;IAAC,CAAC,GAACpB,EAAE,CAAC,eAAe,CAAC;IAACqB,CAAC,GAAC,oCAAoC5G,CAAC,CAAC,CAAC,EAAE;IAAC6G,CAAC,GAAC,mCAAmC7G,CAAC,CAAC,CAAC,EAAE;IAAC8G,CAAC,GAACxH,CAAC,CAAC,IAAI,CAAC;IAACyH,CAAC,GAAC7F,CAAC,CAAC4F,CAAC,EAACpC,CAAC,EAACgE,CAAC,IAAE;MAACpC,CAAC,CAAC;QAACT,IAAI,EAAC,CAAC;QAACd,KAAK,EAAC2D;MAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAAC1B,CAAC,GAACxG,EAAE,CAACsG,CAAC,CAAC;IAACG,CAAC,GAACxD,EAAE,CAAC,CAAC;EAACvD,EAAE,CAAC,OAAKoG,CAAC,CAAC;IAACT,IAAI,EAAC,CAAC;IAACb,OAAO,EAACiB;EAAC,CAAC,CAAC,EAAC,MAAI;IAACK,CAAC,CAAC;MAACT,IAAI,EAAC,CAAC;MAACb,OAAO,EAAC;IAAI,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,CAACiB,CAAC,EAACK,CAAC,CAAC,CAAC;EAAC,IAAIY,CAAC,GAAClF,EAAE,CAAC,CAAC;IAACmF,CAAC,GAAC,CAAC,MAAID,CAAC,KAAG,IAAI,GAAC,CAACA,CAAC,GAACpF,CAAC,CAACgC,IAAI,MAAIhC,CAAC,CAACgC,IAAI,GAACG,CAAC,CAACU,YAAY,KAAG,CAAC,EAAE,CAAC;IAACqD,CAAC,GAACpI,CAAC,CAAC8I,CAAC,IAAE;MAAC,IAAIC,CAAC;MAAC,QAAOD,CAAC,CAACwC,GAAG;QAAE,KAAKvH,CAAC,CAAC4H,MAAM;UAAC,IAAGtH,CAAC,CAACU,YAAY,KAAG,CAAC,IAAE,CAACmC,CAAC,CAACT,OAAO,IAAEW,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACyB,aAAa,IAAE,CAAC3B,CAAC,CAACT,OAAO,CAACmB,QAAQ,CAACR,CAAC,CAACyB,aAAa,CAAC,EAAC;UAAOC,CAAC,CAACa,cAAc,CAAC,CAAC,EAACb,CAAC,CAAC4C,eAAe,CAAC,CAAC,EAAChF,CAAC,CAAC;YAACT,IAAI,EAAC;UAAC,CAAC,CAAC,EAAC,CAAC8C,CAAC,GAAC1E,CAAC,CAACY,MAAM,KAAG,IAAI,IAAE8D,CAAC,CAACa,KAAK,CAAC,CAAC;UAAC;MAAK;IAAC,CAAC,CAAC;EAACxK,EAAE,CAAC,MAAI;IAAC,IAAI0J,CAAC;IAACjE,CAAC,CAAC6I,MAAM,IAAErJ,CAAC,CAACU,YAAY,KAAG,CAAC,KAAG,CAAC+D,CAAC,GAACjE,CAAC,CAAC8I,OAAO,KAAG,IAAI,IAAE7E,CAAC,CAAC,IAAEpC,CAAC,CAAC;MAACT,IAAI,EAAC,CAAC;MAACd,KAAK,EAAC;IAAI,CAAC,CAAC;EAAA,CAAC,EAAC,CAACd,CAAC,CAACU,YAAY,EAACF,CAAC,CAAC8I,OAAO,EAAC9I,CAAC,CAAC6I,MAAM,EAAChH,CAAC,CAAC,CAAC,EAACtH,EAAE,CAAC,MAAI;IAAC,IAAGiF,CAAC,CAACW,UAAU,IAAE,CAACsB,CAAC,IAAEjC,CAAC,CAACU,YAAY,KAAG,CAAC,IAAE,CAACmC,CAAC,CAACT,OAAO,EAAC;IAAO,IAAIqC,CAAC,GAAC1B,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACyB,aAAa;IAAC3B,CAAC,CAACT,OAAO,CAACmB,QAAQ,CAACkB,CAAC,CAAC,IAAElG,CAAC,CAACsE,CAAC,CAACT,OAAO,EAACjE,CAAC,CAAC2J,KAAK,CAAC;EAAA,CAAC,EAAC,CAAC9H,CAAC,CAACW,UAAU,EAACsB,CAAC,EAACY,CAAC,EAAC7C,CAAC,CAACU,YAAY,CAAC,CAAC;EAAC,IAAIsD,CAAC,GAAC/I,CAAC,CAAC,OAAK;MAAC2K,IAAI,EAAC5F,CAAC,CAACU,YAAY,KAAG,CAAC;MAACwD,KAAK,EAACzB;IAAC,CAAC,CAAC,EAAC,CAACzC,CAAC,EAACyC,CAAC,CAAC,CAAC;IAACwB,CAAC,GAAC;MAAC6B,GAAG,EAAChD,CAAC;MAAC4D,EAAE,EAAC1E,CAAC;MAACyF,SAAS,EAAC1D,CAAC;MAACwF,MAAM,EAACtH,CAAC,IAAEjC,CAAC,CAACU,YAAY,KAAG,CAAC,GAAC+D,CAAC,IAAE;QAAC,IAAIE,CAAC,EAACa,CAAC,EAACC,CAAC,EAACE,CAAC,EAACE,CAAC;QAAC,IAAInB,CAAC,GAACD,CAAC,CAAC+E,aAAa;QAAC9E,CAAC,IAAE7B,CAAC,CAACT,OAAO,KAAG,CAACuC,CAAC,GAAC9B,CAAC,CAACT,OAAO,KAAG,IAAI,IAAEuC,CAAC,CAACpB,QAAQ,CAACmB,CAAC,CAAC,KAAGrC,CAAC,CAAC;UAACT,IAAI,EAAC;QAAC,CAAC,CAAC,EAAC,CAAC,CAAC6D,CAAC,GAAC,CAACD,CAAC,GAACxF,CAAC,CAACuC,mBAAmB,CAACH,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACoD,CAAC,CAACjC,QAAQ,KAAG,IAAI,IAAEkC,CAAC,CAACN,IAAI,CAACK,CAAC,EAACd,CAAC,CAAC,IAAE,CAACmB,CAAC,GAAC,CAACF,CAAC,GAAC3F,CAAC,CAACwC,kBAAkB,CAACJ,OAAO,KAAG,IAAI,GAAC,KAAK,CAAC,GAACuD,CAAC,CAACpC,QAAQ,KAAG,IAAI,IAAEsC,CAAC,CAACV,IAAI,CAACQ,CAAC,EAACjB,CAAC,CAAC,KAAGA,CAAC,CAACa,KAAK,CAAC;UAACkE,aAAa,EAAC,CAAC;QAAC,CAAC,CAAC,CAAC,CAAC;MAAA,CAAC,GAAC,KAAK,CAAC;MAACC,QAAQ,EAAC,CAAC;IAAC,CAAC;IAACvF,CAAC,GAAC9G,EAAE,CAAC,CAAC;IAAC+G,CAAC,GAACzI,CAAC,CAAC,MAAI;MAAC,IAAI8I,CAAC,GAAC5B,CAAC,CAACT,OAAO;MAAC,IAAG,CAACqC,CAAC,EAAC;MAAO,SAASC,CAACA,CAAA,EAAE;QAAC3F,CAAC,CAACoF,CAAC,CAAC/B,OAAO,EAAC;UAAC,CAACjF,CAAC,CAAC0K,QAAQ,GAAE,MAAI;YAAC,IAAIrC,CAAC;YAACjH,CAAC,CAACkG,CAAC,EAACtG,CAAC,CAAC2J,KAAK,CAAC,KAAGrJ,EAAE,CAAC0C,KAAK,KAAG,CAACqE,CAAC,GAACxF,CAAC,CAACwC,kBAAkB,CAACJ,OAAO,KAAG,IAAI,IAAEoD,CAAC,CAACD,KAAK,CAAC,CAAC,CAAC;UAAA,CAAC;UAAC,CAACpI,CAAC,CAAC4K,SAAS,GAAE,MAAI;YAAC,IAAIpD,CAAC;YAAC,CAACA,CAAC,GAAC3E,CAAC,CAACY,MAAM,KAAG,IAAI,IAAE+D,CAAC,CAACY,KAAK,CAAC;cAACkE,aAAa,EAAC,CAAC;YAAC,CAAC,CAAC;UAAA;QAAC,CAAC,CAAC;MAAA;MAAC/E,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;IAACJ,CAAC,GAAC3I,CAAC,CAAC,MAAI;MAAC,IAAI8I,CAAC,GAAC5B,CAAC,CAACT,OAAO;MAAC,IAAG,CAACqC,CAAC,EAAC;MAAO,SAASC,CAACA,CAAA,EAAE;QAAC3F,CAAC,CAACoF,CAAC,CAAC/B,OAAO,EAAC;UAAC,CAACjF,CAAC,CAAC0K,QAAQ,GAAE,MAAI;YAAC,IAAI9F,CAAC;YAAC,IAAG,CAAC/B,CAAC,CAACY,MAAM,EAAC;YAAO,IAAI+D,CAAC,GAAChG,EAAE,CAAC,CAAC;cAAC6G,CAAC,GAACb,CAAC,CAAClB,OAAO,CAACzD,CAAC,CAACY,MAAM,CAAC;cAAC6E,CAAC,GAACd,CAAC,CAACgF,KAAK,CAAC,CAAC,EAACnE,CAAC,GAAC,CAAC,CAAC;cAACK,CAAC,GAAC,CAAC,GAAGlB,CAAC,CAACgF,KAAK,CAACnE,CAAC,GAAC,CAAC,CAAC,EAAC,GAAGC,CAAC,CAAC;YAAC,KAAI,IAAItD,CAAC,IAAI0D,CAAC,CAAC8D,KAAK,CAAC,CAAC,EAAC,IAAGxH,CAAC,CAAC+F,OAAO,CAACC,oBAAoB,KAAG,MAAM,IAAE,CAACpG,CAAC,GAAC/B,CAAC,CAACc,KAAK,KAAG,IAAI,IAAEiB,CAAC,CAACwB,QAAQ,CAACpB,CAAC,CAAC,EAAC;cAAC,IAAIqB,CAAC,GAACqC,CAAC,CAACpC,OAAO,CAACtB,CAAC,CAAC;cAACqB,CAAC,KAAG,CAAC,CAAC,IAAEqC,CAAC,CAACiB,MAAM,CAACtD,CAAC,EAAC,CAAC,CAAC;YAAA;YAACjF,CAAC,CAACsH,CAAC,EAAC1H,CAAC,CAAC2J,KAAK,EAAC;cAAC8B,MAAM,EAAC,CAAC;YAAC,CAAC,CAAC;UAAA,CAAC;UAAC,CAACzM,CAAC,CAAC4K,SAAS,GAAE,MAAI;YAAC,IAAIvC,CAAC;YAACjH,CAAC,CAACkG,CAAC,EAACtG,CAAC,CAACkK,QAAQ,CAAC,KAAG5J,EAAE,CAAC0C,KAAK,KAAG,CAACqE,CAAC,GAACxF,CAAC,CAACY,MAAM,KAAG,IAAI,IAAE4E,CAAC,CAACD,KAAK,CAAC,CAAC,CAAC;UAAA;QAAC,CAAC,CAAC;MAAA;MAACb,CAAC,CAAC,CAAC;IAAA,CAAC,CAAC;EAAC,OAAOnK,CAAC,CAACwL,aAAa,CAACtE,EAAE,CAACuE,QAAQ,EAAC;IAACC,KAAK,EAACjE;EAAC,CAAC,EAACkB,CAAC,IAAER,CAAC,IAAEnI,CAAC,CAACwL,aAAa,CAACtI,EAAE,EAAC;IAACiJ,EAAE,EAAC/D,CAAC;IAACmD,GAAG,EAAC9F,CAAC,CAACuC,mBAAmB;IAACiG,QAAQ,EAACjL,EAAE,CAACkL,SAAS;IAAC,6BAA6B,EAAC,CAAC,CAAC;IAACC,EAAE,EAAC,QAAQ;IAAC9G,IAAI,EAAC,QAAQ;IAAC+G,OAAO,EAACvE;EAAC,CAAC,CAAC,EAAC9E,CAAC,CAAC;IAACuK,SAAS,EAAC7G,CAAC;IAACkD,QAAQ,EAACjC,CAAC;IAACkC,UAAU,EAACjE,CAAC;IAACkE,IAAI,EAACpC,CAAC;IAACqC,UAAU,EAAC6C,EAAE;IAACV,QAAQ,EAACW,EAAE;IAACF,OAAO,EAAC/F,CAAC;IAACoD,IAAI,EAAC;EAAe,CAAC,CAAC,EAACpD,CAAC,IAAER,CAAC,IAAEnI,CAAC,CAACwL,aAAa,CAACtI,EAAE,EAAC;IAACiJ,EAAE,EAAC9D,CAAC;IAACkD,GAAG,EAAC9F,CAAC,CAACwC,kBAAkB;IAACgG,QAAQ,EAACjL,EAAE,CAACkL,SAAS;IAAC,6BAA6B,EAAC,CAAC,CAAC;IAACC,EAAE,EAAC,QAAQ;IAAC9G,IAAI,EAAC,QAAQ;IAAC+G,OAAO,EAACrE;EAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIwF,EAAE,GAAC,KAAK;AAAC,SAASC,EAAEA,CAACvJ,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIb,CAAC,GAACvE,CAAC,CAAC,IAAI,CAAC;IAAC2G,CAAC,GAAC/E,CAAC,CAAC2C,CAAC,EAACa,CAAC,CAAC;IAAC,CAACwB,CAAC,EAACC,CAAC,CAAC,GAAC3G,EAAE,CAAC,EAAE,CAAC;IAACyE,CAAC,GAACrD,EAAE,CAAC,CAAC;IAAC0F,CAAC,GAAC1G,CAAC,CAACqH,CAAC,IAAE;MAACd,CAAC,CAACe,CAAC,IAAE;QAAC,IAAIC,CAAC,GAACD,CAAC,CAACQ,OAAO,CAACT,CAAC,CAAC;QAAC,IAAGE,CAAC,KAAG,CAAC,CAAC,EAAC;UAAC,IAAIa,CAAC,GAACd,CAAC,CAAC0G,KAAK,CAAC,CAAC;UAAC,OAAO5F,CAAC,CAAC+C,MAAM,CAAC5D,CAAC,EAAC,CAAC,CAAC,EAACa,CAAC;QAAA;QAAC,OAAOd,CAAC;MAAA,CAAC,CAAC;IAAA,CAAC,CAAC;IAACR,CAAC,GAAC9G,CAAC,CAACqH,CAAC,KAAGd,CAAC,CAACe,CAAC,IAAE,CAAC,GAAGA,CAAC,EAACD,CAAC,CAAC,CAAC,EAAC,MAAIX,CAAC,CAACW,CAAC,CAAC,CAAC,CAAC;IAACN,CAAC,GAAC/G,CAAC,CAAC,MAAI;MAAC,IAAIuH,CAAC;MAAC,IAAIF,CAAC,GAAC/D,EAAE,CAACW,CAAC,CAAC;MAAC,IAAG,CAACoD,CAAC,EAAC,OAAM,CAAC,CAAC;MAAC,IAAIC,CAAC,GAACD,CAAC,CAACwB,aAAa;MAAC,OAAM,CAACtB,CAAC,GAACtD,CAAC,CAACwC,OAAO,KAAG,IAAI,IAAEc,CAAC,CAACK,QAAQ,CAACN,CAAC,CAAC,GAAC,CAAC,CAAC,GAAChB,CAAC,CAAC+H,IAAI,CAACjG,CAAC,IAAE;QAAC,IAAIC,CAAC,EAACC,CAAC;QAAC,OAAM,CAAC,CAACD,CAAC,GAAChB,CAAC,CAACiH,cAAc,CAAClG,CAAC,CAAClD,QAAQ,CAACuB,OAAO,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC4B,CAAC,CAACT,QAAQ,CAACN,CAAC,CAAC,MAAI,CAACgB,CAAC,GAACjB,CAAC,CAACiH,cAAc,CAAClG,CAAC,CAAChD,OAAO,CAACqB,OAAO,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAAC6B,CAAC,CAACV,QAAQ,CAACN,CAAC,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA,CAAC,CAAC;IAACN,CAAC,GAAChH,CAAC,CAACqH,CAAC,IAAE;MAAC,KAAI,IAAIC,CAAC,IAAIhB,CAAC,EAACgB,CAAC,CAACpC,QAAQ,CAACuB,OAAO,KAAGY,CAAC,IAAEC,CAAC,CAACiB,KAAK,CAAC,CAAC;IAAA,CAAC,CAAC;IAACtB,CAAC,GAAC3H,CAAC,CAAC,OAAK;MAACoJ,eAAe,EAAC5B,CAAC;MAACyH,iBAAiB,EAAC7H,CAAC;MAACkC,yBAAyB,EAAC7B,CAAC;MAACiE,WAAW,EAAChE,CAAC;MAACiC,eAAe,EAAC5E,CAAC,CAAC4E;IAAe,CAAC,CAAC,EAAC,CAACnC,CAAC,EAACJ,CAAC,EAACK,CAAC,EAACC,CAAC,EAAC3C,CAAC,CAAC4E,eAAe,CAAC,CAAC;IAAC/B,CAAC,GAAC5H,CAAC,CAAC,OAAK,CAAC,CAAC,CAAC,EAAC,EAAE,CAAC;IAAC6H,CAAC,GAACtC,CAAC;IAACuC,CAAC,GAAC;MAAC+C,GAAG,EAAC9D;IAAC,CAAC;EAAC,OAAOzH,CAAC,CAACwL,aAAa,CAACxE,EAAE,CAACyE,QAAQ,EAAC;IAACC,KAAK,EAACrD;EAAC,CAAC,EAACtD,CAAC,CAAC;IAAC4G,QAAQ,EAACnD,CAAC;IAACoD,UAAU,EAACrD,CAAC;IAACsD,IAAI,EAACvD,CAAC;IAACwD,UAAU,EAACyD,EAAE;IAACxD,IAAI,EAAC;EAAe,CAAC,CAAC,EAAC/L,CAAC,CAACwL,aAAa,CAAC/F,CAAC,CAACuG,YAAY,EAAC,IAAI,CAAC,CAAC;AAAA;AAAC,IAAI4D,EAAE,GAAC/K,CAAC,CAAC0C,EAAE,CAAC;EAACsI,EAAE,GAAChL,CAAC,CAACqH,EAAE,CAAC;EAAC4D,EAAE,GAACjL,CAAC,CAAC4J,EAAE,CAAC;EAACsB,EAAE,GAAClL,CAAC,CAACgK,EAAE,CAAC;EAACmB,EAAE,GAACnL,CAAC,CAAC2K,EAAE,CAAC;EAACS,EAAE,GAACC,MAAM,CAACC,MAAM,CAACP,EAAE,EAAC;IAACQ,MAAM,EAACP,EAAE;IAACQ,OAAO,EAACP,EAAE;IAACQ,KAAK,EAACP,EAAE;IAACQ,KAAK,EAACP;EAAE,CAAC,CAAC;AAAC,SAAOC,EAAE,IAAIO,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}