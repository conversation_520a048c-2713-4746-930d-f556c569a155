{"ast": null, "code": "import { debounce, memo, notUndefined, approxEqual } from \"./utils.js\";\nconst getRect = element => {\n  const {\n    offsetWidth,\n    offsetHeight\n  } = element;\n  return {\n    width: offsetWidth,\n    height: offsetHeight\n  };\n};\nconst defaultKeyExtractor = index => index;\nconst defaultRangeExtractor = range => {\n  const start = Math.max(range.startIndex - range.overscan, 0);\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1);\n  const arr = [];\n  for (let i = start; i <= end; i++) {\n    arr.push(i);\n  }\n  return arr;\n};\nconst observeElementRect = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  const handler = rect => {\n    const {\n      width,\n      height\n    } = rect;\n    cb({\n      width: Math.round(width),\n      height: Math.round(height)\n    });\n  };\n  handler(getRect(element));\n  if (!targetWindow.ResizeObserver) {\n    return () => {};\n  }\n  const observer = new targetWindow.ResizeObserver(entries => {\n    const run = () => {\n      const entry = entries[0];\n      if (entry == null ? void 0 : entry.borderBoxSize) {\n        const box = entry.borderBoxSize[0];\n        if (box) {\n          handler({\n            width: box.inlineSize,\n            height: box.blockSize\n          });\n          return;\n        }\n      }\n      handler(getRect(element));\n    };\n    instance.options.useAnimationFrameWithResizeObserver ? requestAnimationFrame(run) : run();\n  });\n  observer.observe(element, {\n    box: \"border-box\"\n  });\n  return () => {\n    observer.unobserve(element);\n  };\n};\nconst addEventListenerOptions = {\n  passive: true\n};\nconst observeWindowRect = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const handler = () => {\n    cb({\n      width: element.innerWidth,\n      height: element.innerHeight\n    });\n  };\n  handler();\n  element.addEventListener(\"resize\", handler, addEventListenerOptions);\n  return () => {\n    element.removeEventListener(\"resize\", handler);\n  };\n};\nconst supportsScrollend = typeof window == \"undefined\" ? true : \"onscrollend\" in window;\nconst observeElementOffset = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  let offset = 0;\n  const fallback = instance.options.useScrollendEvent && supportsScrollend ? () => void 0 : debounce(targetWindow, () => {\n    cb(offset, false);\n  }, instance.options.isScrollingResetDelay);\n  const createHandler = isScrolling => () => {\n    const {\n      horizontal,\n      isRtl\n    } = instance.options;\n    offset = horizontal ? element[\"scrollLeft\"] * (isRtl && -1 || 1) : element[\"scrollTop\"];\n    fallback();\n    cb(offset, isScrolling);\n  };\n  const handler = createHandler(true);\n  const endHandler = createHandler(false);\n  endHandler();\n  element.addEventListener(\"scroll\", handler, addEventListenerOptions);\n  const registerScrollendEvent = instance.options.useScrollendEvent && supportsScrollend;\n  if (registerScrollendEvent) {\n    element.addEventListener(\"scrollend\", endHandler, addEventListenerOptions);\n  }\n  return () => {\n    element.removeEventListener(\"scroll\", handler);\n    if (registerScrollendEvent) {\n      element.removeEventListener(\"scrollend\", endHandler);\n    }\n  };\n};\nconst observeWindowOffset = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  let offset = 0;\n  const fallback = instance.options.useScrollendEvent && supportsScrollend ? () => void 0 : debounce(targetWindow, () => {\n    cb(offset, false);\n  }, instance.options.isScrollingResetDelay);\n  const createHandler = isScrolling => () => {\n    offset = element[instance.options.horizontal ? \"scrollX\" : \"scrollY\"];\n    fallback();\n    cb(offset, isScrolling);\n  };\n  const handler = createHandler(true);\n  const endHandler = createHandler(false);\n  endHandler();\n  element.addEventListener(\"scroll\", handler, addEventListenerOptions);\n  const registerScrollendEvent = instance.options.useScrollendEvent && supportsScrollend;\n  if (registerScrollendEvent) {\n    element.addEventListener(\"scrollend\", endHandler, addEventListenerOptions);\n  }\n  return () => {\n    element.removeEventListener(\"scroll\", handler);\n    if (registerScrollendEvent) {\n      element.removeEventListener(\"scrollend\", endHandler);\n    }\n  };\n};\nconst measureElement = (element, entry, instance) => {\n  if (entry == null ? void 0 : entry.borderBoxSize) {\n    const box = entry.borderBoxSize[0];\n    if (box) {\n      const size = Math.round(box[instance.options.horizontal ? \"inlineSize\" : \"blockSize\"]);\n      return size;\n    }\n  }\n  return element[instance.options.horizontal ? \"offsetWidth\" : \"offsetHeight\"];\n};\nconst windowScroll = (offset, {\n  adjustments = 0,\n  behavior\n}, instance) => {\n  var _a, _b;\n  const toOffset = offset + adjustments;\n  (_b = (_a = instance.scrollElement) == null ? void 0 : _a.scrollTo) == null ? void 0 : _b.call(_a, {\n    [instance.options.horizontal ? \"left\" : \"top\"]: toOffset,\n    behavior\n  });\n};\nconst elementScroll = (offset, {\n  adjustments = 0,\n  behavior\n}, instance) => {\n  var _a, _b;\n  const toOffset = offset + adjustments;\n  (_b = (_a = instance.scrollElement) == null ? void 0 : _a.scrollTo) == null ? void 0 : _b.call(_a, {\n    [instance.options.horizontal ? \"left\" : \"top\"]: toOffset,\n    behavior\n  });\n};\nclass Virtualizer {\n  constructor(opts) {\n    this.unsubs = [];\n    this.scrollElement = null;\n    this.targetWindow = null;\n    this.isScrolling = false;\n    this.scrollToIndexTimeoutId = null;\n    this.measurementsCache = [];\n    this.itemSizeCache = /* @__PURE__ */new Map();\n    this.pendingMeasuredCacheIndexes = [];\n    this.scrollRect = null;\n    this.scrollOffset = null;\n    this.scrollDirection = null;\n    this.scrollAdjustments = 0;\n    this.elementsCache = /* @__PURE__ */new Map();\n    this.observer = /* @__PURE__ */(() => {\n      let _ro = null;\n      const get = () => {\n        if (_ro) {\n          return _ro;\n        }\n        if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n          return null;\n        }\n        return _ro = new this.targetWindow.ResizeObserver(entries => {\n          entries.forEach(entry => {\n            const run = () => {\n              this._measureElement(entry.target, entry);\n            };\n            this.options.useAnimationFrameWithResizeObserver ? requestAnimationFrame(run) : run();\n          });\n        });\n      };\n      return {\n        disconnect: () => {\n          var _a;\n          (_a = get()) == null ? void 0 : _a.disconnect();\n          _ro = null;\n        },\n        observe: target => {\n          var _a;\n          return (_a = get()) == null ? void 0 : _a.observe(target, {\n            box: \"border-box\"\n          });\n        },\n        unobserve: target => {\n          var _a;\n          return (_a = get()) == null ? void 0 : _a.unobserve(target);\n        }\n      };\n    })();\n    this.range = null;\n    this.setOptions = opts2 => {\n      Object.entries(opts2).forEach(([key, value]) => {\n        if (typeof value === \"undefined\") delete opts2[key];\n      });\n      this.options = {\n        debug: false,\n        initialOffset: 0,\n        overscan: 1,\n        paddingStart: 0,\n        paddingEnd: 0,\n        scrollPaddingStart: 0,\n        scrollPaddingEnd: 0,\n        horizontal: false,\n        getItemKey: defaultKeyExtractor,\n        rangeExtractor: defaultRangeExtractor,\n        onChange: () => {},\n        measureElement,\n        initialRect: {\n          width: 0,\n          height: 0\n        },\n        scrollMargin: 0,\n        gap: 0,\n        indexAttribute: \"data-index\",\n        initialMeasurementsCache: [],\n        lanes: 1,\n        isScrollingResetDelay: 150,\n        enabled: true,\n        isRtl: false,\n        useScrollendEvent: false,\n        useAnimationFrameWithResizeObserver: false,\n        ...opts2\n      };\n    };\n    this.notify = sync => {\n      var _a, _b;\n      (_b = (_a = this.options).onChange) == null ? void 0 : _b.call(_a, this, sync);\n    };\n    this.maybeNotify = memo(() => {\n      this.calculateRange();\n      return [this.isScrolling, this.range ? this.range.startIndex : null, this.range ? this.range.endIndex : null];\n    }, isScrolling => {\n      this.notify(isScrolling);\n    }, {\n      key: process.env.NODE_ENV !== \"production\" && \"maybeNotify\",\n      debug: () => this.options.debug,\n      initialDeps: [this.isScrolling, this.range ? this.range.startIndex : null, this.range ? this.range.endIndex : null]\n    });\n    this.cleanup = () => {\n      this.unsubs.filter(Boolean).forEach(d => d());\n      this.unsubs = [];\n      this.observer.disconnect();\n      this.scrollElement = null;\n      this.targetWindow = null;\n    };\n    this._didMount = () => {\n      return () => {\n        this.cleanup();\n      };\n    };\n    this._willUpdate = () => {\n      var _a;\n      const scrollElement = this.options.enabled ? this.options.getScrollElement() : null;\n      if (this.scrollElement !== scrollElement) {\n        this.cleanup();\n        if (!scrollElement) {\n          this.maybeNotify();\n          return;\n        }\n        this.scrollElement = scrollElement;\n        if (this.scrollElement && \"ownerDocument\" in this.scrollElement) {\n          this.targetWindow = this.scrollElement.ownerDocument.defaultView;\n        } else {\n          this.targetWindow = ((_a = this.scrollElement) == null ? void 0 : _a.window) ?? null;\n        }\n        this.elementsCache.forEach(cached => {\n          this.observer.observe(cached);\n        });\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: void 0,\n          behavior: void 0\n        });\n        this.unsubs.push(this.options.observeElementRect(this, rect => {\n          this.scrollRect = rect;\n          this.maybeNotify();\n        }));\n        this.unsubs.push(this.options.observeElementOffset(this, (offset, isScrolling) => {\n          this.scrollAdjustments = 0;\n          this.scrollDirection = isScrolling ? this.getScrollOffset() < offset ? \"forward\" : \"backward\" : null;\n          this.scrollOffset = offset;\n          this.isScrolling = isScrolling;\n          this.maybeNotify();\n        }));\n      }\n    };\n    this.getSize = () => {\n      if (!this.options.enabled) {\n        this.scrollRect = null;\n        return 0;\n      }\n      this.scrollRect = this.scrollRect ?? this.options.initialRect;\n      return this.scrollRect[this.options.horizontal ? \"width\" : \"height\"];\n    };\n    this.getScrollOffset = () => {\n      if (!this.options.enabled) {\n        this.scrollOffset = null;\n        return 0;\n      }\n      this.scrollOffset = this.scrollOffset ?? (typeof this.options.initialOffset === \"function\" ? this.options.initialOffset() : this.options.initialOffset);\n      return this.scrollOffset;\n    };\n    this.getFurthestMeasurement = (measurements, index) => {\n      const furthestMeasurementsFound = /* @__PURE__ */new Map();\n      const furthestMeasurements = /* @__PURE__ */new Map();\n      for (let m = index - 1; m >= 0; m--) {\n        const measurement = measurements[m];\n        if (furthestMeasurementsFound.has(measurement.lane)) {\n          continue;\n        }\n        const previousFurthestMeasurement = furthestMeasurements.get(measurement.lane);\n        if (previousFurthestMeasurement == null || measurement.end > previousFurthestMeasurement.end) {\n          furthestMeasurements.set(measurement.lane, measurement);\n        } else if (measurement.end < previousFurthestMeasurement.end) {\n          furthestMeasurementsFound.set(measurement.lane, true);\n        }\n        if (furthestMeasurementsFound.size === this.options.lanes) {\n          break;\n        }\n      }\n      return furthestMeasurements.size === this.options.lanes ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n        if (a.end === b.end) {\n          return a.index - b.index;\n        }\n        return a.end - b.end;\n      })[0] : void 0;\n    };\n    this.getMeasurementOptions = memo(() => [this.options.count, this.options.paddingStart, this.options.scrollMargin, this.options.getItemKey, this.options.enabled], (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n      this.pendingMeasuredCacheIndexes = [];\n      return {\n        count,\n        paddingStart,\n        scrollMargin,\n        getItemKey,\n        enabled\n      };\n    }, {\n      key: false\n    });\n    this.getMeasurements = memo(() => [this.getMeasurementOptions(), this.itemSizeCache], ({\n      count,\n      paddingStart,\n      scrollMargin,\n      getItemKey,\n      enabled\n    }, itemSizeCache) => {\n      if (!enabled) {\n        this.measurementsCache = [];\n        this.itemSizeCache.clear();\n        return [];\n      }\n      if (this.measurementsCache.length === 0) {\n        this.measurementsCache = this.options.initialMeasurementsCache;\n        this.measurementsCache.forEach(item => {\n          this.itemSizeCache.set(item.key, item.size);\n        });\n      }\n      const min = this.pendingMeasuredCacheIndexes.length > 0 ? Math.min(...this.pendingMeasuredCacheIndexes) : 0;\n      this.pendingMeasuredCacheIndexes = [];\n      const measurements = this.measurementsCache.slice(0, min);\n      for (let i = min; i < count; i++) {\n        const key = getItemKey(i);\n        const furthestMeasurement = this.options.lanes === 1 ? measurements[i - 1] : this.getFurthestMeasurement(measurements, i);\n        const start = furthestMeasurement ? furthestMeasurement.end + this.options.gap : paddingStart + scrollMargin;\n        const measuredSize = itemSizeCache.get(key);\n        const size = typeof measuredSize === \"number\" ? measuredSize : this.options.estimateSize(i);\n        const end = start + size;\n        const lane = furthestMeasurement ? furthestMeasurement.lane : i % this.options.lanes;\n        measurements[i] = {\n          index: i,\n          start,\n          size,\n          end,\n          key,\n          lane\n        };\n      }\n      this.measurementsCache = measurements;\n      return measurements;\n    }, {\n      key: process.env.NODE_ENV !== \"production\" && \"getMeasurements\",\n      debug: () => this.options.debug\n    });\n    this.calculateRange = memo(() => [this.getMeasurements(), this.getSize(), this.getScrollOffset(), this.options.lanes], (measurements, outerSize, scrollOffset, lanes) => {\n      return this.range = measurements.length > 0 && outerSize > 0 ? calculateRange({\n        measurements,\n        outerSize,\n        scrollOffset,\n        lanes\n      }) : null;\n    }, {\n      key: process.env.NODE_ENV !== \"production\" && \"calculateRange\",\n      debug: () => this.options.debug\n    });\n    this.getVirtualIndexes = memo(() => {\n      let startIndex = null;\n      let endIndex = null;\n      const range = this.calculateRange();\n      if (range) {\n        startIndex = range.startIndex;\n        endIndex = range.endIndex;\n      }\n      this.maybeNotify.updateDeps([this.isScrolling, startIndex, endIndex]);\n      return [this.options.rangeExtractor, this.options.overscan, this.options.count, startIndex, endIndex];\n    }, (rangeExtractor, overscan, count, startIndex, endIndex) => {\n      return startIndex === null || endIndex === null ? [] : rangeExtractor({\n        startIndex,\n        endIndex,\n        overscan,\n        count\n      });\n    }, {\n      key: process.env.NODE_ENV !== \"production\" && \"getVirtualIndexes\",\n      debug: () => this.options.debug\n    });\n    this.indexFromElement = node => {\n      const attributeName = this.options.indexAttribute;\n      const indexStr = node.getAttribute(attributeName);\n      if (!indexStr) {\n        console.warn(`Missing attribute name '${attributeName}={index}' on measured element.`);\n        return -1;\n      }\n      return parseInt(indexStr, 10);\n    };\n    this._measureElement = (node, entry) => {\n      const index = this.indexFromElement(node);\n      const item = this.measurementsCache[index];\n      if (!item) {\n        return;\n      }\n      const key = item.key;\n      const prevNode = this.elementsCache.get(key);\n      if (prevNode !== node) {\n        if (prevNode) {\n          this.observer.unobserve(prevNode);\n        }\n        this.observer.observe(node);\n        this.elementsCache.set(key, node);\n      }\n      if (node.isConnected) {\n        this.resizeItem(index, this.options.measureElement(node, entry, this));\n      }\n    };\n    this.resizeItem = (index, size) => {\n      const item = this.measurementsCache[index];\n      if (!item) {\n        return;\n      }\n      const itemSize = this.itemSizeCache.get(item.key) ?? item.size;\n      const delta = size - itemSize;\n      if (delta !== 0) {\n        if (this.shouldAdjustScrollPositionOnItemSizeChange !== void 0 ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this) : item.start < this.getScrollOffset() + this.scrollAdjustments) {\n          if (process.env.NODE_ENV !== \"production\" && this.options.debug) {\n            console.info(\"correction\", delta);\n          }\n          this._scrollToOffset(this.getScrollOffset(), {\n            adjustments: this.scrollAdjustments += delta,\n            behavior: void 0\n          });\n        }\n        this.pendingMeasuredCacheIndexes.push(item.index);\n        this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size));\n        this.notify(false);\n      }\n    };\n    this.measureElement = node => {\n      if (!node) {\n        this.elementsCache.forEach((cached, key) => {\n          if (!cached.isConnected) {\n            this.observer.unobserve(cached);\n            this.elementsCache.delete(key);\n          }\n        });\n        return;\n      }\n      this._measureElement(node, void 0);\n    };\n    this.getVirtualItems = memo(() => [this.getVirtualIndexes(), this.getMeasurements()], (indexes, measurements) => {\n      const virtualItems = [];\n      for (let k = 0, len = indexes.length; k < len; k++) {\n        const i = indexes[k];\n        const measurement = measurements[i];\n        virtualItems.push(measurement);\n      }\n      return virtualItems;\n    }, {\n      key: process.env.NODE_ENV !== \"production\" && \"getVirtualItems\",\n      debug: () => this.options.debug\n    });\n    this.getVirtualItemForOffset = offset => {\n      const measurements = this.getMeasurements();\n      if (measurements.length === 0) {\n        return void 0;\n      }\n      return notUndefined(measurements[findNearestBinarySearch(0, measurements.length - 1, index => notUndefined(measurements[index]).start, offset)]);\n    };\n    this.getOffsetForAlignment = (toOffset, align, itemSize = 0) => {\n      const size = this.getSize();\n      const scrollOffset = this.getScrollOffset();\n      if (align === \"auto\") {\n        align = toOffset >= scrollOffset + size ? \"end\" : \"start\";\n      }\n      if (align === \"center\") {\n        toOffset += (itemSize - size) / 2;\n      } else if (align === \"end\") {\n        toOffset -= size;\n      }\n      const maxOffset = this.getTotalSize() - size;\n      return Math.max(Math.min(maxOffset, toOffset), 0);\n    };\n    this.getOffsetForIndex = (index, align = \"auto\") => {\n      index = Math.max(0, Math.min(index, this.options.count - 1));\n      const item = this.measurementsCache[index];\n      if (!item) {\n        return void 0;\n      }\n      const size = this.getSize();\n      const scrollOffset = this.getScrollOffset();\n      if (align === \"auto\") {\n        if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {\n          align = \"end\";\n        } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {\n          align = \"start\";\n        } else {\n          return [scrollOffset, align];\n        }\n      }\n      const toOffset = align === \"end\" ? item.end + this.options.scrollPaddingEnd : item.start - this.options.scrollPaddingStart;\n      return [this.getOffsetForAlignment(toOffset, align, item.size), align];\n    };\n    this.isDynamicMode = () => this.elementsCache.size > 0;\n    this.cancelScrollToIndex = () => {\n      if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {\n        this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId);\n        this.scrollToIndexTimeoutId = null;\n      }\n    };\n    this.scrollToOffset = (toOffset, {\n      align = \"start\",\n      behavior\n    } = {}) => {\n      this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && this.isDynamicMode()) {\n        console.warn(\"The `smooth` scroll behavior is not fully supported with dynamic size.\");\n      }\n      this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {\n        adjustments: void 0,\n        behavior\n      });\n    };\n    this.scrollToIndex = (index, {\n      align: initialAlign = \"auto\",\n      behavior\n    } = {}) => {\n      index = Math.max(0, Math.min(index, this.options.count - 1));\n      this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && this.isDynamicMode()) {\n        console.warn(\"The `smooth` scroll behavior is not fully supported with dynamic size.\");\n      }\n      const offsetAndAlign = this.getOffsetForIndex(index, initialAlign);\n      if (!offsetAndAlign) return;\n      const [offset, align] = offsetAndAlign;\n      this._scrollToOffset(offset, {\n        adjustments: void 0,\n        behavior\n      });\n      if (behavior !== \"smooth\" && this.isDynamicMode() && this.targetWindow) {\n        this.scrollToIndexTimeoutId = this.targetWindow.setTimeout(() => {\n          this.scrollToIndexTimeoutId = null;\n          const elementInDOM = this.elementsCache.has(this.options.getItemKey(index));\n          if (elementInDOM) {\n            const result = this.getOffsetForIndex(index, align);\n            if (!result) return;\n            const [latestOffset] = result;\n            const currentScrollOffset = this.getScrollOffset();\n            if (!approxEqual(latestOffset, currentScrollOffset)) {\n              this.scrollToIndex(index, {\n                align,\n                behavior\n              });\n            }\n          } else {\n            this.scrollToIndex(index, {\n              align,\n              behavior\n            });\n          }\n        });\n      }\n    };\n    this.scrollBy = (delta, {\n      behavior\n    } = {}) => {\n      this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && this.isDynamicMode()) {\n        console.warn(\"The `smooth` scroll behavior is not fully supported with dynamic size.\");\n      }\n      this._scrollToOffset(this.getScrollOffset() + delta, {\n        adjustments: void 0,\n        behavior\n      });\n    };\n    this.getTotalSize = () => {\n      var _a;\n      const measurements = this.getMeasurements();\n      let end;\n      if (measurements.length === 0) {\n        end = this.options.paddingStart;\n      } else if (this.options.lanes === 1) {\n        end = ((_a = measurements[measurements.length - 1]) == null ? void 0 : _a.end) ?? 0;\n      } else {\n        const endByLane = Array(this.options.lanes).fill(null);\n        let endIndex = measurements.length - 1;\n        while (endIndex >= 0 && endByLane.some(val => val === null)) {\n          const item = measurements[endIndex];\n          if (endByLane[item.lane] === null) {\n            endByLane[item.lane] = item.end;\n          }\n          endIndex--;\n        }\n        end = Math.max(...endByLane.filter(val => val !== null));\n      }\n      return Math.max(end - this.options.scrollMargin + this.options.paddingEnd, 0);\n    };\n    this._scrollToOffset = (offset, {\n      adjustments,\n      behavior\n    }) => {\n      this.options.scrollToFn(offset, {\n        behavior,\n        adjustments\n      }, this);\n    };\n    this.measure = () => {\n      this.itemSizeCache = /* @__PURE__ */new Map();\n      this.notify(false);\n    };\n    this.setOptions(opts);\n  }\n}\nconst findNearestBinarySearch = (low, high, getCurrentValue, value) => {\n  while (low <= high) {\n    const middle = (low + high) / 2 | 0;\n    const currentValue = getCurrentValue(middle);\n    if (currentValue < value) {\n      low = middle + 1;\n    } else if (currentValue > value) {\n      high = middle - 1;\n    } else {\n      return middle;\n    }\n  }\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\nfunction calculateRange({\n  measurements,\n  outerSize,\n  scrollOffset,\n  lanes\n}) {\n  const lastIndex = measurements.length - 1;\n  const getOffset = index => measurements[index].start;\n  if (measurements.length <= lanes) {\n    return {\n      startIndex: 0,\n      endIndex: lastIndex\n    };\n  }\n  let startIndex = findNearestBinarySearch(0, lastIndex, getOffset, scrollOffset);\n  let endIndex = startIndex;\n  if (lanes === 1) {\n    while (endIndex < lastIndex && measurements[endIndex].end < scrollOffset + outerSize) {\n      endIndex++;\n    }\n  } else if (lanes > 1) {\n    const endPerLane = Array(lanes).fill(0);\n    while (endIndex < lastIndex && endPerLane.some(pos => pos < scrollOffset + outerSize)) {\n      const item = measurements[endIndex];\n      endPerLane[item.lane] = item.end;\n      endIndex++;\n    }\n    const startPerLane = Array(lanes).fill(scrollOffset + outerSize);\n    while (startIndex >= 0 && startPerLane.some(pos => pos >= scrollOffset)) {\n      const item = measurements[startIndex];\n      startPerLane[item.lane] = item.start;\n      startIndex--;\n    }\n    startIndex = Math.max(0, startIndex - startIndex % lanes);\n    endIndex = Math.min(lastIndex, endIndex + (lanes - 1 - endIndex % lanes));\n  }\n  return {\n    startIndex,\n    endIndex\n  };\n}\nexport { Virtualizer, approxEqual, debounce, defaultKeyExtractor, defaultRangeExtractor, elementScroll, measureElement, memo, notUndefined, observeElementOffset, observeElementRect, observeWindowOffset, observeWindowRect, windowScroll };", "map": {"version": 3, "names": ["getRect", "element", "offsetWidth", "offsetHeight", "width", "height", "defaultKeyExtractor", "index", "defaultRangeExtractor", "range", "start", "Math", "max", "startIndex", "overscan", "end", "min", "endIndex", "count", "arr", "i", "push", "observeElementRect", "instance", "cb", "scrollElement", "targetWindow", "handler", "rect", "round", "ResizeObserver", "observer", "entries", "run", "entry", "borderBoxSize", "box", "inlineSize", "blockSize", "options", "useAnimationFrameWithResizeObserver", "requestAnimationFrame", "observe", "unobserve", "addEventListenerOptions", "passive", "observeWindowRect", "innerWidth", "innerHeight", "addEventListener", "removeEventListener", "supportsScrollend", "window", "observeElementOffset", "offset", "fallback", "useScrollendEvent", "debounce", "isScrollingResetDelay", "createHandler", "isScrolling", "horizontal", "isRtl", "end<PERSON><PERSON><PERSON>", "registerScrollendEvent", "observeWindowOffset", "measureElement", "size", "windowScroll", "adjustments", "behavior", "toOffset", "_b", "_a", "scrollTo", "call", "elementScroll", "Virtualizer", "constructor", "opts", "unsubs", "scrollToIndexTimeoutId", "measurementsCache", "itemSizeCache", "Map", "pendingMeasuredCacheIndexes", "scrollRect", "scrollOffset", "scrollDirection", "scrollAdjustments", "elementsCache", "_ro", "get", "for<PERSON>ach", "_measureElement", "target", "disconnect", "setOptions", "opts2", "Object", "key", "value", "debug", "initialOffset", "paddingStart", "paddingEnd", "scrollPaddingStart", "scrollPaddingEnd", "getItemKey", "rangeExtractor", "onChange", "initialRect", "scrollMargin", "gap", "indexAttribute", "initialMeasurementsCache", "lanes", "enabled", "notify", "sync", "maybeNotify", "memo", "calculateRange", "process", "env", "NODE_ENV", "initialDeps", "cleanup", "filter", "Boolean", "d", "_didMount", "_willUpdate", "getScrollElement", "ownerDocument", "defaultView", "cached", "_scrollToOffset", "getScrollOffset", "getSize", "getFurthestMeasurement", "measurements", "furthestMeasurementsFound", "furthestMeasurements", "m", "measurement", "has", "lane", "previousFurthestMeasurement", "set", "Array", "from", "values", "sort", "a", "b", "getMeasurementOptions", "getMeasurements", "clear", "length", "item", "slice", "furthestMeasurement", "measuredSize", "estimateSize", "outerSize", "getVirtualIndexes", "updateDeps", "indexFromElement", "node", "attributeName", "indexStr", "getAttribute", "console", "warn", "parseInt", "prevNode", "isConnected", "resizeItem", "itemSize", "delta", "shouldAdjustScrollPositionOnItemSizeChange", "info", "delete", "getVirtualItems", "indexes", "virtualItems", "k", "len", "getVirtualItemForOffset", "notUndefined", "findNearestBinarySearch", "getOffsetForAlignment", "align", "maxOffset", "getTotalSize", "getOffsetForIndex", "isDynamicMode", "cancelScrollToIndex", "clearTimeout", "scrollToOffset", "scrollToIndex", "initialAlign", "offsetAndAlign", "setTimeout", "elementInDOM", "result", "latestOffset", "currentScrollOffset", "approxEqual", "scrollBy", "endByLane", "fill", "some", "val", "scrollToFn", "measure", "low", "high", "getCurrentValue", "middle", "currentValue", "lastIndex", "getOffset", "endPerLane", "pos", "startPerLane"], "sources": ["E:\\code\\Resonance-KLE\\home-security-dashboard\\node_modules\\@tanstack\\virtual-core\\src\\index.ts"], "sourcesContent": ["import { approxEqual, debounce, memo, notUndefined } from './utils'\n\nexport * from './utils'\n\n//\n\ntype ScrollDirection = 'forward' | 'backward'\n\ntype ScrollAlignment = 'start' | 'center' | 'end' | 'auto'\n\ntype ScrollBehavior = 'auto' | 'smooth'\n\nexport interface ScrollToOptions {\n  align?: ScrollAlignment\n  behavior?: ScrollBehavior\n}\n\ntype ScrollToOffsetOptions = ScrollToOptions\n\ntype ScrollToIndexOptions = ScrollToOptions\n\nexport interface Range {\n  startIndex: number\n  endIndex: number\n  overscan: number\n  count: number\n}\n\ntype Key = number | string | bigint\n\nexport interface VirtualItem {\n  key: Key\n  index: number\n  start: number\n  end: number\n  size: number\n  lane: number\n}\n\nexport interface Rect {\n  width: number\n  height: number\n}\n\n//\n\nconst getRect = (element: HTMLElement): Rect => {\n  const { offsetWidth, offsetHeight } = element\n  return { width: offsetWidth, height: offsetHeight }\n}\n\nexport const defaultKeyExtractor = (index: number) => index\n\nexport const defaultRangeExtractor = (range: Range) => {\n  const start = Math.max(range.startIndex - range.overscan, 0)\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1)\n\n  const arr = []\n\n  for (let i = start; i <= end; i++) {\n    arr.push(i)\n  }\n\n  return arr\n}\n\nexport const observeElementRect = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  const handler = (rect: Rect) => {\n    const { width, height } = rect\n    cb({ width: Math.round(width), height: Math.round(height) })\n  }\n\n  handler(getRect(element as unknown as HTMLElement))\n\n  if (!targetWindow.ResizeObserver) {\n    return () => {}\n  }\n\n  const observer = new targetWindow.ResizeObserver((entries) => {\n    const run = () => {\n      const entry = entries[0]\n      if (entry?.borderBoxSize) {\n        const box = entry.borderBoxSize[0]\n        if (box) {\n          handler({ width: box.inlineSize, height: box.blockSize })\n          return\n        }\n      }\n      handler(getRect(element as unknown as HTMLElement))\n    }\n\n    instance.options.useAnimationFrameWithResizeObserver\n      ? requestAnimationFrame(run)\n      : run()\n  })\n\n  observer.observe(element, { box: 'border-box' })\n\n  return () => {\n    observer.unobserve(element)\n  }\n}\n\nconst addEventListenerOptions = {\n  passive: true,\n}\n\nexport const observeWindowRect = (\n  instance: Virtualizer<Window, any>,\n  cb: (rect: Rect) => void,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n\n  const handler = () => {\n    cb({ width: element.innerWidth, height: element.innerHeight })\n  }\n  handler()\n\n  element.addEventListener('resize', handler, addEventListenerOptions)\n\n  return () => {\n    element.removeEventListener('resize', handler)\n  }\n}\n\nconst supportsScrollend =\n  typeof window == 'undefined' ? true : 'onscrollend' in window\n\ntype ObserveOffsetCallBack = (offset: number, isScrolling: boolean) => void\n\nexport const observeElementOffset = <T extends Element>(\n  instance: Virtualizer<T, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    const { horizontal, isRtl } = instance.options\n    offset = horizontal\n      ? element['scrollLeft'] * ((isRtl && -1) || 1)\n      : element['scrollTop']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const observeWindowOffset = (\n  instance: Virtualizer<Window, any>,\n  cb: ObserveOffsetCallBack,\n) => {\n  const element = instance.scrollElement\n  if (!element) {\n    return\n  }\n  const targetWindow = instance.targetWindow\n  if (!targetWindow) {\n    return\n  }\n\n  let offset = 0\n  const fallback =\n    instance.options.useScrollendEvent && supportsScrollend\n      ? () => undefined\n      : debounce(\n          targetWindow,\n          () => {\n            cb(offset, false)\n          },\n          instance.options.isScrollingResetDelay,\n        )\n\n  const createHandler = (isScrolling: boolean) => () => {\n    offset = element[instance.options.horizontal ? 'scrollX' : 'scrollY']\n    fallback()\n    cb(offset, isScrolling)\n  }\n  const handler = createHandler(true)\n  const endHandler = createHandler(false)\n  endHandler()\n\n  element.addEventListener('scroll', handler, addEventListenerOptions)\n  const registerScrollendEvent =\n    instance.options.useScrollendEvent && supportsScrollend\n  if (registerScrollendEvent) {\n    element.addEventListener('scrollend', endHandler, addEventListenerOptions)\n  }\n  return () => {\n    element.removeEventListener('scroll', handler)\n    if (registerScrollendEvent) {\n      element.removeEventListener('scrollend', endHandler)\n    }\n  }\n}\n\nexport const measureElement = <TItemElement extends Element>(\n  element: TItemElement,\n  entry: ResizeObserverEntry | undefined,\n  instance: Virtualizer<any, TItemElement>,\n) => {\n  if (entry?.borderBoxSize) {\n    const box = entry.borderBoxSize[0]\n    if (box) {\n      const size = Math.round(\n        box[instance.options.horizontal ? 'inlineSize' : 'blockSize'],\n      )\n      return size\n    }\n  }\n\n  return (element as unknown as HTMLElement)[\n    instance.options.horizontal ? 'offsetWidth' : 'offsetHeight'\n  ]\n}\n\nexport const windowScroll = <T extends Window>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport const elementScroll = <T extends Element>(\n  offset: number,\n  {\n    adjustments = 0,\n    behavior,\n  }: { adjustments?: number; behavior?: ScrollBehavior },\n  instance: Virtualizer<T, any>,\n) => {\n  const toOffset = offset + adjustments\n\n  instance.scrollElement?.scrollTo?.({\n    [instance.options.horizontal ? 'left' : 'top']: toOffset,\n    behavior,\n  })\n}\n\nexport interface VirtualizerOptions<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  // Required from the user\n  count: number\n  getScrollElement: () => TScrollElement | null\n  estimateSize: (index: number) => number\n\n  // Required from the framework adapter (but can be overridden)\n  scrollToFn: (\n    offset: number,\n    options: { adjustments?: number; behavior?: ScrollBehavior },\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => void\n  observeElementRect: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: (rect: Rect) => void,\n  ) => void | (() => void)\n  observeElementOffset: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    cb: ObserveOffsetCallBack,\n  ) => void | (() => void)\n  // Optional\n  debug?: boolean\n  initialRect?: Rect\n  onChange?: (\n    instance: Virtualizer<TScrollElement, TItemElement>,\n    sync: boolean,\n  ) => void\n  measureElement?: (\n    element: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n    instance: Virtualizer<TScrollElement, TItemElement>,\n  ) => number\n  overscan?: number\n  horizontal?: boolean\n  paddingStart?: number\n  paddingEnd?: number\n  scrollPaddingStart?: number\n  scrollPaddingEnd?: number\n  initialOffset?: number | (() => number)\n  getItemKey?: (index: number) => Key\n  rangeExtractor?: (range: Range) => Array<number>\n  scrollMargin?: number\n  gap?: number\n  indexAttribute?: string\n  initialMeasurementsCache?: Array<VirtualItem>\n  lanes?: number\n  isScrollingResetDelay?: number\n  useScrollendEvent?: boolean\n  enabled?: boolean\n  isRtl?: boolean\n  useAnimationFrameWithResizeObserver?: boolean\n}\n\nexport class Virtualizer<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n> {\n  private unsubs: Array<void | (() => void)> = []\n  options!: Required<VirtualizerOptions<TScrollElement, TItemElement>>\n  scrollElement: TScrollElement | null = null\n  targetWindow: (Window & typeof globalThis) | null = null\n  isScrolling = false\n  private scrollToIndexTimeoutId: number | null = null\n  measurementsCache: Array<VirtualItem> = []\n  private itemSizeCache = new Map<Key, number>()\n  private pendingMeasuredCacheIndexes: Array<number> = []\n  scrollRect: Rect | null = null\n  scrollOffset: number | null = null\n  scrollDirection: ScrollDirection | null = null\n  private scrollAdjustments = 0\n  shouldAdjustScrollPositionOnItemSizeChange:\n    | undefined\n    | ((\n        item: VirtualItem,\n        delta: number,\n        instance: Virtualizer<TScrollElement, TItemElement>,\n      ) => boolean)\n  elementsCache = new Map<Key, TItemElement>()\n  private observer = (() => {\n    let _ro: ResizeObserver | null = null\n\n    const get = () => {\n      if (_ro) {\n        return _ro\n      }\n\n      if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n        return null\n      }\n\n      return (_ro = new this.targetWindow.ResizeObserver((entries) => {\n        entries.forEach((entry) => {\n          const run = () => {\n            this._measureElement(entry.target as TItemElement, entry)\n          }\n          this.options.useAnimationFrameWithResizeObserver\n            ? requestAnimationFrame(run)\n            : run()\n        })\n      }))\n    }\n\n    return {\n      disconnect: () => {\n        get()?.disconnect()\n        _ro = null\n      },\n      observe: (target: Element) =>\n        get()?.observe(target, { box: 'border-box' }),\n      unobserve: (target: Element) => get()?.unobserve(target),\n    }\n  })()\n  range: { startIndex: number; endIndex: number } | null = null\n\n  constructor(opts: VirtualizerOptions<TScrollElement, TItemElement>) {\n    this.setOptions(opts)\n  }\n\n  setOptions = (opts: VirtualizerOptions<TScrollElement, TItemElement>) => {\n    Object.entries(opts).forEach(([key, value]) => {\n      if (typeof value === 'undefined') delete (opts as any)[key]\n    })\n\n    this.options = {\n      debug: false,\n      initialOffset: 0,\n      overscan: 1,\n      paddingStart: 0,\n      paddingEnd: 0,\n      scrollPaddingStart: 0,\n      scrollPaddingEnd: 0,\n      horizontal: false,\n      getItemKey: defaultKeyExtractor,\n      rangeExtractor: defaultRangeExtractor,\n      onChange: () => {},\n      measureElement,\n      initialRect: { width: 0, height: 0 },\n      scrollMargin: 0,\n      gap: 0,\n      indexAttribute: 'data-index',\n      initialMeasurementsCache: [],\n      lanes: 1,\n      isScrollingResetDelay: 150,\n      enabled: true,\n      isRtl: false,\n      useScrollendEvent: false,\n      useAnimationFrameWithResizeObserver: false,\n      ...opts,\n    }\n  }\n\n  private notify = (sync: boolean) => {\n    this.options.onChange?.(this, sync)\n  }\n\n  private maybeNotify = memo(\n    () => {\n      this.calculateRange()\n\n      return [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ]\n    },\n    (isScrolling) => {\n      this.notify(isScrolling)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'maybeNotify',\n      debug: () => this.options.debug,\n      initialDeps: [\n        this.isScrolling,\n        this.range ? this.range.startIndex : null,\n        this.range ? this.range.endIndex : null,\n      ] as [boolean, number | null, number | null],\n    },\n  )\n\n  private cleanup = () => {\n    this.unsubs.filter(Boolean).forEach((d) => d!())\n    this.unsubs = []\n    this.observer.disconnect()\n    this.scrollElement = null\n    this.targetWindow = null\n  }\n\n  _didMount = () => {\n    return () => {\n      this.cleanup()\n    }\n  }\n\n  _willUpdate = () => {\n    const scrollElement = this.options.enabled\n      ? this.options.getScrollElement()\n      : null\n\n    if (this.scrollElement !== scrollElement) {\n      this.cleanup()\n\n      if (!scrollElement) {\n        this.maybeNotify()\n        return\n      }\n\n      this.scrollElement = scrollElement\n\n      if (this.scrollElement && 'ownerDocument' in this.scrollElement) {\n        this.targetWindow = this.scrollElement.ownerDocument.defaultView\n      } else {\n        this.targetWindow = this.scrollElement?.window ?? null\n      }\n\n      this.elementsCache.forEach((cached) => {\n        this.observer.observe(cached)\n      })\n\n      this._scrollToOffset(this.getScrollOffset(), {\n        adjustments: undefined,\n        behavior: undefined,\n      })\n\n      this.unsubs.push(\n        this.options.observeElementRect(this, (rect) => {\n          this.scrollRect = rect\n          this.maybeNotify()\n        }),\n      )\n\n      this.unsubs.push(\n        this.options.observeElementOffset(this, (offset, isScrolling) => {\n          this.scrollAdjustments = 0\n          this.scrollDirection = isScrolling\n            ? this.getScrollOffset() < offset\n              ? 'forward'\n              : 'backward'\n            : null\n          this.scrollOffset = offset\n          this.isScrolling = isScrolling\n\n          this.maybeNotify()\n        }),\n      )\n    }\n  }\n\n  private getSize = () => {\n    if (!this.options.enabled) {\n      this.scrollRect = null\n      return 0\n    }\n\n    this.scrollRect = this.scrollRect ?? this.options.initialRect\n\n    return this.scrollRect[this.options.horizontal ? 'width' : 'height']\n  }\n\n  private getScrollOffset = () => {\n    if (!this.options.enabled) {\n      this.scrollOffset = null\n      return 0\n    }\n\n    this.scrollOffset =\n      this.scrollOffset ??\n      (typeof this.options.initialOffset === 'function'\n        ? this.options.initialOffset()\n        : this.options.initialOffset)\n\n    return this.scrollOffset\n  }\n\n  private getFurthestMeasurement = (\n    measurements: Array<VirtualItem>,\n    index: number,\n  ) => {\n    const furthestMeasurementsFound = new Map<number, true>()\n    const furthestMeasurements = new Map<number, VirtualItem>()\n    for (let m = index - 1; m >= 0; m--) {\n      const measurement = measurements[m]!\n\n      if (furthestMeasurementsFound.has(measurement.lane)) {\n        continue\n      }\n\n      const previousFurthestMeasurement = furthestMeasurements.get(\n        measurement.lane,\n      )\n      if (\n        previousFurthestMeasurement == null ||\n        measurement.end > previousFurthestMeasurement.end\n      ) {\n        furthestMeasurements.set(measurement.lane, measurement)\n      } else if (measurement.end < previousFurthestMeasurement.end) {\n        furthestMeasurementsFound.set(measurement.lane, true)\n      }\n\n      if (furthestMeasurementsFound.size === this.options.lanes) {\n        break\n      }\n    }\n\n    return furthestMeasurements.size === this.options.lanes\n      ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n          if (a.end === b.end) {\n            return a.index - b.index\n          }\n\n          return a.end - b.end\n        })[0]\n      : undefined\n  }\n\n  private getMeasurementOptions = memo(\n    () => [\n      this.options.count,\n      this.options.paddingStart,\n      this.options.scrollMargin,\n      this.options.getItemKey,\n      this.options.enabled,\n    ],\n    (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n      this.pendingMeasuredCacheIndexes = []\n      return {\n        count,\n        paddingStart,\n        scrollMargin,\n        getItemKey,\n        enabled,\n      }\n    },\n    {\n      key: false,\n    },\n  )\n\n  private getMeasurements = memo(\n    () => [this.getMeasurementOptions(), this.itemSizeCache],\n    (\n      { count, paddingStart, scrollMargin, getItemKey, enabled },\n      itemSizeCache,\n    ) => {\n      if (!enabled) {\n        this.measurementsCache = []\n        this.itemSizeCache.clear()\n        return []\n      }\n\n      if (this.measurementsCache.length === 0) {\n        this.measurementsCache = this.options.initialMeasurementsCache\n        this.measurementsCache.forEach((item) => {\n          this.itemSizeCache.set(item.key, item.size)\n        })\n      }\n\n      const min =\n        this.pendingMeasuredCacheIndexes.length > 0\n          ? Math.min(...this.pendingMeasuredCacheIndexes)\n          : 0\n      this.pendingMeasuredCacheIndexes = []\n\n      const measurements = this.measurementsCache.slice(0, min)\n\n      for (let i = min; i < count; i++) {\n        const key = getItemKey(i)\n\n        const furthestMeasurement =\n          this.options.lanes === 1\n            ? measurements[i - 1]\n            : this.getFurthestMeasurement(measurements, i)\n\n        const start = furthestMeasurement\n          ? furthestMeasurement.end + this.options.gap\n          : paddingStart + scrollMargin\n\n        const measuredSize = itemSizeCache.get(key)\n        const size =\n          typeof measuredSize === 'number'\n            ? measuredSize\n            : this.options.estimateSize(i)\n\n        const end = start + size\n\n        const lane = furthestMeasurement\n          ? furthestMeasurement.lane\n          : i % this.options.lanes\n\n        measurements[i] = {\n          index: i,\n          start,\n          size,\n          end,\n          key,\n          lane,\n        }\n      }\n\n      this.measurementsCache = measurements\n\n      return measurements\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getMeasurements',\n      debug: () => this.options.debug,\n    },\n  )\n\n  calculateRange = memo(\n    () => [\n      this.getMeasurements(),\n      this.getSize(),\n      this.getScrollOffset(),\n      this.options.lanes,\n    ],\n    (measurements, outerSize, scrollOffset, lanes) => {\n      return (this.range =\n        measurements.length > 0 && outerSize > 0\n          ? calculateRange({\n              measurements,\n              outerSize,\n              scrollOffset,\n              lanes,\n            })\n          : null)\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'calculateRange',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualIndexes = memo(\n    () => {\n      let startIndex: number | null = null\n      let endIndex: number | null = null\n      const range = this.calculateRange()\n      if (range) {\n        startIndex = range.startIndex\n        endIndex = range.endIndex\n      }\n      this.maybeNotify.updateDeps([this.isScrolling, startIndex, endIndex])\n      return [\n        this.options.rangeExtractor,\n        this.options.overscan,\n        this.options.count,\n        startIndex,\n        endIndex,\n      ]\n    },\n    (rangeExtractor, overscan, count, startIndex, endIndex) => {\n      return startIndex === null || endIndex === null\n        ? []\n        : rangeExtractor({\n            startIndex,\n            endIndex,\n            overscan,\n            count,\n          })\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualIndexes',\n      debug: () => this.options.debug,\n    },\n  )\n\n  indexFromElement = (node: TItemElement) => {\n    const attributeName = this.options.indexAttribute\n    const indexStr = node.getAttribute(attributeName)\n\n    if (!indexStr) {\n      console.warn(\n        `Missing attribute name '${attributeName}={index}' on measured element.`,\n      )\n      return -1\n    }\n\n    return parseInt(indexStr, 10)\n  }\n\n  private _measureElement = (\n    node: TItemElement,\n    entry: ResizeObserverEntry | undefined,\n  ) => {\n    const index = this.indexFromElement(node)\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const key = item.key\n    const prevNode = this.elementsCache.get(key)\n\n    if (prevNode !== node) {\n      if (prevNode) {\n        this.observer.unobserve(prevNode)\n      }\n      this.observer.observe(node)\n      this.elementsCache.set(key, node)\n    }\n\n    if (node.isConnected) {\n      this.resizeItem(index, this.options.measureElement(node, entry, this))\n    }\n  }\n\n  resizeItem = (index: number, size: number) => {\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return\n    }\n    const itemSize = this.itemSizeCache.get(item.key) ?? item.size\n    const delta = size - itemSize\n\n    if (delta !== 0) {\n      if (\n        this.shouldAdjustScrollPositionOnItemSizeChange !== undefined\n          ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this)\n          : item.start < this.getScrollOffset() + this.scrollAdjustments\n      ) {\n        if (process.env.NODE_ENV !== 'production' && this.options.debug) {\n          console.info('correction', delta)\n        }\n\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: (this.scrollAdjustments += delta),\n          behavior: undefined,\n        })\n      }\n\n      this.pendingMeasuredCacheIndexes.push(item.index)\n      this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size))\n\n      this.notify(false)\n    }\n  }\n\n  measureElement = (node: TItemElement | null | undefined) => {\n    if (!node) {\n      this.elementsCache.forEach((cached, key) => {\n        if (!cached.isConnected) {\n          this.observer.unobserve(cached)\n          this.elementsCache.delete(key)\n        }\n      })\n      return\n    }\n\n    this._measureElement(node, undefined)\n  }\n\n  getVirtualItems = memo(\n    () => [this.getVirtualIndexes(), this.getMeasurements()],\n    (indexes, measurements) => {\n      const virtualItems: Array<VirtualItem> = []\n\n      for (let k = 0, len = indexes.length; k < len; k++) {\n        const i = indexes[k]!\n        const measurement = measurements[i]!\n\n        virtualItems.push(measurement)\n      }\n\n      return virtualItems\n    },\n    {\n      key: process.env.NODE_ENV !== 'production' && 'getVirtualItems',\n      debug: () => this.options.debug,\n    },\n  )\n\n  getVirtualItemForOffset = (offset: number) => {\n    const measurements = this.getMeasurements()\n    if (measurements.length === 0) {\n      return undefined\n    }\n    return notUndefined(\n      measurements[\n        findNearestBinarySearch(\n          0,\n          measurements.length - 1,\n          (index: number) => notUndefined(measurements[index]).start,\n          offset,\n        )\n      ],\n    )\n  }\n\n  getOffsetForAlignment = (\n    toOffset: number,\n    align: ScrollAlignment,\n    itemSize = 0,\n  ) => {\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      align = toOffset >= scrollOffset + size ? 'end' : 'start'\n    }\n\n    if (align === 'center') {\n      // When aligning to a particular item (e.g. with scrollToIndex),\n      // adjust offset by the size of the item to center on the item\n      toOffset += (itemSize - size) / 2\n    } else if (align === 'end') {\n      toOffset -= size\n    }\n\n    const maxOffset = this.getTotalSize() - size\n\n    return Math.max(Math.min(maxOffset, toOffset), 0)\n  }\n\n  getOffsetForIndex = (index: number, align: ScrollAlignment = 'auto') => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    const item = this.measurementsCache[index]\n    if (!item) {\n      return undefined\n    }\n\n    const size = this.getSize()\n    const scrollOffset = this.getScrollOffset()\n\n    if (align === 'auto') {\n      if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {\n        align = 'end'\n      } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {\n        align = 'start'\n      } else {\n        return [scrollOffset, align] as const\n      }\n    }\n\n    const toOffset =\n      align === 'end'\n        ? item.end + this.options.scrollPaddingEnd\n        : item.start - this.options.scrollPaddingStart\n\n    return [\n      this.getOffsetForAlignment(toOffset, align, item.size),\n      align,\n    ] as const\n  }\n\n  private isDynamicMode = () => this.elementsCache.size > 0\n\n  private cancelScrollToIndex = () => {\n    if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {\n      this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId)\n      this.scrollToIndexTimeoutId = null\n    }\n  }\n\n  scrollToOffset = (\n    toOffset: number,\n    { align = 'start', behavior }: ScrollToOffsetOptions = {},\n  ) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  scrollToIndex = (\n    index: number,\n    { align: initialAlign = 'auto', behavior }: ScrollToIndexOptions = {},\n  ) => {\n    index = Math.max(0, Math.min(index, this.options.count - 1))\n\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    const offsetAndAlign = this.getOffsetForIndex(index, initialAlign)\n    if (!offsetAndAlign) return\n\n    const [offset, align] = offsetAndAlign\n\n    this._scrollToOffset(offset, { adjustments: undefined, behavior })\n\n    if (behavior !== 'smooth' && this.isDynamicMode() && this.targetWindow) {\n      this.scrollToIndexTimeoutId = this.targetWindow.setTimeout(() => {\n        this.scrollToIndexTimeoutId = null\n\n        const elementInDOM = this.elementsCache.has(\n          this.options.getItemKey(index),\n        )\n\n        if (elementInDOM) {\n          const result = this.getOffsetForIndex(index, align)\n          if (!result) return\n          const [latestOffset] = result\n\n          const currentScrollOffset = this.getScrollOffset()\n          if (!approxEqual(latestOffset, currentScrollOffset)) {\n            this.scrollToIndex(index, { align, behavior })\n          }\n        } else {\n          this.scrollToIndex(index, { align, behavior })\n        }\n      })\n    }\n  }\n\n  scrollBy = (delta: number, { behavior }: ScrollToOffsetOptions = {}) => {\n    this.cancelScrollToIndex()\n\n    if (behavior === 'smooth' && this.isDynamicMode()) {\n      console.warn(\n        'The `smooth` scroll behavior is not fully supported with dynamic size.',\n      )\n    }\n\n    this._scrollToOffset(this.getScrollOffset() + delta, {\n      adjustments: undefined,\n      behavior,\n    })\n  }\n\n  getTotalSize = () => {\n    const measurements = this.getMeasurements()\n\n    let end: number\n    // If there are no measurements, set the end to paddingStart\n    // If there is only one lane, use the last measurement's end\n    // Otherwise find the maximum end value among all measurements\n    if (measurements.length === 0) {\n      end = this.options.paddingStart\n    } else if (this.options.lanes === 1) {\n      end = measurements[measurements.length - 1]?.end ?? 0\n    } else {\n      const endByLane = Array<number | null>(this.options.lanes).fill(null)\n      let endIndex = measurements.length - 1\n      while (endIndex >= 0 && endByLane.some((val) => val === null)) {\n        const item = measurements[endIndex]!\n        if (endByLane[item.lane] === null) {\n          endByLane[item.lane] = item.end\n        }\n\n        endIndex--\n      }\n\n      end = Math.max(...endByLane.filter((val): val is number => val !== null))\n    }\n\n    return Math.max(\n      end - this.options.scrollMargin + this.options.paddingEnd,\n      0,\n    )\n  }\n\n  private _scrollToOffset = (\n    offset: number,\n    {\n      adjustments,\n      behavior,\n    }: {\n      adjustments: number | undefined\n      behavior: ScrollBehavior | undefined\n    },\n  ) => {\n    this.options.scrollToFn(offset, { behavior, adjustments }, this)\n  }\n\n  measure = () => {\n    this.itemSizeCache = new Map()\n    this.notify(false)\n  }\n}\n\nconst findNearestBinarySearch = (\n  low: number,\n  high: number,\n  getCurrentValue: (i: number) => number,\n  value: number,\n) => {\n  while (low <= high) {\n    const middle = ((low + high) / 2) | 0\n    const currentValue = getCurrentValue(middle)\n\n    if (currentValue < value) {\n      low = middle + 1\n    } else if (currentValue > value) {\n      high = middle - 1\n    } else {\n      return middle\n    }\n  }\n\n  if (low > 0) {\n    return low - 1\n  } else {\n    return 0\n  }\n}\n\nfunction calculateRange({\n  measurements,\n  outerSize,\n  scrollOffset,\n  lanes,\n}: {\n  measurements: Array<VirtualItem>\n  outerSize: number\n  scrollOffset: number\n  lanes: number\n}) {\n  const lastIndex = measurements.length - 1\n  const getOffset = (index: number) => measurements[index]!.start\n\n  // handle case when item count is less than or equal to lanes\n  if (measurements.length <= lanes) {\n    return {\n      startIndex: 0,\n      endIndex: lastIndex,\n    }\n  }\n\n  let startIndex = findNearestBinarySearch(\n    0,\n    lastIndex,\n    getOffset,\n    scrollOffset,\n  )\n  let endIndex = startIndex\n\n  if (lanes === 1) {\n    while (\n      endIndex < lastIndex &&\n      measurements[endIndex]!.end < scrollOffset + outerSize\n    ) {\n      endIndex++\n    }\n  } else if (lanes > 1) {\n    // Expand forward until we include the visible items from all lanes\n    // which are closer to the end of the virtualizer window\n    const endPerLane = Array(lanes).fill(0)\n    while (\n      endIndex < lastIndex &&\n      endPerLane.some((pos) => pos < scrollOffset + outerSize)\n    ) {\n      const item = measurements[endIndex]!\n      endPerLane[item.lane] = item.end\n      endIndex++\n    }\n\n    // Expand backward until we include all lanes' visible items\n    // closer to the top\n    const startPerLane = Array(lanes).fill(scrollOffset + outerSize)\n    while (startIndex >= 0 && startPerLane.some((pos) => pos >= scrollOffset)) {\n      const item = measurements[startIndex]!\n      startPerLane[item.lane] = item.start\n      startIndex--\n    }\n\n    // Align startIndex to the beginning of its lane\n    startIndex = Math.max(0, startIndex - (startIndex % lanes))\n    // Align endIndex to the end of its lane\n    endIndex = Math.min(lastIndex, endIndex + (lanes - 1 - (endIndex % lanes)))\n  }\n\n  return { startIndex, endIndex }\n}\n"], "mappings": ";AA8CA,MAAMA,OAAA,GAAWC,OAAA,IAA+B;EACxC;IAAEC,WAAA;IAAaC;EAAA,IAAiBF,OAAA;EACtC,OAAO;IAAEG,KAAA,EAAOF,WAAA;IAAaG,MAAA,EAAQF;EAAa;AACpD;AAEa,MAAAG,mBAAA,GAAuBC,KAAA,IAAkBA,KAAA;AAEzC,MAAAC,qBAAA,GAAyBC,KAAA,IAAiB;EACrD,MAAMC,KAAA,GAAQC,IAAA,CAAKC,GAAA,CAAIH,KAAA,CAAMI,UAAA,GAAaJ,KAAA,CAAMK,QAAA,EAAU,CAAC;EACrD,MAAAC,GAAA,GAAMJ,IAAA,CAAKK,GAAA,CAAIP,KAAA,CAAMQ,QAAA,GAAWR,KAAA,CAAMK,QAAA,EAAUL,KAAA,CAAMS,KAAA,GAAQ,CAAC;EAErE,MAAMC,GAAA,GAAM,EAAC;EAEb,SAASC,CAAA,GAAIV,KAAA,EAAOU,CAAA,IAAKL,GAAA,EAAKK,CAAA,IAAK;IACjCD,GAAA,CAAIE,IAAA,CAAKD,CAAC;EAAA;EAGL,OAAAD,GAAA;AACT;AAEa,MAAAG,kBAAA,GAAqBA,CAChCC,QAAA,EACAC,EAAA,KACG;EACH,MAAMvB,OAAA,GAAUsB,QAAA,CAASE,aAAA;EACzB,IAAI,CAACxB,OAAA,EAAS;IACZ;EAAA;EAEF,MAAMyB,YAAA,GAAeH,QAAA,CAASG,YAAA;EAC9B,IAAI,CAACA,YAAA,EAAc;IACjB;EAAA;EAGI,MAAAC,OAAA,GAAWC,IAAA,IAAe;IACxB;MAAExB,KAAA;MAAOC;IAAA,IAAWuB,IAAA;IACvBJ,EAAA;MAAEpB,KAAA,EAAOO,IAAA,CAAKkB,KAAA,CAAMzB,KAAK;MAAGC,MAAA,EAAQM,IAAA,CAAKkB,KAAA,CAAMxB,MAAM;IAAA,CAAG;EAC7D;EAEQsB,OAAA,CAAA3B,OAAA,CAAQC,OAAiC,CAAC;EAE9C,KAACyB,YAAA,CAAaI,cAAA,EAAgB;IAChC,OAAO,MAAM,CAAC;EAAA;EAGhB,MAAMC,QAAA,GAAW,IAAIL,YAAA,CAAaI,cAAA,CAAgBE,OAAA,IAAY;IAC5D,MAAMC,GAAA,GAAMA,CAAA,KAAM;MACV,MAAAC,KAAA,GAAQF,OAAA,CAAQ,CAAC;MACvB,IAAIE,KAAA,oBAAAA,KAAA,CAAOC,aAAA,EAAe;QAClB,MAAAC,GAAA,GAAMF,KAAA,CAAMC,aAAA,CAAc,CAAC;QACjC,IAAIC,GAAA,EAAK;UACPT,OAAA,CAAQ;YAAEvB,KAAA,EAAOgC,GAAA,CAAIC,UAAA;YAAYhC,MAAA,EAAQ+B,GAAA,CAAIE;UAAA,CAAW;UACxD;QAAA;MACF;MAEMX,OAAA,CAAA3B,OAAA,CAAQC,OAAiC,CAAC;IACpD;IAEAsB,QAAA,CAASgB,OAAA,CAAQC,mCAAA,GACbC,qBAAA,CAAsBR,GAAG,IACzBA,GAAA,CAAI;EAAA,CACT;EAEDF,QAAA,CAASW,OAAA,CAAQzC,OAAA,EAAS;IAAEmC,GAAA,EAAK;EAAA,CAAc;EAE/C,OAAO,MAAM;IACXL,QAAA,CAASY,SAAA,CAAU1C,OAAO;EAC5B;AACF;AAEA,MAAM2C,uBAAA,GAA0B;EAC9BC,OAAA,EAAS;AACX;AAEa,MAAAC,iBAAA,GAAoBA,CAC/BvB,QAAA,EACAC,EAAA,KACG;EACH,MAAMvB,OAAA,GAAUsB,QAAA,CAASE,aAAA;EACzB,IAAI,CAACxB,OAAA,EAAS;IACZ;EAAA;EAGF,MAAM0B,OAAA,GAAUA,CAAA,KAAM;IACpBH,EAAA,CAAG;MAAEpB,KAAA,EAAOH,OAAA,CAAQ8C,UAAA;MAAY1C,MAAA,EAAQJ,OAAA,CAAQ+C;IAAA,CAAa;EAC/D;EACQrB,OAAA;EAEA1B,OAAA,CAAAgD,gBAAA,CAAiB,UAAUtB,OAAA,EAASiB,uBAAuB;EAEnE,OAAO,MAAM;IACH3C,OAAA,CAAAiD,mBAAA,CAAoB,UAAUvB,OAAO;EAC/C;AACF;AAEA,MAAMwB,iBAAA,GACJ,OAAOC,MAAA,IAAU,cAAc,OAAO,iBAAiBA,MAAA;AAI5C,MAAAC,oBAAA,GAAuBA,CAClC9B,QAAA,EACAC,EAAA,KACG;EACH,MAAMvB,OAAA,GAAUsB,QAAA,CAASE,aAAA;EACzB,IAAI,CAACxB,OAAA,EAAS;IACZ;EAAA;EAEF,MAAMyB,YAAA,GAAeH,QAAA,CAASG,YAAA;EAC9B,IAAI,CAACA,YAAA,EAAc;IACjB;EAAA;EAGF,IAAI4B,MAAA,GAAS;EACb,MAAMC,QAAA,GACJhC,QAAA,CAASgB,OAAA,CAAQiB,iBAAA,IAAqBL,iBAAA,GAClC,MAAM,SACNM,QAAA,CACE/B,YAAA,EACA,MAAM;IACJF,EAAA,CAAG8B,MAAA,EAAQ,KAAK;EAClB,GACA/B,QAAA,CAASgB,OAAA,CAAQmB,qBACnB;EAEA,MAAAC,aAAA,GAAiBC,WAAA,IAAyB,MAAM;IACpD,MAAM;MAAEC,UAAA;MAAYC;IAAM,IAAIvC,QAAA,CAASgB,OAAA;IAC9Be,MAAA,GAAAO,UAAA,GACL5D,OAAA,CAAQ,YAAY,KAAM6D,KAAA,IAAS,MAAO,KAC1C7D,OAAA,CAAQ,WAAW;IACdsD,QAAA;IACT/B,EAAA,CAAG8B,MAAA,EAAQM,WAAW;EACxB;EACM,MAAAjC,OAAA,GAAUgC,aAAA,CAAc,IAAI;EAC5B,MAAAI,UAAA,GAAaJ,aAAA,CAAc,KAAK;EAC3BI,UAAA;EAEH9D,OAAA,CAAAgD,gBAAA,CAAiB,UAAUtB,OAAA,EAASiB,uBAAuB;EAC7D,MAAAoB,sBAAA,GACJzC,QAAA,CAASgB,OAAA,CAAQiB,iBAAA,IAAqBL,iBAAA;EACxC,IAAIa,sBAAA,EAAwB;IAClB/D,OAAA,CAAAgD,gBAAA,CAAiB,aAAac,UAAA,EAAYnB,uBAAuB;EAAA;EAE3E,OAAO,MAAM;IACH3C,OAAA,CAAAiD,mBAAA,CAAoB,UAAUvB,OAAO;IAC7C,IAAIqC,sBAAA,EAAwB;MAClB/D,OAAA,CAAAiD,mBAAA,CAAoB,aAAaa,UAAU;IAAA;EAEvD;AACF;AAEa,MAAAE,mBAAA,GAAsBA,CACjC1C,QAAA,EACAC,EAAA,KACG;EACH,MAAMvB,OAAA,GAAUsB,QAAA,CAASE,aAAA;EACzB,IAAI,CAACxB,OAAA,EAAS;IACZ;EAAA;EAEF,MAAMyB,YAAA,GAAeH,QAAA,CAASG,YAAA;EAC9B,IAAI,CAACA,YAAA,EAAc;IACjB;EAAA;EAGF,IAAI4B,MAAA,GAAS;EACb,MAAMC,QAAA,GACJhC,QAAA,CAASgB,OAAA,CAAQiB,iBAAA,IAAqBL,iBAAA,GAClC,MAAM,SACNM,QAAA,CACE/B,YAAA,EACA,MAAM;IACJF,EAAA,CAAG8B,MAAA,EAAQ,KAAK;EAClB,GACA/B,QAAA,CAASgB,OAAA,CAAQmB,qBACnB;EAEA,MAAAC,aAAA,GAAiBC,WAAA,IAAyB,MAAM;IACpDN,MAAA,GAASrD,OAAA,CAAQsB,QAAA,CAASgB,OAAA,CAAQsB,UAAA,GAAa,YAAY,SAAS;IAC3DN,QAAA;IACT/B,EAAA,CAAG8B,MAAA,EAAQM,WAAW;EACxB;EACM,MAAAjC,OAAA,GAAUgC,aAAA,CAAc,IAAI;EAC5B,MAAAI,UAAA,GAAaJ,aAAA,CAAc,KAAK;EAC3BI,UAAA;EAEH9D,OAAA,CAAAgD,gBAAA,CAAiB,UAAUtB,OAAA,EAASiB,uBAAuB;EAC7D,MAAAoB,sBAAA,GACJzC,QAAA,CAASgB,OAAA,CAAQiB,iBAAA,IAAqBL,iBAAA;EACxC,IAAIa,sBAAA,EAAwB;IAClB/D,OAAA,CAAAgD,gBAAA,CAAiB,aAAac,UAAA,EAAYnB,uBAAuB;EAAA;EAE3E,OAAO,MAAM;IACH3C,OAAA,CAAAiD,mBAAA,CAAoB,UAAUvB,OAAO;IAC7C,IAAIqC,sBAAA,EAAwB;MAClB/D,OAAA,CAAAiD,mBAAA,CAAoB,aAAaa,UAAU;IAAA;EAEvD;AACF;AAEO,MAAMG,cAAA,GAAiBA,CAC5BjE,OAAA,EACAiC,KAAA,EACAX,QAAA,KACG;EACH,IAAIW,KAAA,oBAAAA,KAAA,CAAOC,aAAA,EAAe;IAClB,MAAAC,GAAA,GAAMF,KAAA,CAAMC,aAAA,CAAc,CAAC;IACjC,IAAIC,GAAA,EAAK;MACP,MAAM+B,IAAA,GAAOxD,IAAA,CAAKkB,KAAA,CAChBO,GAAA,CAAIb,QAAA,CAASgB,OAAA,CAAQsB,UAAA,GAAa,eAAe,WAAW,CAC9D;MACO,OAAAM,IAAA;IAAA;EACT;EAGF,OAAQlE,OAAA,CACNsB,QAAA,CAASgB,OAAA,CAAQsB,UAAA,GAAa,gBAAgB,cAChD;AACF;AAEa,MAAAO,YAAA,GAAeA,CAC1Bd,MAAA,EACA;EACEe,WAAA,GAAc;EACdC;AACF,GACA/C,QAAA,KACG;;EACH,MAAMgD,QAAA,GAAWjB,MAAA,GAASe,WAAA;EAE1B,CAAAG,EAAA,IAAAC,EAAA,GAAAlD,QAAA,CAASE,aAAA,KAAT,gBAAAgD,EAAA,CAAwBC,QAAA,KAAxB,gBAAAF,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAmC;IACjC,CAAClD,QAAA,CAASgB,OAAA,CAAQsB,UAAA,GAAa,SAAS,KAAK,GAAGU,QAAA;IAChDD;EAAA;AAEJ;AAEa,MAAAM,aAAA,GAAgBA,CAC3BtB,MAAA,EACA;EACEe,WAAA,GAAc;EACdC;AACF,GACA/C,QAAA,KACG;;EACH,MAAMgD,QAAA,GAAWjB,MAAA,GAASe,WAAA;EAE1B,CAAAG,EAAA,IAAAC,EAAA,GAAAlD,QAAA,CAASE,aAAA,KAAT,gBAAAgD,EAAA,CAAwBC,QAAA,KAAxB,gBAAAF,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAmC;IACjC,CAAClD,QAAA,CAASgB,OAAA,CAAQsB,UAAA,GAAa,SAAS,KAAK,GAAGU,QAAA;IAChDD;EAAA;AAEJ;AA0DO,MAAMO,WAAA,CAGX;EA0DAC,YAAYC,IAAA,EAAwD;IAzDpE,KAAQC,MAAA,GAAqC,EAAC;IAEP,KAAAvD,aAAA;IACa,KAAAC,YAAA;IACtC,KAAAkC,WAAA;IACd,KAAQqB,sBAAA,GAAwC;IAChD,KAAAC,iBAAA,GAAwC,EAAC;IACjC,KAAAC,aAAA,sBAAoBC,GAAA,CAAiB;IAC7C,KAAQC,2BAAA,GAA6C,EAAC;IAC5B,KAAAC,UAAA;IACI,KAAAC,YAAA;IACY,KAAAC,eAAA;IAC1C,KAAQC,iBAAA,GAAoB;IAQ5B,KAAAC,aAAA,sBAAoBN,GAAA,CAAuB;IAC3C,KAAQrD,QAAA,GAAkB;MACxB,IAAI4D,GAAA,GAA6B;MAEjC,MAAMC,GAAA,GAAMA,CAAA,KAAM;QAChB,IAAID,GAAA,EAAK;UACA,OAAAA,GAAA;QAAA;QAGT,IAAI,CAAC,KAAKjE,YAAA,IAAgB,CAAC,KAAKA,YAAA,CAAaI,cAAA,EAAgB;UACpD;QAAA;QAGT,OAAQ6D,GAAA,GAAM,IAAI,KAAKjE,YAAA,CAAaI,cAAA,CAAgBE,OAAA,IAAY;UACtDA,OAAA,CAAA6D,OAAA,CAAS3D,KAAA,IAAU;YACzB,MAAMD,GAAA,GAAMA,CAAA,KAAM;cACX,KAAA6D,eAAA,CAAgB5D,KAAA,CAAM6D,MAAA,EAAwB7D,KAAK;YAC1D;YACA,KAAKK,OAAA,CAAQC,mCAAA,GACTC,qBAAA,CAAsBR,GAAG,IACzBA,GAAA,CAAI;UAAA,CACT;QAAA,CACF;MACH;MAEO;QACL+D,UAAA,EAAYA,CAAA,KAAM;;UAChB,CAAAvB,EAAA,GAAAmB,GAAA,uBAAAnB,EAAA,CAAOuB,UAAA;UACDL,GAAA;QACR;QACAjD,OAAA,EAAUqD,MAAA;;UACR,QAAAtB,EAAA,GAAAmB,GAAA,CAAI,MAAJ,gBAAAnB,EAAA,CAAO/B,OAAA,CAAQqD,MAAA,EAAQ;YAAE3D,GAAA,EAAK;UAAA;;QAChCO,SAAA,EAAYoD,MAAA;;UAAoB,QAAAtB,EAAA,GAAAmB,GAAA,CAAI,MAAJ,gBAAAnB,EAAA,CAAO9B,SAAA,CAAUoD,MAAA;QAAA;MACnD;IAAA,GACC;IACsD,KAAAtF,KAAA;IAMzD,KAAAwF,UAAA,GAAcC,KAAA,IAA2D;MAChEC,MAAA,CAAAnE,OAAA,CAAQkE,KAAI,EAAEL,OAAA,CAAQ,CAAC,CAACO,GAAA,EAAKC,KAAK,MAAM;QAC7C,IAAI,OAAOA,KAAA,KAAU,aAAa,OAAQH,KAAA,CAAaE,GAAG;MAAA,CAC3D;MAED,KAAK7D,OAAA,GAAU;QACb+D,KAAA,EAAO;QACPC,aAAA,EAAe;QACfzF,QAAA,EAAU;QACV0F,YAAA,EAAc;QACdC,UAAA,EAAY;QACZC,kBAAA,EAAoB;QACpBC,gBAAA,EAAkB;QAClB9C,UAAA,EAAY;QACZ+C,UAAA,EAAYtG,mBAAA;QACZuG,cAAA,EAAgBrG,qBAAA;QAChBsG,QAAA,EAAUA,CAAA,KAAM,CAAC;QACjB5C,cAAA;QACA6C,WAAA,EAAa;UAAE3G,KAAA,EAAO;UAAGC,MAAA,EAAQ;QAAE;QACnC2G,YAAA,EAAc;QACdC,GAAA,EAAK;QACLC,cAAA,EAAgB;QAChBC,wBAAA,EAA0B,EAAC;QAC3BC,KAAA,EAAO;QACP1D,qBAAA,EAAuB;QACvB2D,OAAA,EAAS;QACTvD,KAAA,EAAO;QACPN,iBAAA,EAAmB;QACnBhB,mCAAA,EAAqC;QACrC,GAAG0D;MACL;IACF;IAEQ,KAAAoB,MAAA,GAAUC,IAAA,IAAkB;;MAC7B,CAAA/C,EAAA,IAAAC,EAAA,QAAAlC,OAAA,EAAQuE,QAAA,KAAR,gBAAAtC,EAAA,CAAAG,IAAA,CAAAF,EAAA,EAAmB,MAAM8C,IAAA;IAChC;IAEA,KAAQC,WAAA,GAAcC,IAAA,CACpB,MAAM;MACJ,KAAKC,cAAA,CAAe;MAEb,QACL,KAAK9D,WAAA,EACL,KAAKnD,KAAA,GAAQ,KAAKA,KAAA,CAAMI,UAAA,GAAa,MACrC,KAAKJ,KAAA,GAAQ,KAAKA,KAAA,CAAMQ,QAAA,GAAW,KACrC;IACF,GACC2C,WAAA,IAAgB;MACf,KAAK0D,MAAA,CAAO1D,WAAW;IACzB,GACA;MACEwC,GAAA,EAAKuB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB;MAC9CvB,KAAA,EAAOA,CAAA,KAAM,KAAK/D,OAAA,CAAQ+D,KAAA;MAC1BwB,WAAA,EAAa,CACX,KAAKlE,WAAA,EACL,KAAKnD,KAAA,GAAQ,KAAKA,KAAA,CAAMI,UAAA,GAAa,MACrC,KAAKJ,KAAA,GAAQ,KAAKA,KAAA,CAAMQ,QAAA,GAAW;IACrC,CAEJ;IAEA,KAAQ8G,OAAA,GAAU,MAAM;MACjB,KAAA/C,MAAA,CAAOgD,MAAA,CAAOC,OAAO,EAAEpC,OAAA,CAASqC,CAAA,IAAMA,CAAA,EAAI;MAC/C,KAAKlD,MAAA,GAAS,EAAC;MACf,KAAKjD,QAAA,CAASiE,UAAA,CAAW;MACzB,KAAKvE,aAAA,GAAgB;MACrB,KAAKC,YAAA,GAAe;IACtB;IAEA,KAAAyG,SAAA,GAAY,MAAM;MAChB,OAAO,MAAM;QACX,KAAKJ,OAAA,CAAQ;MACf;IACF;IAEA,KAAAK,WAAA,GAAc,MAAM;;MAClB,MAAM3G,aAAA,GAAgB,KAAKc,OAAA,CAAQ8E,OAAA,GAC/B,KAAK9E,OAAA,CAAQ8F,gBAAA,KACb;MAEA,SAAK5G,aAAA,KAAkBA,aAAA,EAAe;QACxC,KAAKsG,OAAA,CAAQ;QAEb,IAAI,CAACtG,aAAA,EAAe;UAClB,KAAK+F,WAAA,CAAY;UACjB;QAAA;QAGF,KAAK/F,aAAA,GAAgBA,aAAA;QAErB,IAAI,KAAKA,aAAA,IAAiB,mBAAmB,KAAKA,aAAA,EAAe;UAC1D,KAAAC,YAAA,GAAe,KAAKD,aAAA,CAAc6G,aAAA,CAAcC,WAAA;QAAA,OAChD;UACA,KAAA7G,YAAA,KAAe+C,EAAA,QAAKhD,aAAA,KAAL,gBAAAgD,EAAA,CAAoBrB,MAAA,KAAU;QAAA;QAG/C,KAAAsC,aAAA,CAAcG,OAAA,CAAS2C,MAAA,IAAW;UAChC,KAAAzG,QAAA,CAASW,OAAA,CAAQ8F,MAAM;QAAA,CAC7B;QAEI,KAAAC,eAAA,CAAgB,KAAKC,eAAA,IAAmB;UAC3CrE,WAAA,EAAa;UACbC,QAAA,EAAU;QAAA,CACX;QAED,KAAKU,MAAA,CAAO3D,IAAA,CACV,KAAKkB,OAAA,CAAQjB,kBAAA,CAAmB,MAAOM,IAAA,IAAS;UAC9C,KAAK0D,UAAA,GAAa1D,IAAA;UAClB,KAAK4F,WAAA,CAAY;QAClB,EACH;QAEA,KAAKxC,MAAA,CAAO3D,IAAA,CACV,KAAKkB,OAAA,CAAQc,oBAAA,CAAqB,MAAM,CAACC,MAAA,EAAQM,WAAA,KAAgB;UAC/D,KAAK6B,iBAAA,GAAoB;UACzB,KAAKD,eAAA,GAAkB5B,WAAA,GACnB,KAAK8E,eAAA,KAAoBpF,MAAA,GACvB,YACA,aACF;UACJ,KAAKiC,YAAA,GAAejC,MAAA;UACpB,KAAKM,WAAA,GAAcA,WAAA;UAEnB,KAAK4D,WAAA,CAAY;QAClB,EACH;MAAA;IAEJ;IAEA,KAAQmB,OAAA,GAAU,MAAM;MAClB,KAAC,KAAKpG,OAAA,CAAQ8E,OAAA,EAAS;QACzB,KAAK/B,UAAA,GAAa;QACX;MAAA;MAGT,KAAKA,UAAA,GAAa,KAAKA,UAAA,IAAc,KAAK/C,OAAA,CAAQwE,WAAA;MAElD,OAAO,KAAKzB,UAAA,CAAW,KAAK/C,OAAA,CAAQsB,UAAA,GAAa,UAAU,QAAQ;IACrE;IAEA,KAAQ6E,eAAA,GAAkB,MAAM;MAC1B,KAAC,KAAKnG,OAAA,CAAQ8E,OAAA,EAAS;QACzB,KAAK9B,YAAA,GAAe;QACb;MAAA;MAGT,KAAKA,YAAA,GACH,KAAKA,YAAA,KACJ,OAAO,KAAKhD,OAAA,CAAQgE,aAAA,KAAkB,aACnC,KAAKhE,OAAA,CAAQgE,aAAA,CAAc,IAC3B,KAAKhE,OAAA,CAAQgE,aAAA;MAEnB,OAAO,KAAKhB,YAAA;IACd;IAEQ,KAAAqD,sBAAA,GAAyB,CAC/BC,YAAA,EACAtI,KAAA,KACG;MACG,MAAAuI,yBAAA,sBAAgC1D,GAAA,CAAkB;MAClD,MAAA2D,oBAAA,sBAA2B3D,GAAA,CAAyB;MAC1D,SAAS4D,CAAA,GAAIzI,KAAA,GAAQ,GAAGyI,CAAA,IAAK,GAAGA,CAAA,IAAK;QAC7B,MAAAC,WAAA,GAAcJ,YAAA,CAAaG,CAAC;QAElC,IAAIF,yBAAA,CAA0BI,GAAA,CAAID,WAAA,CAAYE,IAAI,GAAG;UACnD;QAAA;QAGF,MAAMC,2BAAA,GAA8BL,oBAAA,CAAqBnD,GAAA,CACvDqD,WAAA,CAAYE,IACd;QACA,IACEC,2BAAA,IAA+B,QAC/BH,WAAA,CAAYlI,GAAA,GAAMqI,2BAAA,CAA4BrI,GAAA,EAC9C;UACqBgI,oBAAA,CAAAM,GAAA,CAAIJ,WAAA,CAAYE,IAAA,EAAMF,WAAW;QAC7C,WAAAA,WAAA,CAAYlI,GAAA,GAAMqI,2BAAA,CAA4BrI,GAAA,EAAK;UAClC+H,yBAAA,CAAAO,GAAA,CAAIJ,WAAA,CAAYE,IAAA,EAAM,IAAI;QAAA;QAGtD,IAAIL,yBAAA,CAA0B3E,IAAA,KAAS,KAAK5B,OAAA,CAAQ6E,KAAA,EAAO;UACzD;QAAA;MACF;MAGF,OAAO2B,oBAAA,CAAqB5E,IAAA,KAAS,KAAK5B,OAAA,CAAQ6E,KAAA,GAC9CkC,KAAA,CAAMC,IAAA,CAAKR,oBAAA,CAAqBS,MAAA,EAAQ,EAAEC,IAAA,CAAK,CAACC,CAAA,EAAGC,CAAA,KAAM;QACnD,IAAAD,CAAA,CAAE3I,GAAA,KAAQ4I,CAAA,CAAE5I,GAAA,EAAK;UACZ,OAAA2I,CAAA,CAAEnJ,KAAA,GAAQoJ,CAAA,CAAEpJ,KAAA;QAAA;QAGd,OAAAmJ,CAAA,CAAE3I,GAAA,GAAM4I,CAAA,CAAE5I,GAAA;MAAA,CAClB,EAAE,CAAC,IACJ;IACN;IAEA,KAAQ6I,qBAAA,GAAwBnC,IAAA,CAC9B,MAAM,CACJ,KAAKlF,OAAA,CAAQrB,KAAA,EACb,KAAKqB,OAAA,CAAQiE,YAAA,EACb,KAAKjE,OAAA,CAAQyE,YAAA,EACb,KAAKzE,OAAA,CAAQqE,UAAA,EACb,KAAKrE,OAAA,CAAQ8E,OAAA,CACf,EACA,CAACnG,KAAA,EAAOsF,YAAA,EAAcQ,YAAA,EAAcJ,UAAA,EAAYS,OAAA,KAAY;MAC1D,KAAKhC,2BAAA,GAA8B,EAAC;MAC7B;QACLnE,KAAA;QACAsF,YAAA;QACAQ,YAAA;QACAJ,UAAA;QACAS;MACF;IACF,GACA;MACEjB,GAAA,EAAK;IAAA,CAET;IAEA,KAAQyD,eAAA,GAAkBpC,IAAA,CACxB,MAAM,CAAC,KAAKmC,qBAAA,IAAyB,KAAKzE,aAAa,GACvD,CACE;MAAEjE,KAAA;MAAOsF,YAAA;MAAcQ,YAAA;MAAcJ,UAAA;MAAYS;IAAA,GACjDlC,aAAA,KACG;MACH,IAAI,CAACkC,OAAA,EAAS;QACZ,KAAKnC,iBAAA,GAAoB,EAAC;QAC1B,KAAKC,aAAA,CAAc2E,KAAA,CAAM;QACzB,OAAO,EAAC;MAAA;MAGN,SAAK5E,iBAAA,CAAkB6E,MAAA,KAAW,GAAG;QAClC,KAAA7E,iBAAA,GAAoB,KAAK3C,OAAA,CAAQ4E,wBAAA;QACjC,KAAAjC,iBAAA,CAAkBW,OAAA,CAASmE,IAAA,IAAS;UACvC,KAAK7E,aAAA,CAAckE,GAAA,CAAIW,IAAA,CAAK5D,GAAA,EAAK4D,IAAA,CAAK7F,IAAI;QAAA,CAC3C;MAAA;MAGG,MAAAnD,GAAA,GACJ,KAAKqE,2BAAA,CAA4B0E,MAAA,GAAS,IACtCpJ,IAAA,CAAKK,GAAA,CAAI,GAAG,KAAKqE,2BAA2B,IAC5C;MACN,KAAKA,2BAAA,GAA8B,EAAC;MAEpC,MAAMwD,YAAA,GAAe,KAAK3D,iBAAA,CAAkB+E,KAAA,CAAM,GAAGjJ,GAAG;MAExD,SAASI,CAAA,GAAIJ,GAAA,EAAKI,CAAA,GAAIF,KAAA,EAAOE,CAAA,IAAK;QAC1B,MAAAgF,GAAA,GAAMQ,UAAA,CAAWxF,CAAC;QAExB,MAAM8I,mBAAA,GACJ,KAAK3H,OAAA,CAAQ6E,KAAA,KAAU,IACnByB,YAAA,CAAazH,CAAA,GAAI,CAAC,IAClB,KAAKwH,sBAAA,CAAuBC,YAAA,EAAczH,CAAC;QAEjD,MAAMV,KAAA,GAAQwJ,mBAAA,GACVA,mBAAA,CAAoBnJ,GAAA,GAAM,KAAKwB,OAAA,CAAQ0E,GAAA,GACvCT,YAAA,GAAeQ,YAAA;QAEb,MAAAmD,YAAA,GAAehF,aAAA,CAAcS,GAAA,CAAIQ,GAAG;QACpC,MAAAjC,IAAA,GACJ,OAAOgG,YAAA,KAAiB,WACpBA,YAAA,GACA,KAAK5H,OAAA,CAAQ6H,YAAA,CAAahJ,CAAC;QAEjC,MAAML,GAAA,GAAML,KAAA,GAAQyD,IAAA;QAEpB,MAAMgF,IAAA,GAAOe,mBAAA,GACTA,mBAAA,CAAoBf,IAAA,GACpB/H,CAAA,GAAI,KAAKmB,OAAA,CAAQ6E,KAAA;QAErByB,YAAA,CAAazH,CAAC,IAAI;UAChBb,KAAA,EAAOa,CAAA;UACPV,KAAA;UACAyD,IAAA;UACApD,GAAA;UACAqF,GAAA;UACA+C;QACF;MAAA;MAGF,KAAKjE,iBAAA,GAAoB2D,YAAA;MAElB,OAAAA,YAAA;IACT,GACA;MACEzC,GAAA,EAAKuB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB;MAC9CvB,KAAA,EAAOA,CAAA,KAAM,KAAK/D,OAAA,CAAQ+D;IAAA,CAE9B;IAEiB,KAAAoB,cAAA,GAAAD,IAAA,CACf,MAAM,CACJ,KAAKoC,eAAA,CAAgB,GACrB,KAAKlB,OAAA,CAAQ,GACb,KAAKD,eAAA,CAAgB,GACrB,KAAKnG,OAAA,CAAQ6E,KAAA,CACf,EACA,CAACyB,YAAA,EAAcwB,SAAA,EAAW9E,YAAA,EAAc6B,KAAA,KAAU;MAChD,OAAQ,KAAK3G,KAAA,GACXoI,YAAA,CAAakB,MAAA,GAAS,KAAKM,SAAA,GAAY,IACnC3C,cAAA,CAAe;QACbmB,YAAA;QACAwB,SAAA;QACA9E,YAAA;QACA6B;MACD,KACD;IACR,GACA;MACEhB,GAAA,EAAKuB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB;MAC9CvB,KAAA,EAAOA,CAAA,KAAM,KAAK/D,OAAA,CAAQ+D;IAAA,CAE9B;IAEoB,KAAAgE,iBAAA,GAAA7C,IAAA,CAClB,MAAM;MACJ,IAAI5G,UAAA,GAA4B;MAChC,IAAII,QAAA,GAA0B;MACxB,MAAAR,KAAA,GAAQ,KAAKiH,cAAA,CAAe;MAClC,IAAIjH,KAAA,EAAO;QACTI,UAAA,GAAaJ,KAAA,CAAMI,UAAA;QACnBI,QAAA,GAAWR,KAAA,CAAMQ,QAAA;MAAA;MAEnB,KAAKuG,WAAA,CAAY+C,UAAA,CAAW,CAAC,KAAK3G,WAAA,EAAa/C,UAAA,EAAYI,QAAQ,CAAC;MAC7D,QACL,KAAKsB,OAAA,CAAQsE,cAAA,EACb,KAAKtE,OAAA,CAAQzB,QAAA,EACb,KAAKyB,OAAA,CAAQrB,KAAA,EACbL,UAAA,EACAI,QAAA,CACF;IACF,GACA,CAAC4F,cAAA,EAAgB/F,QAAA,EAAUI,KAAA,EAAOL,UAAA,EAAYI,QAAA,KAAa;MACzD,OAAOJ,UAAA,KAAe,QAAQI,QAAA,KAAa,OACvC,KACA4F,cAAA,CAAe;QACbhG,UAAA;QACAI,QAAA;QACAH,QAAA;QACAI;MAAA,CACD;IACP,GACA;MACEkF,GAAA,EAAKuB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB;MAC9CvB,KAAA,EAAOA,CAAA,KAAM,KAAK/D,OAAA,CAAQ+D;IAAA,CAE9B;IAEA,KAAAkE,gBAAA,GAAoBC,IAAA,IAAuB;MACnC,MAAAC,aAAA,GAAgB,KAAKnI,OAAA,CAAQ2E,cAAA;MAC7B,MAAAyD,QAAA,GAAWF,IAAA,CAAKG,YAAA,CAAaF,aAAa;MAEhD,IAAI,CAACC,QAAA,EAAU;QACLE,OAAA,CAAAC,IAAA,CACN,2BAA2BJ,aAAa,gCAC1C;QACO;MAAA;MAGF,OAAAK,QAAA,CAASJ,QAAA,EAAU,EAAE;IAC9B;IAEQ,KAAA7E,eAAA,GAAkB,CACxB2E,IAAA,EACAvI,KAAA,KACG;MACG,MAAA3B,KAAA,GAAQ,KAAKiK,gBAAA,CAAiBC,IAAI;MAClC,MAAAT,IAAA,GAAO,KAAK9E,iBAAA,CAAkB3E,KAAK;MACzC,IAAI,CAACyJ,IAAA,EAAM;QACT;MAAA;MAEF,MAAM5D,GAAA,GAAM4D,IAAA,CAAK5D,GAAA;MACjB,MAAM4E,QAAA,GAAW,KAAKtF,aAAA,CAAcE,GAAA,CAAIQ,GAAG;MAE3C,IAAI4E,QAAA,KAAaP,IAAA,EAAM;QACrB,IAAIO,QAAA,EAAU;UACP,KAAAjJ,QAAA,CAASY,SAAA,CAAUqI,QAAQ;QAAA;QAE7B,KAAAjJ,QAAA,CAASW,OAAA,CAAQ+H,IAAI;QACrB,KAAA/E,aAAA,CAAc2D,GAAA,CAAIjD,GAAA,EAAKqE,IAAI;MAAA;MAGlC,IAAIA,IAAA,CAAKQ,WAAA,EAAa;QACf,KAAAC,UAAA,CAAW3K,KAAA,EAAO,KAAKgC,OAAA,CAAQ2B,cAAA,CAAeuG,IAAA,EAAMvI,KAAA,EAAO,IAAI,CAAC;MAAA;IAEzE;IAEa,KAAAgJ,UAAA,IAAC3K,KAAA,EAAe4D,IAAA,KAAiB;MACtC,MAAA6F,IAAA,GAAO,KAAK9E,iBAAA,CAAkB3E,KAAK;MACzC,IAAI,CAACyJ,IAAA,EAAM;QACT;MAAA;MAEF,MAAMmB,QAAA,GAAW,KAAKhG,aAAA,CAAcS,GAAA,CAAIoE,IAAA,CAAK5D,GAAG,KAAK4D,IAAA,CAAK7F,IAAA;MAC1D,MAAMiH,KAAA,GAAQjH,IAAA,GAAOgH,QAAA;MAErB,IAAIC,KAAA,KAAU,GAAG;QACf,IACE,KAAKC,0CAAA,KAA+C,SAChD,KAAKA,0CAAA,CAA2CrB,IAAA,EAAMoB,KAAA,EAAO,IAAI,IACjEpB,IAAA,CAAKtJ,KAAA,GAAQ,KAAKgI,eAAA,CAAgB,IAAI,KAAKjD,iBAAA,EAC/C;UACA,IAAIkC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB,KAAKtF,OAAA,CAAQ+D,KAAA,EAAO;YACvDuE,OAAA,CAAAS,IAAA,CAAK,cAAcF,KAAK;UAAA;UAG7B,KAAA3C,eAAA,CAAgB,KAAKC,eAAA,IAAmB;YAC3CrE,WAAA,EAAc,KAAKoB,iBAAA,IAAqB2F,KAAA;YACxC9G,QAAA,EAAU;UAAA,CACX;QAAA;QAGE,KAAAe,2BAAA,CAA4BhE,IAAA,CAAK2I,IAAA,CAAKzJ,KAAK;QAC3C,KAAA4E,aAAA,GAAgB,IAAIC,GAAA,CAAI,KAAKD,aAAA,CAAckE,GAAA,CAAIW,IAAA,CAAK5D,GAAA,EAAKjC,IAAI,CAAC;QAEnE,KAAKmD,MAAA,CAAO,KAAK;MAAA;IAErB;IAEA,KAAApD,cAAA,GAAkBuG,IAAA,IAA0C;MAC1D,IAAI,CAACA,IAAA,EAAM;QACT,KAAK/E,aAAA,CAAcG,OAAA,CAAQ,CAAC2C,MAAA,EAAQpC,GAAA,KAAQ;UACtC,KAACoC,MAAA,CAAOyC,WAAA,EAAa;YAClB,KAAAlJ,QAAA,CAASY,SAAA,CAAU6F,MAAM;YACzB,KAAA9C,aAAA,CAAc6F,MAAA,CAAOnF,GAAG;UAAA;QAC/B,CACD;QACD;MAAA;MAGG,KAAAN,eAAA,CAAgB2E,IAAA,EAAM,MAAS;IACtC;IAEkB,KAAAe,eAAA,GAAA/D,IAAA,CAChB,MAAM,CAAC,KAAK6C,iBAAA,CAAqB,QAAKT,eAAA,EAAiB,GACvD,CAAC4B,OAAA,EAAS5C,YAAA,KAAiB;MACzB,MAAM6C,YAAA,GAAmC,EAAC;MAE1C,SAASC,CAAA,GAAI,GAAGC,GAAA,GAAMH,OAAA,CAAQ1B,MAAA,EAAQ4B,CAAA,GAAIC,GAAA,EAAKD,CAAA,IAAK;QAC5C,MAAAvK,CAAA,GAAIqK,OAAA,CAAQE,CAAC;QACb,MAAA1C,WAAA,GAAcJ,YAAA,CAAazH,CAAC;QAElCsK,YAAA,CAAarK,IAAA,CAAK4H,WAAW;MAAA;MAGxB,OAAAyC,YAAA;IACT,GACA;MACEtF,GAAA,EAAKuB,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,gBAAgB;MAC9CvB,KAAA,EAAOA,CAAA,KAAM,KAAK/D,OAAA,CAAQ+D;IAAA,CAE9B;IAEA,KAAAuF,uBAAA,GAA2BvI,MAAA,IAAmB;MACtC,MAAAuF,YAAA,GAAe,KAAKgB,eAAA,CAAgB;MACtC,IAAAhB,YAAA,CAAakB,MAAA,KAAW,GAAG;QACtB;MAAA;MAEF,OAAA+B,YAAA,CACLjD,YAAA,CACEkD,uBAAA,CACE,GACAlD,YAAA,CAAakB,MAAA,GAAS,GACrBxJ,KAAA,IAAkBuL,YAAA,CAAajD,YAAA,CAAatI,KAAK,CAAC,EAAEG,KAAA,EACrD4C,MAEJ,EACF;IACF;IAEA,KAAA0I,qBAAA,GAAwB,CACtBzH,QAAA,EACA0H,KAAA,EACAd,QAAA,GAAW,MACR;MACG,MAAAhH,IAAA,GAAO,KAAKwE,OAAA,CAAQ;MACpB,MAAApD,YAAA,GAAe,KAAKmD,eAAA,CAAgB;MAE1C,IAAIuD,KAAA,KAAU,QAAQ;QACZA,KAAA,GAAA1H,QAAA,IAAYgB,YAAA,GAAepB,IAAA,GAAO,QAAQ;MAAA;MAGpD,IAAI8H,KAAA,KAAU,UAAU;QAGtB1H,QAAA,KAAa4G,QAAA,GAAWhH,IAAA,IAAQ;MAAA,WACvB8H,KAAA,KAAU,OAAO;QACd1H,QAAA,IAAAJ,IAAA;MAAA;MAGR,MAAA+H,SAAA,GAAY,KAAKC,YAAA,KAAiBhI,IAAA;MAExC,OAAOxD,IAAA,CAAKC,GAAA,CAAID,IAAA,CAAKK,GAAA,CAAIkL,SAAA,EAAW3H,QAAQ,GAAG,CAAC;IAClD;IAEoB,KAAA6H,iBAAA,IAAC7L,KAAA,EAAe0L,KAAA,GAAyB,WAAW;MAC9D1L,KAAA,GAAAI,IAAA,CAAKC,GAAA,CAAI,GAAGD,IAAA,CAAKK,GAAA,CAAIT,KAAA,EAAO,KAAKgC,OAAA,CAAQrB,KAAA,GAAQ,CAAC,CAAC;MAErD,MAAA8I,IAAA,GAAO,KAAK9E,iBAAA,CAAkB3E,KAAK;MACzC,IAAI,CAACyJ,IAAA,EAAM;QACF;MAAA;MAGH,MAAA7F,IAAA,GAAO,KAAKwE,OAAA,CAAQ;MACpB,MAAApD,YAAA,GAAe,KAAKmD,eAAA,CAAgB;MAE1C,IAAIuD,KAAA,KAAU,QAAQ;QACpB,IAAIjC,IAAA,CAAKjJ,GAAA,IAAOwE,YAAA,GAAepB,IAAA,GAAO,KAAK5B,OAAA,CAAQoE,gBAAA,EAAkB;UAC3DsF,KAAA;QAAA,WACCjC,IAAA,CAAKtJ,KAAA,IAAS6E,YAAA,GAAe,KAAKhD,OAAA,CAAQmE,kBAAA,EAAoB;UAC/DuF,KAAA;QAAA,OACH;UACE,QAAC1G,YAAA,EAAc0G,KAAK;QAAA;MAC7B;MAGI,MAAA1H,QAAA,GACJ0H,KAAA,KAAU,QACNjC,IAAA,CAAKjJ,GAAA,GAAM,KAAKwB,OAAA,CAAQoE,gBAAA,GACxBqD,IAAA,CAAKtJ,KAAA,GAAQ,KAAK6B,OAAA,CAAQmE,kBAAA;MAEzB,QACL,KAAKsF,qBAAA,CAAsBzH,QAAA,EAAU0H,KAAA,EAAOjC,IAAA,CAAK7F,IAAI,GACrD8H,KAAA,CACF;IACF;IAEA,KAAQI,aAAA,GAAgB,MAAM,KAAK3G,aAAA,CAAcvB,IAAA,GAAO;IAExD,KAAQmI,mBAAA,GAAsB,MAAM;MAClC,IAAI,KAAKrH,sBAAA,KAA2B,QAAQ,KAAKvD,YAAA,EAAc;QACxD,KAAAA,YAAA,CAAa6K,YAAA,CAAa,KAAKtH,sBAAsB;QAC1D,KAAKA,sBAAA,GAAyB;MAAA;IAElC;IAEiB,KAAAuH,cAAA,IACfjI,QAAA,EACA;MAAE0H,KAAA,GAAQ;MAAS3H;IAAS,IAA2B,OACpD;MACH,KAAKgI,mBAAA,CAAoB;MAEzB,IAAIhI,QAAA,KAAa,YAAY,KAAK+H,aAAA,IAAiB;QACzCxB,OAAA,CAAAC,IAAA,CACN,wEACF;MAAA;MAGF,KAAKrC,eAAA,CAAgB,KAAKuD,qBAAA,CAAsBzH,QAAA,EAAU0H,KAAK,GAAG;QAChE5H,WAAA,EAAa;QACbC;MAAA,CACD;IACH;IAEgB,KAAAmI,aAAA,IACdlM,KAAA,EACA;MAAE0L,KAAA,EAAOS,YAAA,GAAe;MAAQpI;IAAmC,WAChE;MACK/D,KAAA,GAAAI,IAAA,CAAKC,GAAA,CAAI,GAAGD,IAAA,CAAKK,GAAA,CAAIT,KAAA,EAAO,KAAKgC,OAAA,CAAQrB,KAAA,GAAQ,CAAC,CAAC;MAE3D,KAAKoL,mBAAA,CAAoB;MAEzB,IAAIhI,QAAA,KAAa,YAAY,KAAK+H,aAAA,IAAiB;QACzCxB,OAAA,CAAAC,IAAA,CACN,wEACF;MAAA;MAGF,MAAM6B,cAAA,GAAiB,KAAKP,iBAAA,CAAkB7L,KAAA,EAAOmM,YAAY;MACjE,IAAI,CAACC,cAAA,EAAgB;MAEf,OAACrJ,MAAA,EAAQ2I,KAAK,IAAIU,cAAA;MAExB,KAAKlE,eAAA,CAAgBnF,MAAA,EAAQ;QAAEe,WAAA,EAAa;QAAWC;MAAA,CAAU;MAEjE,IAAIA,QAAA,KAAa,YAAY,KAAK+H,aAAA,CAAc,KAAK,KAAK3K,YAAA,EAAc;QACtE,KAAKuD,sBAAA,GAAyB,KAAKvD,YAAA,CAAakL,UAAA,CAAW,MAAM;UAC/D,KAAK3H,sBAAA,GAAyB;UAExB,MAAA4H,YAAA,GAAe,KAAKnH,aAAA,CAAcwD,GAAA,CACtC,KAAK3G,OAAA,CAAQqE,UAAA,CAAWrG,KAAK,CAC/B;UAEA,IAAIsM,YAAA,EAAc;YAChB,MAAMC,MAAA,GAAS,KAAKV,iBAAA,CAAkB7L,KAAA,EAAO0L,KAAK;YAClD,IAAI,CAACa,MAAA,EAAQ;YACP,OAACC,YAAY,IAAID,MAAA;YAEjB,MAAAE,mBAAA,GAAsB,KAAKtE,eAAA,CAAgB;YACjD,IAAI,CAACuE,WAAA,CAAYF,YAAA,EAAcC,mBAAmB,GAAG;cACnD,KAAKP,aAAA,CAAclM,KAAA,EAAO;gBAAE0L,KAAA;gBAAO3H;cAAA,CAAU;YAAA;UAC/C,OACK;YACL,KAAKmI,aAAA,CAAclM,KAAA,EAAO;cAAE0L,KAAA;cAAO3H;YAAA,CAAU;UAAA;QAC/C,CACD;MAAA;IAEL;IAEA,KAAA4I,QAAA,GAAW,CAAC9B,KAAA,EAAe;MAAE9G;IAAS,IAA2B,OAAO;MACtE,KAAKgI,mBAAA,CAAoB;MAEzB,IAAIhI,QAAA,KAAa,YAAY,KAAK+H,aAAA,IAAiB;QACzCxB,OAAA,CAAAC,IAAA,CACN,wEACF;MAAA;MAGF,KAAKrC,eAAA,CAAgB,KAAKC,eAAA,CAAgB,IAAI0C,KAAA,EAAO;QACnD/G,WAAA,EAAa;QACbC;MAAA,CACD;IACH;IAEA,KAAA6H,YAAA,GAAe,MAAM;;MACb,MAAAtD,YAAA,GAAe,KAAKgB,eAAA,CAAgB;MAEtC,IAAA9I,GAAA;MAIA,IAAA8H,YAAA,CAAakB,MAAA,KAAW,GAAG;QAC7BhJ,GAAA,GAAM,KAAKwB,OAAA,CAAQiE,YAAA;MACV,gBAAKjE,OAAA,CAAQ6E,KAAA,KAAU,GAAG;QACnCrG,GAAA,KAAM0D,EAAA,GAAAoE,YAAA,CAAaA,YAAA,CAAakB,MAAA,GAAS,CAAC,MAApC,gBAAAtF,EAAA,CAAuC1D,GAAA,KAAO;MAAA,OAC/C;QACL,MAAMoM,SAAA,GAAY7D,KAAA,CAAqB,KAAK/G,OAAA,CAAQ6E,KAAK,EAAEgG,IAAA,CAAK,IAAI;QAChE,IAAAnM,QAAA,GAAW4H,YAAA,CAAakB,MAAA,GAAS;QAC9B,OAAA9I,QAAA,IAAY,KAAKkM,SAAA,CAAUE,IAAA,CAAMC,GAAA,IAAQA,GAAA,KAAQ,IAAI,GAAG;UACvD,MAAAtD,IAAA,GAAOnB,YAAA,CAAa5H,QAAQ;UAClC,IAAIkM,SAAA,CAAUnD,IAAA,CAAKb,IAAI,MAAM,MAAM;YACvBgE,SAAA,CAAAnD,IAAA,CAAKb,IAAI,IAAIa,IAAA,CAAKjJ,GAAA;UAAA;UAG9BE,QAAA;QAAA;QAGIF,GAAA,GAAAJ,IAAA,CAAKC,GAAA,CAAI,GAAGuM,SAAA,CAAUnF,MAAA,CAAQsF,GAAA,IAAuBA,GAAA,KAAQ,IAAI,CAAC;MAAA;MAG1E,OAAO3M,IAAA,CAAKC,GAAA,CACVG,GAAA,GAAM,KAAKwB,OAAA,CAAQyE,YAAA,GAAe,KAAKzE,OAAA,CAAQkE,UAAA,EAC/C,CACF;IACF;IAEQ,KAAAgC,eAAA,GAAkB,CACxBnF,MAAA,EACA;MACEe,WAAA;MACAC;IAAA,MAKC;MACH,KAAK/B,OAAA,CAAQgL,UAAA,CAAWjK,MAAA,EAAQ;QAAEgB,QAAA;QAAUD;MAAA,GAAe,IAAI;IACjE;IAEA,KAAAmJ,OAAA,GAAU,MAAM;MACT,KAAArI,aAAA,sBAAoBC,GAAA,CAAI;MAC7B,KAAKkC,MAAA,CAAO,KAAK;IACnB;IAxpBE,KAAKrB,UAAA,CAAWlB,IAAI;EAAA;AAypBxB;AAEA,MAAMgH,uBAAA,GAA0BA,CAC9B0B,GAAA,EACAC,IAAA,EACAC,eAAA,EACAtH,KAAA,KACG;EACH,OAAOoH,GAAA,IAAOC,IAAA,EAAM;IACZ,MAAAE,MAAA,IAAWH,GAAA,GAAMC,IAAA,IAAQ,IAAK;IAC9B,MAAAG,YAAA,GAAeF,eAAA,CAAgBC,MAAM;IAE3C,IAAIC,YAAA,GAAexH,KAAA,EAAO;MACxBoH,GAAA,GAAMG,MAAA,GAAS;IAAA,WACNC,YAAA,GAAexH,KAAA,EAAO;MAC/BqH,IAAA,GAAOE,MAAA,GAAS;IAAA,OACX;MACE,OAAAA,MAAA;IAAA;EACT;EAGF,IAAIH,GAAA,GAAM,GAAG;IACX,OAAOA,GAAA,GAAM;EAAA,OACR;IACE;EAAA;AAEX;AAEA,SAAS/F,eAAe;EACtBmB,YAAA;EACAwB,SAAA;EACA9E,YAAA;EACA6B;AACF,GAKG;EACK,MAAA0G,SAAA,GAAYjF,YAAA,CAAakB,MAAA,GAAS;EACxC,MAAMgE,SAAA,GAAaxN,KAAA,IAAkBsI,YAAA,CAAatI,KAAK,EAAGG,KAAA;EAGtD,IAAAmI,YAAA,CAAakB,MAAA,IAAU3C,KAAA,EAAO;IACzB;MACLvG,UAAA,EAAY;MACZI,QAAA,EAAU6M;IACZ;EAAA;EAGF,IAAIjN,UAAA,GAAakL,uBAAA,CACf,GACA+B,SAAA,EACAC,SAAA,EACAxI,YACF;EACA,IAAItE,QAAA,GAAWJ,UAAA;EAEf,IAAIuG,KAAA,KAAU,GAAG;IACf,OACEnG,QAAA,GAAW6M,SAAA,IACXjF,YAAA,CAAa5H,QAAQ,EAAGF,GAAA,GAAMwE,YAAA,GAAe8E,SAAA,EAC7C;MACApJ,QAAA;IAAA;EACF,WACSmG,KAAA,GAAQ,GAAG;IAGpB,MAAM4G,UAAA,GAAa1E,KAAA,CAAMlC,KAAK,EAAEgG,IAAA,CAAK,CAAC;IAEpC,OAAAnM,QAAA,GAAW6M,SAAA,IACXE,UAAA,CAAWX,IAAA,CAAMY,GAAA,IAAQA,GAAA,GAAM1I,YAAA,GAAe8E,SAAS,GACvD;MACM,MAAAL,IAAA,GAAOnB,YAAA,CAAa5H,QAAQ;MACvB+M,UAAA,CAAAhE,IAAA,CAAKb,IAAI,IAAIa,IAAA,CAAKjJ,GAAA;MAC7BE,QAAA;IAAA;IAKF,MAAMiN,YAAA,GAAe5E,KAAA,CAAMlC,KAAK,EAAEgG,IAAA,CAAK7H,YAAA,GAAe8E,SAAS;IACxD,OAAAxJ,UAAA,IAAc,KAAKqN,YAAA,CAAab,IAAA,CAAMY,GAAA,IAAQA,GAAA,IAAO1I,YAAY,GAAG;MACnE,MAAAyE,IAAA,GAAOnB,YAAA,CAAahI,UAAU;MACvBqN,YAAA,CAAAlE,IAAA,CAAKb,IAAI,IAAIa,IAAA,CAAKtJ,KAAA;MAC/BG,UAAA;IAAA;IAIFA,UAAA,GAAaF,IAAA,CAAKC,GAAA,CAAI,GAAGC,UAAA,GAAcA,UAAA,GAAauG,KAAM;IAE1DnG,QAAA,GAAWN,IAAA,CAAKK,GAAA,CAAI8M,SAAA,EAAW7M,QAAA,IAAYmG,KAAA,GAAQ,IAAKnG,QAAA,GAAWmG,KAAA,CAAO;EAAA;EAGrE;IAAEvG,UAAA;IAAYI;EAAS;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}