{"ast": null, "code": "import { lightFormatters } from \"./_lib/format/lightFormatters.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n// Rexports of internal for libraries to use.\n// See: https://github.com/date-fns/date-fns/issues/3638#issuecomment-1877082874\nexport { lightFormatters };\n\n// This RegExp consists of three parts separated by `|`:\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nconst formattingTokensRegExp = /(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\nconst escapedStringRegExp = /^'([^]*?)'?$/;\nconst doubleQuoteRegExp = /''/g;\nconst unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\n/**\n * @private\n */\n\n/**\n * @name lightFormat\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. Unlike `format`,\n * `lightFormat` doesn't use locales and outputs date using the most popular tokens.\n *\n * > ⚠️ Please note that the `lightFormat` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   |\n * |---------------------------------|---------|-----------------------------------|\n * | AM, PM                          | a..aaa  | AM, PM                            |\n * |                                 | aaaa    | a.m., p.m.                        |\n * |                                 | aaaaa   | a, p                              |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 |\n * |                                 | yy      | 44, 01, 00, 17                    |\n * |                                 | yyy     | 044, 001, 000, 017                |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |\n * |                                 | MM      | 01, 02, ..., 12                   |\n * | Day of month                    | d       | 1, 2, ..., 31                     |\n * |                                 | dd      | 01, 02, ..., 31                   |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |\n * |                                 | hh      | 01, 02, ..., 11, 12               |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |\n * |                                 | HH      | 00, 01, 02, ..., 23               |\n * | Minute                          | m       | 0, 1, ..., 59                     |\n * |                                 | mm      | 00, 01, ..., 59                   |\n * | Second                          | s       | 0, 1, ..., 59                     |\n * |                                 | ss      | 00, 01, ..., 59                   |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |\n * |                                 | SS      | 00, 01, ..., 99                   |\n * |                                 | SSS     | 000, 001, ..., 999                |\n * |                                 | SSSS    | ...                               |\n *\n * @param date - The original date\n * @param format - The string of tokens\n *\n * @returns The formatted date string\n *\n * @throws `Invalid time value` if the date is invalid\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * const result = lightFormat(new Date(2014, 1, 11), 'yyyy-MM-dd')\n * //=> '2014-02-11'\n */\nexport function lightFormat(date, formatStr) {\n  const date_ = toDate(date);\n  if (!isValid(date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n  const tokens = formatStr.match(formattingTokensRegExp);\n\n  // The only case when formattingTokensRegExp doesn't match the string is when it's empty\n  if (!tokens) return \"\";\n  const result = tokens.map(substring => {\n    // Replace two single quote characters with one single quote character\n    if (substring === \"''\") {\n      return \"'\";\n    }\n    const firstCharacter = substring[0];\n    if (firstCharacter === \"'\") {\n      return cleanEscapedString(substring);\n    }\n    const formatter = lightFormatters[firstCharacter];\n    if (formatter) {\n      return formatter(date_, substring);\n    }\n    if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n      throw new RangeError(\"Format string contains an unescaped latin alphabet character `\" + firstCharacter + \"`\");\n    }\n    return substring;\n  }).join(\"\");\n  return result;\n}\nfunction cleanEscapedString(input) {\n  const matches = input.match(escapedStringRegExp);\n  if (!matches) return input;\n  return matches[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n// Fallback for modularized imports:\nexport default lightFormat;", "map": {"version": 3, "names": ["lightFormatters", "<PERSON><PERSON><PERSON><PERSON>", "toDate", "formattingTokensRegExp", "escapedStringRegExp", "doubleQuoteRegExp", "unescapedLatinCharacterRegExp", "lightFormat", "date", "formatStr", "date_", "RangeError", "tokens", "match", "result", "map", "substring", "firstCharacter", "cleanEscapedString", "formatter", "join", "input", "matches", "replace"], "sources": ["E:/code/Resonance-KLE/home-security-dashboard/node_modules/date-fns/lightFormat.js"], "sourcesContent": ["import { lightFormatters } from \"./_lib/format/lightFormatters.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n// Rexports of internal for libraries to use.\n// See: https://github.com/date-fns/date-fns/issues/3638#issuecomment-1877082874\nexport { lightFormatters };\n\n// This RegExp consists of three parts separated by `|`:\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nconst formattingTokensRegExp = /(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\nconst escapedStringRegExp = /^'([^]*?)'?$/;\nconst doubleQuoteRegExp = /''/g;\nconst unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\n/**\n * @private\n */\n\n/**\n * @name lightFormat\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. Unlike `format`,\n * `lightFormat` doesn't use locales and outputs date using the most popular tokens.\n *\n * > ⚠️ Please note that the `lightFormat` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   |\n * |---------------------------------|---------|-----------------------------------|\n * | AM, PM                          | a..aaa  | AM, PM                            |\n * |                                 | aaaa    | a.m., p.m.                        |\n * |                                 | aaaaa   | a, p                              |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 |\n * |                                 | yy      | 44, 01, 00, 17                    |\n * |                                 | yyy     | 044, 001, 000, 017                |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |\n * |                                 | MM      | 01, 02, ..., 12                   |\n * | Day of month                    | d       | 1, 2, ..., 31                     |\n * |                                 | dd      | 01, 02, ..., 31                   |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |\n * |                                 | hh      | 01, 02, ..., 11, 12               |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |\n * |                                 | HH      | 00, 01, 02, ..., 23               |\n * | Minute                          | m       | 0, 1, ..., 59                     |\n * |                                 | mm      | 00, 01, ..., 59                   |\n * | Second                          | s       | 0, 1, ..., 59                     |\n * |                                 | ss      | 00, 01, ..., 59                   |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |\n * |                                 | SS      | 00, 01, ..., 99                   |\n * |                                 | SSS     | 000, 001, ..., 999                |\n * |                                 | SSSS    | ...                               |\n *\n * @param date - The original date\n * @param format - The string of tokens\n *\n * @returns The formatted date string\n *\n * @throws `Invalid time value` if the date is invalid\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * const result = lightFormat(new Date(2014, 1, 11), 'yyyy-MM-dd')\n * //=> '2014-02-11'\n */\nexport function lightFormat(date, formatStr) {\n  const date_ = toDate(date);\n\n  if (!isValid(date_)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  const tokens = formatStr.match(formattingTokensRegExp);\n\n  // The only case when formattingTokensRegExp doesn't match the string is when it's empty\n  if (!tokens) return \"\";\n\n  const result = tokens\n    .map((substring) => {\n      // Replace two single quote characters with one single quote character\n      if (substring === \"''\") {\n        return \"'\";\n      }\n\n      const firstCharacter = substring[0];\n      if (firstCharacter === \"'\") {\n        return cleanEscapedString(substring);\n      }\n\n      const formatter = lightFormatters[firstCharacter];\n      if (formatter) {\n        return formatter(date_, substring);\n      }\n\n      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n        throw new RangeError(\n          \"Format string contains an unescaped latin alphabet character `\" +\n            firstCharacter +\n            \"`\",\n        );\n      }\n\n      return substring;\n    })\n    .join(\"\");\n\n  return result;\n}\n\nfunction cleanEscapedString(input) {\n  const matches = input.match(escapedStringRegExp);\n  if (!matches) return input;\n  return matches[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n// Fallback for modularized imports:\nexport default lightFormat;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,kCAAkC;AAClE,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA,SAASF,eAAe;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,sBAAsB,GAAG,gCAAgC;AAE/D,MAAMC,mBAAmB,GAAG,cAAc;AAC1C,MAAMC,iBAAiB,GAAG,KAAK;AAC/B,MAAMC,6BAA6B,GAAG,UAAU;;AAEhD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,SAAS,EAAE;EAC3C,MAAMC,KAAK,GAAGR,MAAM,CAACM,IAAI,CAAC;EAE1B,IAAI,CAACP,OAAO,CAACS,KAAK,CAAC,EAAE;IACnB,MAAM,IAAIC,UAAU,CAAC,oBAAoB,CAAC;EAC5C;EAEA,MAAMC,MAAM,GAAGH,SAAS,CAACI,KAAK,CAACV,sBAAsB,CAAC;;EAEtD;EACA,IAAI,CAACS,MAAM,EAAE,OAAO,EAAE;EAEtB,MAAME,MAAM,GAAGF,MAAM,CAClBG,GAAG,CAAEC,SAAS,IAAK;IAClB;IACA,IAAIA,SAAS,KAAK,IAAI,EAAE;MACtB,OAAO,GAAG;IACZ;IAEA,MAAMC,cAAc,GAAGD,SAAS,CAAC,CAAC,CAAC;IACnC,IAAIC,cAAc,KAAK,GAAG,EAAE;MAC1B,OAAOC,kBAAkB,CAACF,SAAS,CAAC;IACtC;IAEA,MAAMG,SAAS,GAAGnB,eAAe,CAACiB,cAAc,CAAC;IACjD,IAAIE,SAAS,EAAE;MACb,OAAOA,SAAS,CAACT,KAAK,EAAEM,SAAS,CAAC;IACpC;IAEA,IAAIC,cAAc,CAACJ,KAAK,CAACP,6BAA6B,CAAC,EAAE;MACvD,MAAM,IAAIK,UAAU,CAClB,gEAAgE,GAC9DM,cAAc,GACd,GACJ,CAAC;IACH;IAEA,OAAOD,SAAS;EAClB,CAAC,CAAC,CACDI,IAAI,CAAC,EAAE,CAAC;EAEX,OAAON,MAAM;AACf;AAEA,SAASI,kBAAkBA,CAACG,KAAK,EAAE;EACjC,MAAMC,OAAO,GAAGD,KAAK,CAACR,KAAK,CAACT,mBAAmB,CAAC;EAChD,IAAI,CAACkB,OAAO,EAAE,OAAOD,KAAK;EAC1B,OAAOC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAAClB,iBAAiB,EAAE,GAAG,CAAC;AACnD;;AAEA;AACA,eAAeE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}